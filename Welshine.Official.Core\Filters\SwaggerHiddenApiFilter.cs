﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Welshine.Official.Core.Filters
{
    /// <summary>
    /// Swagger隐藏API的过滤器
    /// </summary>
    public class SwaggerHiddenApiFilter : IDocumentFilter
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="swaggerDoc"></param>
        /// <param name="context"></param>
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            foreach (var apiDescription in context.ApiDescriptions)
            {
                var key = "/" + apiDescription.RelativePath.TrimEnd('/');
                if (key.StartsWith("/api"))
                {
                    swaggerDoc.Paths.Remove(key);
                }
            }
        }
    }
}
