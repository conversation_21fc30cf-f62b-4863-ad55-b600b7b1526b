﻿using DTHY.Core.Repository;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 图册仓储接口
    /// </summary>
    public interface IAtlasRepository : IBaseRepository<Atlas>
    {
        /// <summary>
        /// 判断图册名称是否存在
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <returns></returns>
        Task<bool> ExistsAtlasName(string atlasName);

        /// <summary>
        /// 判断图册名称是否存在
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="atlasId">图册Id</param>
        /// <returns></returns>
        Task<bool> ExistsAtlasName(string atlasName, long atlasId);

        /// <summary>
        /// 添加图册信息
        /// </summary>
        /// <param name="atlas">图册信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        Task<bool> AddAtlas(Atlas atlas, string thumbnail, string fileId);

        /// <summary>
        /// 获取图册信息
        /// </summary>
        /// <param name="atlasId">图册Id</param>
        /// <returns></returns>
        Task<Atlas> GetAtlas(long atlasId);

        /// <summary>
        /// 修改图册信息
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        Task<bool> EditAtlas(Atlas atlas, string thumbnail, string fileId);

        /// <summary>
        /// 删除图册信息
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <returns></returns>
        Task<bool> DeleteAtlas(Atlas atlas);

        /// <summary>
        /// 获取图册列表
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="times">时间筛选</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<AtlasListReponse>> GetAtlasPageList(string atlasName, TimeHorizon times, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取最新图册
        /// </summary>
        /// <param name="openId">用户openId</param>
        /// <returns></returns>
        Task<AtlasDetailResponse> WX_GetAtlas(string openId);
    }
}
