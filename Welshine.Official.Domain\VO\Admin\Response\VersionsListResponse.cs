﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 版本列表条目
    /// </summary>
    public class VersionsListResponse
    {
        /// <summary>
        /// 版本Id
        /// </summary>
        public long VersionsId { get; set; }

        /// <summary>
        /// 版本类型: 0->首页Banner;1->首页中部广告;2->关于惠而信;3->联系方式;4->中文产品介绍;5->首页底部横幅;6->侧边浮层;
        /// </summary>
        public int VersionType { get; set; }

        /// <summary>
        /// 版本标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 版本内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public string RData { get; set; }

        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        public int ReleaseStatus { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedUserName { get; set; }

        /// <summary>
        /// 提交人
        /// </summary>
        public string SubmitUserName { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }

        /// <summary>
        /// 审批人
        /// </summary>
        public string ApproverUserName { get; set; }

        /// <summary>
        /// 审核状态: 0->待提交; 1->待审核; 2->审核通过; 3->审核驳回
        /// </summary>
        public int? ApproverStatus { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime? ApprovalTime { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
}
