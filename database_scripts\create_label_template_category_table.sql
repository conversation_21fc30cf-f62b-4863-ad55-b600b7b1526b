-- 创建标签模版分类表
CREATE TABLE `ext_label_template_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (0:禁用 1:启用)',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `creator_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `modifier_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 (0:未删除 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_category_name` (`category_name`) COMMENT '分类名称索引',
  KEY `idx_sort` (`sort`) COMMENT '排序索引',
  KEY `idx_is_enabled` (`is_enabled`) COMMENT '启用状态索引',
  KEY `idx_created_time` (`created_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签模版分类表';

-- 插入默认分类数据
INSERT INTO `ext_label_template_category` (`category_name`, `category_description`, `sort`, `is_enabled`) VALUES
('通用标签', '通用标签模版分类', 1, 1),
('商品标签', '商品标签模版分类', 2, 1),
('地址标签', '地址标签模版分类', 3, 1),
('价格标签', '价格标签模版分类', 4, 1),
('条码标签', '条码标签模版分类', 5, 1);
