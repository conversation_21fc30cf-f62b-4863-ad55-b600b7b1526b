﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 留言板查询条件条目
    /// </summary>
    public class GetMessageBoardPageListRequest
    {
        /// <summary>
        /// 电话
        /// </summary>
        [StringLength(13, ErrorMessage = "电话长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9_\\-@&=`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "电话格式错误")]
        public string Phone { get; set; }

        /// <summary>
        /// 提交时间筛选
        /// </summary>
        public TimeHorizon CreatedTimeScope { get; set; }
    }
}
