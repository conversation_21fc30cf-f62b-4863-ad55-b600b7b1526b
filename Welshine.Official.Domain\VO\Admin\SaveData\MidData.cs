﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.SaveData
{
    /// <summary>
    /// 首页横幅数据
    /// </summary>
    public class MidData
    {
        /// <summary>
        /// 中文
        /// </summary>
        [Required(ErrorMessage = "中文是必填项")]
        public MidItem CNImg { get; set; }

        /// <summary>
        /// 英文
        /// </summary>
        [Required(ErrorMessage = "英文是必填项")]
        public MidItem ENImg { get; set; }
    }

    public class MidItem
    {
        /// <summary>
        /// PC端图
        /// </summary>
        [Required(ErrorMessage = "PC端图是必填项")]
        [ArrayRequired(ErrorMessage = "PC端图是必填项")]
        [ArrayMaxLength(10, ErrorMessage = "PC端图限制最多5张")]
        public List<SlideShowImage> PC { get; set; }

        /// <summary>
        /// 手机端图
        /// </summary>
        [Required(ErrorMessage = "手机端图是必填项")]
        [ArrayRequired(ErrorMessage = "手机端图是必填项")]
        [ArrayMaxLength(10, ErrorMessage = "手机端图限制最多5张")]
        public List<SlideShowImage> Mobile { get; set; }
    }
}
