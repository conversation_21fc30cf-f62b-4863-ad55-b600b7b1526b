﻿using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 文件关联表
    /// </summary>
    [SqlSugar.SugarTable("cms_files_relation")]
    public class FilesRelation : BaseEntity<string>
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "files_id")]
        public string FilesId { get; set; }

        /// <summary>
        /// 关联Id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "object_id")]
        public long ObjectId { get; set; }

        /// <summary>
        /// 关联类型:
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "table_enum")]
        public EnumRelationType TableEnum { get; set; }
    }
}
