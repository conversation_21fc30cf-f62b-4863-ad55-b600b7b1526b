﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 编辑产品条目
    /// </summary>
    public class EditProductRequest : AddProductRequest
    {
        /// <summary>
        /// 产品Id
        /// </summary>
        [Required(ErrorMessage = "产品Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "产品Id参数错误")]
        public long ProductId { get; set; }
    }
}
