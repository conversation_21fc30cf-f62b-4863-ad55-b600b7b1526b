﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 编辑分类条目
    /// </summary>
    public class EditCategoryRequest : CategoryIdRequest
    {
        /// <summary>
        /// 分类名称(英文)
        /// </summary>
        [Required(ErrorMessage = "分类名称(英文)是必填项")]
        [StringLength(40, ErrorMessage = "分类名称(英文)长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "分类名称(英文)不允许有中文")]
        public string categoryNameEN { get; set; }

        /// <summary>
        /// 分类名称(中文)
        /// </summary>
        [Required(ErrorMessage = "分类名称(中文)是必填项")]
        [StringLength(40, ErrorMessage = "分类名称(中文)长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[\u4e00-\u9fa5]+$", ErrorMessage = "分类名称(中文)只允许有中文")]
        public string categoryNameCN { get; set; }

        /// <summary>
        /// 未激活logo图片
        /// </summary>
        [StringLength(500, ErrorMessage = "未激活logo图片长度不符", MinimumLength = 1)]
        public string logoImgUrl { get; set; }

        /// <summary>
        /// 激活logo图片
        /// </summary>
        [StringLength(500, ErrorMessage = "激活logo图片长度不符", MinimumLength = 1)]
        public string logoImgUrlActivate { get; set; }

        /// <summary>
        /// 手机版激活logo图片
        /// </summary>
        [StringLength(500, ErrorMessage = "手机版激活logo图片长度不符", MinimumLength = 1)]
        public string mobileImgUrlActivate { get; set; }

    }
}
