﻿using Newtonsoft.Json;
using System;

namespace Welshine.Official.Core
{
    /// 
    /// Long类型Json序列化重写
    /// 在js中传输会导致精度丢失，故而在序列化时转换成字符类型
    /// 
    public class JsonConverterLong : JsonConverter
    {
        /// 
        /// 是否可以转换
        /// 
        /// 
        /// 
        public override bool CanConvert(Type objectType)
        {
            return true;
        }

        /// 
        /// 读json
        /// 
        /// 
        /// 
        /// 
        /// 
        /// 
        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if ((reader.ValueType == null || reader.ValueType == typeof(long?)) && reader.Value == null)
            {
                return null;
            }
            else
            {
                //long.TryParse(reader.Value != null ? reader.Value.ToString() : "", out long value);
                return long.Parse(reader.Value != null ? reader.Value.ToString() : "");
            }
            //return reader.Value;
        }

        /// 
        /// 写json
        /// 
        /// 
        /// 
        /// 
        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            if (value == null)
                writer.WriteValue(value);
            else
                writer.WriteValue(value + "");
        }
    }
}
