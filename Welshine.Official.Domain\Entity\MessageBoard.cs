﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 留言区
    /// </summary>
    [SqlSugar.SugarTable("cms_message_board")]
    public class MessageBoard
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "name")]
        public string Name { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "phone")]
        public string Phone { get; set; }

        /// <summary>
        /// 留言信息
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "content")]
        public string Content { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "email")]
        public string Email { get; set; }

        /// <summary>
        /// 地区
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "area")]
        public string Area { get; set; }

        /// <summary>
        /// ip地址
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "ip_address")]
        public string IpAddress { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_time", IsOnlyIgnoreUpdate = true)]
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }
}
