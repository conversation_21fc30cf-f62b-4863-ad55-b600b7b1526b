-- 创建打印机设备查询记录表
CREATE TABLE `ext_printer_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model` varchar(100) NOT NULL COMMENT '打印机型号',
  `sn` varchar(100) NOT NULL COMMENT '设备SN码',
  `first_query_time` datetime DEFAULT NULL COMMENT '首次查询时间',
  `last_query_time` datetime DEFAULT NULL COMMENT '最后查询时间',
  `query_count` int(11) NOT NULL DEFAULT '0' COMMENT '查询次数',
  `device_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '设备状态 (0:不在库 1:在库)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `creator_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `modifier_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 (0:未删除 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_sn` (`model`,`sn`) COMMENT '型号和SN码唯一索引',
  KEY `idx_model` (`model`) COMMENT '型号索引',
  KEY `idx_sn` (`sn`) COMMENT 'SN码索引',
  KEY `idx_device_status` (`device_status`) COMMENT '设备状态索引',
  KEY `idx_created_time` (`created_time`) COMMENT '创建时间索引',
  KEY `idx_last_query_time` (`last_query_time`) COMMENT '最后查询时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='打印机设备表';

-- 插入一些示例数据（可选）
INSERT INTO `ext_printer_device` (`model`, `sn`, `device_status`, `remark`) VALUES
('T50PRO', 'HP001234567890', 1, '测试设备'),
('P2', 'CN987654321012', 1, '测试设备');