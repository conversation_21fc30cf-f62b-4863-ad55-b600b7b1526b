﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.App.Request
{
    /// <summary>
    /// 微信授权手机
    /// </summary>
    public class WxAuthPhoneRequet
    {
        /// <summary>
        /// 动态令牌
        /// </summary>
        [Required(ErrorMessage = "动态令牌是必填项,请完善", AllowEmptyStrings = false)]
        public string Code { get; set; }
        /// <summary>
        /// 用户Id
        /// </summary>
        [Required(ErrorMessage = "OpenIdId是必填项,请完善", AllowEmptyStrings = false)]
        [StringLength(128, ErrorMessage = "OpenId长度不符,请完善", MinimumLength = 1)]
        public string OpenId { get; set; }
    }
}
