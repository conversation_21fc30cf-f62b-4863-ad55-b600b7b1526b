﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Request;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 图册管理
    /// </summary>
    public class AtlasController : BaseApiController
    {
        private readonly IAtlasService _atlasService;

        /// <summary>
        /// 构造器注入
        /// </summary>
        /// <param name="atlasService"></param>
        public AtlasController(IAtlasService atlasService)
        {
            _atlasService = atlasService;
        }

        /// <summary>
        /// 获取最新图册信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<AtlasDetailResponse>> GetAtlas([FromBody] BaseRequest<GetAtlasRequest> request)
        {
            AtlasDetailResponse result = null;
            try
            {
                result = await _atlasService.WX_GetAtlas(request.Body.OpenId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetAtlas Error {u}", ex.Message);
                return Failure<AtlasDetailResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }
    }
}
