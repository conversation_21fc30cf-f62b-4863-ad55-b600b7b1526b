﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加分类产品条目
    /// </summary>
    public class AddENCategoryProductRequest
    {
        /// <summary>
        /// 分类Id
        /// </summary>
        [Required(ErrorMessage = "分类Id是必填项")]
        [Range(0, long.MaxValue, ErrorMessage = "分类Id参数错误")]
        public long? CategoryId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Required(ErrorMessage = "产品名称是必填项")]
        [StringLength(100, ErrorMessage = "产品名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "产品名称不允许有中文")]
        public string ProductName { get; set; }

        ///// <summary>
        ///// 商品材质
        ///// </summary>
        //[Required(ErrorMessage = "商品材质是必填项")]
        //[StringLength(100, ErrorMessage = "商品材质长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "商品材质不允许有中文")]
        //public string ProductTexture { get; set; }

        ///// <summary>
        ///// 商品颜色
        ///// </summary>
        //[Required(ErrorMessage = "商品颜色是必填项")]
        //[StringLength(100, ErrorMessage = "商品颜色长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "商品颜色不允许有中文")]
        //public string ProductColor { get; set; }

        ///// <summary>
        ///// 商品规格
        ///// </summary>
        //[Required(ErrorMessage = "商品规格是必填项")]
        //[StringLength(1000, ErrorMessage = "商品规格长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "商品规格不允许有中文")]
        //public string ProductSpecification { get; set; }

        /// <summary>
        /// 商品图片Id
        /// </summary>
        [Required(ErrorMessage = "商品图片Id是必填项")]
        [ArrayRequired(ErrorMessage = "商品图片Id是必填项")]
        [ArrayMaxLength(20, ErrorMessage = "商品图片Id限制最多20张")]
        public List<string> ProductImgId { get; set; }

        /// <summary>
        /// 商品介绍
        /// </summary>
        //[Required(ErrorMessage = "商品介绍是必填项")]
        [StringLength(2000, ErrorMessage = "商品介绍长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "商品介绍不允许有中文")]
        public string ProductIntroduce { get; set; }

        /// <summary>
        /// 角标图片Id
        /// </summary>
        //[Required(ErrorMessage = "角标图片Id是必填项")]
        //[ArrayRequired(ErrorMessage = "角标图片Id是必填项")]
        [ArrayMaxLength(10, ErrorMessage = "角标图片Id限制最多10张")]
        public List<string> CornerMarkImgId { get; set; }

        /// <summary>
        /// 商品规格
        /// </summary>
        [Required(ErrorMessage = "商品规格是必填项")]
        [ArrayRequired(ErrorMessage = "商品规格是必填项")]
        //[ArrayMaxLength(5, ErrorMessage = "商品规格限制最多5个")]
        public List<ProductIntroduce> IntroduceList { get; set; }

    }

    /// <summary>
    /// 商品规格
    /// </summary>
    public class ProductIntroduce
    {
        /// <summary>
        /// 型号
        /// </summary>
        [Required(ErrorMessage = "型号是必填项")]
        //[StringLength(10, ErrorMessage = "型号长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[a-zA-Z0-9]+$", ErrorMessage = "型号只允许数字英文")]
        public string IntroduceModel { get; set; }

        /// <summary>
        /// 尺寸
        /// </summary>
        [Required(ErrorMessage = "尺寸是必填项")]
        //[StringLength(20, ErrorMessage = "尺寸长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[a-zA-Z0-9*]+$", ErrorMessage = "尺寸只允许数字英文")]
        public string IntroduceSize { get; set; }

        /// <summary>
        /// 容量
        /// </summary>
        //[Required(ErrorMessage = "容量是必填项")]
        //[StringLength(10, ErrorMessage = "容量长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^(0|[1-9][0-9]*)(\.[0-9]{1,2})?[a-zA-Z]*$", ErrorMessage = "容量只允许数字英文")]
        public string IntroduceCapacity { get; set; }

        /// <summary>
        /// 规格图片地址
        /// </summary>
        [Required(ErrorMessage = "规格图片地址是必填项")]
        [StringLength(500, ErrorMessage = "规格图片地址长度不符", MinimumLength = 1)]
        public string imgUrl { get; set; }

    }
}
