﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class CorporateNewDetailResponse
    {
        /// <summary>
        /// 企业资讯id
        /// </summary>
        public long NewId { get; set; }
        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        public int ReleaseStatus { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }
        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdatedBy { get; set;}
        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? CNPublishDate { get; set; }
        /// <summary>
        /// 中文企业资讯
        /// </summary>
        public CorporateNewDetailItem CNItem { get; set; }= new CorporateNewDetailItem();
        /// <summary>
        /// 英文企业资讯
        /// </summary>
        public CorporateNewDetailItem ENItem { get; set; } = new CorporateNewDetailItem();
        /// <summary>
        /// 文章栏目: 0->企业快讯;1->新品上市;
        /// </summary>
        public int NewColumn { get; set; }

        /// <summary>
        /// 热点推荐: 0->不推荐;1->推荐;
        /// </summary>
        public int NewRecommend { get; set; }

        /// <summary>
        /// 上一篇
        /// </summary>
        public CorporateNewTitleInfo UpCorporateNewTitle { get; set; }

        /// <summary>
        /// 下一篇
        /// </summary>
        public CorporateNewTitleInfo NextCorporateNewTitle { get; set; }

    }

    public class CorporateNewDetailItem
    {
        /// <summary>
        /// 文章标题
        /// </summary>
        public string NewTitle { get; set; }
        /// <summary>
        /// 文章内容
        /// </summary>
        public string NewContent { get; set; }
        /// <summary>
        /// 文章大图
        /// </summary>
        public string NewImage { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishDate { get; set; }

        /// <summary>
        /// 文章摘要
        /// </summary>
        public string Description { get; set; }
    }

    public class CorporateNewTitleInfo
    {
        /// <summary>
        /// id
        /// </summary>
        public long CorporateId { get; set; }

        /// <summary>
        /// 英文文章标题
        /// </summary>
        public string ENNewTitle { get; set; }

        /// <summary>
        /// 中文文章标题
        /// </summary>
        public string CNNewTitle { get; set; }
    }
}
