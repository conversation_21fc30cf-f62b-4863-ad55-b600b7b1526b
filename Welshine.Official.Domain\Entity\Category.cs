﻿namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 分类表
    /// </summary>
    [SqlSugar.SugarTable("cms_category")]
    public class Category : BaseBigEntity
    {
        /// <summary>
        /// 父节点Id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "father_id")]
        public int FatherId { get; set; }

        /// <summary>
        /// 分类名称(英文)
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "name_en")]
        public string NameEN { get; set; }

        /// <summary>
        /// 分类名称(中文)
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "name_cn")]
        public string NameCN { get; set; }

        /// <summary>
        /// 级别
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "level")]
        public int Level { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "sort")]
        public int Sort { get; set; }

        /// <summary>
        /// logo
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "logo_Img_url")]
        public string LogoImgUrl { get; set; }

        /// <summary>
        /// logo
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "logo_Img_url_activate")]
        public string LogoImgUrlActivate { get; set; }

        /// <summary>
        /// logo
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "mobile_Img_url_activate")]
        public string MobileImgUrlActivate { get; set; }

    }
}
