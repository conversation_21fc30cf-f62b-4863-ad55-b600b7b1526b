﻿using DTHY.Core.Repository;
using SqlSugar.IOC;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository.Interface;
using DocumentFormat.OpenXml.Bibliography;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 留言区仓储实现
    /// </summary>
    public class MessageBoardRepository : BaseRepository<MessageBoard>, IMessageBoardRepository
    {
        /// <summary>
        /// 获取留言区列表(分页)
        /// </summary>
        /// <param name="phone">电话</param>
        /// <param name="times">提交时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageRows<MessageBoardPageListResponse>> GetMessageBoardPageList(string phone, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<MessageBoardPageListResponse> result = new PageRows<MessageBoardPageListResponse>();

            result.Data = await DbScoped.SugarScope.Queryable<MessageBoard>()
                .WhereIF(!string.IsNullOrWhiteSpace(phone), x => x.Phone.Contains(phone))
                .WhereIF(times != null && times.From != null, x => x.CreatedTime >= times.From)
                .WhereIF(times != null && times.To != null, x => x.CreatedTime <= times.To)
                .OrderByIF(orderFile == "Id" || orderFile == "id", x => x.Id, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "CreatedTime", x => x.CreatedTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .Select(x => new MessageBoardPageListResponse
                {
                    Id = x.Id,
                    Name = x.Name,
                    Phone = x.Phone,
                    Content = x.Content,
                    Email = x.Email,
                    Area = x.Area,
                    IpAddress = x.IpAddress,
                    CreatedTime = x.CreatedTime

                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            result.Total = totalNumber;

            return result;
        }

        /// <summary>
        /// 获取留言区列表(不分页)
        /// </summary>
        /// <param name="phone">电话</param>
        /// <param name="times">提交时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<List<MessageBoardPageListResponse>> GetMessageBoardList(string phone, TimeHorizon times)
        {
            RefAsync<int> totalNumber = 0;
            List<MessageBoardPageListResponse> result = new List<MessageBoardPageListResponse>();

            result = await DbScoped.SugarScope.Queryable<MessageBoard>()
                .WhereIF(!string.IsNullOrWhiteSpace(phone), x => x.Phone.Contains(phone))
                .WhereIF(times != null && times.From != null, x => x.CreatedTime >= times.From)
                .WhereIF(times != null && times.To != null, x => x.CreatedTime <= times.To)
                .OrderBy(x => x.CreatedTime, OrderByType.Desc)
                .Select(x => new MessageBoardPageListResponse
                {
                    Id = x.Id,
                    Name = x.Name,
                    Phone = x.Phone,
                    Content = x.Content,
                    Email = x.Email,
                    Area = x.Area,
                    IpAddress = x.IpAddress,
                    CreatedTime = x.CreatedTime

                })
                .ToListAsync();

            return result;
        }

        /// <summary>
        /// 添加留言
        /// </summary>
        /// <param name="messageBoard"></param>
        /// <returns></returns>
        public async Task<bool> AddMessageBoard(MessageBoard messageBoard)
        {
            var id = await DbScoped.SugarScope.Insertable(messageBoard).ExecuteReturnBigIdentityAsync();
            if (id > 0)
            {
                return true;
            }
            return false;
        }

    }
}
