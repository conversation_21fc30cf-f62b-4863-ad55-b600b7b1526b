﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Service.Interface;
using Welshine.Official.Web.Api.Core;

namespace Welshine.Official.Web.Api.Controllers
{
    /// <summary>
    /// 直营门店
    /// </summary>
    public class StoreController : BaseApiController
    {
        private readonly IVersionsService _versionsService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="versionsService"></param>
        public StoreController(IVersionsService versionsService)
        {
            _versionsService = versionsService;
        }

        /// <summary>
        /// 获取直营门店列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetWebContactsPageListResponse>> GetContactsPageList([FromBody] RequestPageModel request)
        {
            GetWebContactsPageListResponse pageRows = null;
            try
            {
                pageRows = await _versionsService.GetWebContactsPageList(request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetContactsPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

    }
}
