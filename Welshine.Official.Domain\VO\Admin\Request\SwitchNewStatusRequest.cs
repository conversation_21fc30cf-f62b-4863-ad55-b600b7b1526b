﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class SwitchNewStatusRequest
    {
        /// <summary>
        /// 资讯id
        /// </summary>
        [Required(ErrorMessage = "资讯id为必填")]
        public long? NewId { get; set; }
        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [Required(ErrorMessage = "内容状态为必填")]
        [Range(0, 1, ErrorMessage = "内容状态参数只能为 0->未发布; 1->已发布")]
        public int? ReleaseStatus { get; set; }
    }
}
