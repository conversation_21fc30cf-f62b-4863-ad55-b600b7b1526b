﻿using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class CorporateNewService : ICorporateNewService
    {
        private readonly ICorporateNewRepository _corporateNewRepository;

        public CorporateNewService(ICorporateNewRepository corporateNewRepository) 
        {
            _corporateNewRepository = corporateNewRepository;
        }

        public CorporateNewResponse AddNew(string cnTitle, string cnContent, string cnImage, string enTitle, string enContent, string enImage,DateTime? cnPublishDate,DateTime? enPublishDate, JwtTokenUserInfo userInfo, int newColumn, int newRecommend, string enDescription, string cnDescription)
        {
            var corporateNew = new CorporateNew();
            corporateNew.CNTitle = cnTitle;
            corporateNew.CNContent = cnContent;
            corporateNew.CNImage = cnImage;
            corporateNew.ENTitle = enTitle;
            corporateNew.ENContent = enContent;
            corporateNew.ENImage = enImage;
            corporateNew.ReleaseStatus = 0;
            corporateNew.UpdatedTime = DateTime.Now;
            corporateNew.CreatedTime = DateTime.Now;
            corporateNew.CreatedBy = userInfo.UserName;
            corporateNew.UpdatedBy = userInfo.UserName;
            corporateNew.ModifierId = userInfo.UserId;
            corporateNew.CreatorId = userInfo.UserId;
            corporateNew.CNPublishDate = cnPublishDate;
            corporateNew.ENPublishDate = enPublishDate;
            corporateNew.NewColumn = newColumn;
            corporateNew.NewRecommend = newRecommend;
            corporateNew.ENDescription = enDescription;
            corporateNew.CNDescription = cnDescription;
            /*corporateNew.ReleaseStatus = releaseStatus;
            if (releaseStatus == 1)
            {
                var releaseCount = _corporateNewRepository.CountEntity(x =>x.ReleaseStatus == 1).Result;
                if (releaseCount >= 20)
                {
                    throw new BusinessException(ErrorCode.CorporateNewReleaseError);
                }
            }*/
            corporateNew = _corporateNewRepository.InsertAsync(corporateNew).Result;
            CorporateNewResponse item = new CorporateNewResponse();
            item.NewId = corporateNew.Id;
            item.UpdateTime = corporateNew.UpdatedTime.Value;
            item.UpdatedBy = corporateNew.UpdatedBy;
            item.CreateTime = corporateNew.CreatedTime;
            item.ReleaseStatus = corporateNew.ReleaseStatus;
            item.NewTitle = corporateNew.CNTitle;
            return item;
        }

        public void DeleteNew(long id, JwtTokenUserInfo userInfo)
        {
            var corporateNew = _corporateNewRepository.GetEntity(x => x.Id == id).Result;
            if (corporateNew == null)
            {
                throw new BusinessException(ErrorCode.CorporateNewNotExist);
            }
            if (corporateNew.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.CorporateNewStatusError);
            } 
            corporateNew.UpdatedBy = userInfo.UserName;
            corporateNew.UpdatedTime = DateTime.Now;
            corporateNew.ModifierId = userInfo.UserId;
            corporateNew.IsDeleted = true;
            _corporateNewRepository.Update(corporateNew).Wait();
        }

        public void EditNew(long id, string cnTitle, string cnContent, string cnImage, string enTitle, string enContent, string enImage, DateTime? cnPublishDate, DateTime? enPublishDate, JwtTokenUserInfo userInfo, int newColumn, int newRecommend, string enDescription, string cnDescription)
        {
            var corporateNew = _corporateNewRepository.GetEntity(x => x.Id == id&&x.IsDeleted == false).Result;
            if (corporateNew == null)
            {
                throw new BusinessException(ErrorCode.CorporateNewNotExist);
            }
            if (corporateNew.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.CorporateNewStatusError);
            }
            corporateNew.CNTitle = cnTitle;
            corporateNew.CNContent = cnContent;
            corporateNew.CNImage = cnImage;
            corporateNew.ENTitle = enTitle;
            corporateNew.ENContent = enContent;
            corporateNew.ENImage = enImage;
            corporateNew.UpdatedBy = userInfo.UserName;
            corporateNew.UpdatedTime = DateTime.Now;
            corporateNew.ModifierId = userInfo.UserId;
            corporateNew.NewColumn = newColumn;
            corporateNew.NewRecommend = newRecommend;
            corporateNew.ENDescription = enDescription;
            corporateNew.CNDescription = cnDescription;
            corporateNew.CNPublishDate = cnPublishDate;
            corporateNew.ENPublishDate = enPublishDate;
            /*corporateNew.ReleaseStatus = releaseStatus;
            if (releaseStatus == 1)
            {
                var releaseCount = _corporateNewRepository.CountEntity(x => x.Id != id && x.ReleaseStatus == 1).Result;
                if (releaseCount >= 20)
                {
                    throw new BusinessException(ErrorCode.CorporateNewReleaseError);
                }
            }*/
            _corporateNewRepository.Update(corporateNew).Wait();
        }

        public PageRows<CorporateNewResponse> FindCorporateNewPageList(string title, int? releaseStatus, DateTime? startDate, DateTime? endDate, int? newColumn, int? newRecommend, int pageIndex, int pageSize,string orderField, OrderByType orderByType)
        {
            var result= new PageRows<CorporateNewResponse>();
            var totalNumb = 0;
            var data = DbScoped.SugarScope.Queryable<CorporateNew>()
                .WhereIF(!string.IsNullOrEmpty(title),x=>x.CNTitle.Contains(title))
                .WhereIF(releaseStatus!=null,x=>x.ReleaseStatus == releaseStatus)
                .WhereIF(startDate!=null,x=>x.CreatedTime>=startDate)
                .WhereIF(endDate!=null,x=>x.CreatedTime<endDate)
                .WhereIF(newColumn != null, x => x.NewColumn== newColumn)
                .WhereIF(newRecommend != null, x => x.NewRecommend == newRecommend)
                .Where(x=>x.IsDeleted == false)
                .OrderByIF(orderField == "createdTime", x => x.CreatedTime, orderByType)
                .OrderByIF(orderField == "updatedTime", x => x.UpdatedTime, orderByType)
                .ToPageList(pageIndex,pageSize,ref totalNumb);
            var resultData = new List<CorporateNewResponse>();
            foreach (var corporateNew in data)
            {
                CorporateNewResponse item = new CorporateNewResponse();
                item.NewId = corporateNew.Id;
                item.UpdateTime = corporateNew.UpdatedTime.Value;
                item.UpdatedBy = corporateNew.UpdatedBy;
                item.CreateTime = corporateNew.CreatedTime;
                item.ReleaseStatus = corporateNew.ReleaseStatus;
                item.NewTitle = corporateNew.CNTitle;
                item.PublishDate = corporateNew.CNPublishDate;
                item.NewRecommend = corporateNew.NewRecommend;
                item.NewColumn = corporateNew.NewColumn;
                resultData.Add(item);
            }
            result.Total = totalNumb;
            result.Data  = resultData;
            return result;
        }

        public List<CorporateNewDetailResponse> GetNewDetailPageList()
        {
            var result = new List<CorporateNewDetailResponse>();
            var data = DbScoped.SugarScope.Queryable<CorporateNew>().Where(x => x.ReleaseStatus == 1).Where(x => x.IsDeleted == false)
                .OrderBy(x => x.UpdatedBy, OrderByType.Desc).ToPageList(1, 20);
            foreach (var corporateNew in data)
            {
                var itemData = new CorporateNewDetailResponse();
                itemData.NewId = corporateNew.Id;
                itemData.UpdatedBy = corporateNew.UpdatedBy;
                itemData.CreatedBy = corporateNew.CreatedBy;
                itemData.UpdatedTime = corporateNew.UpdatedTime.Value;
                itemData.CreatedTime = corporateNew.CreatedTime;
                itemData.CNItem.NewTitle = corporateNew.CNTitle;
                itemData.CNItem.NewContent = corporateNew.CNContent;
                itemData.CNItem.NewImage = corporateNew.CNImage;
                itemData.ENItem.NewTitle = corporateNew.ENTitle;
                itemData.ENItem.NewContent = corporateNew.ENContent;
                itemData.ENItem.NewImage = corporateNew.ENImage;
                itemData.CNItem.PublishDate = corporateNew.CNPublishDate;
                itemData.ENItem.PublishDate = corporateNew.ENPublishDate;
                itemData.ENItem.Description = corporateNew.ENDescription;
                itemData.CNItem.Description = corporateNew.CNDescription;
                itemData.NewColumn = corporateNew.NewColumn;
                itemData.NewRecommend = corporateNew.NewRecommend;
                result.Add(itemData);
            }
            return result;
        }
        public CorporateNewDetailResponse FindNewDetail(long id)
        {
            var result = new CorporateNewDetailResponse(); 
            var corporateNew = _corporateNewRepository.GetEntity(x=>x.Id == id&&x.IsDeleted == false).Result;
            if (corporateNew == null)
            {
                throw new BusinessException(ErrorCode.CorporateNewNotExist);
            }
            result.NewId = corporateNew.Id;
            result.UpdatedBy= corporateNew.UpdatedBy;
            result.CreatedBy = corporateNew.CreatedBy;
            result.UpdatedTime = corporateNew.UpdatedTime.Value;
            result.CreatedTime = corporateNew.CreatedTime;
            result.CNItem.NewTitle = corporateNew.CNTitle;
            result.CNItem.NewContent = corporateNew.CNContent;
            result.CNItem.NewImage = corporateNew.CNImage;
            result.ENItem.NewTitle = corporateNew.ENTitle;
            result.ENItem.NewContent = corporateNew.ENContent;
            result.ENItem.NewImage = corporateNew.ENImage;
            result.ENItem.PublishDate = corporateNew.ENPublishDate;
            result.CNItem.PublishDate = corporateNew.CNPublishDate;
            result.NewColumn = corporateNew.NewColumn;
            result.NewRecommend = corporateNew.NewRecommend;
            result.CNItem.Description = corporateNew.CNDescription;
            result.ENItem.Description = corporateNew.ENDescription;


            List<CorporateNew> list = _corporateNewRepository.GetEntityList(x => !x.IsDeleted && x.NewColumn == corporateNew.NewColumn && x.ReleaseStatus == 1).Result.OrderByDescending(x => x.CNPublishDate).ToList();
            if (list.Count > 1)
            {
                for (int i = 0; i < list.Count; i++)
                {
                    if (list[i].Id == id)
                    {
                        if (i == 0)
                        {
                            CorporateNew cn = list[i + 1];
                            CorporateNewTitleInfo next = new CorporateNewTitleInfo();
                            next.CorporateId = cn.Id;
                            next.ENNewTitle = cn.ENTitle;
                            next.CNNewTitle = cn.CNTitle;
                            result.NextCorporateNewTitle = next;
                        }
                        else if(i > 0)
                        {
                            CorporateNew cnup = list[i - 1];
                            CorporateNewTitleInfo up = new CorporateNewTitleInfo();
                            up.CorporateId = cnup.Id;
                            up.ENNewTitle = cnup.ENTitle;
                            up.CNNewTitle = cnup.CNTitle;
                            result.UpCorporateNewTitle = up;

                            if (i < list.Count - 1)
                            {
                                CorporateNew cn = list[i + 1];
                                CorporateNewTitleInfo next = new CorporateNewTitleInfo();
                                next.CorporateId = cn.Id;
                                next.ENNewTitle = cn.ENTitle;
                                next.CNNewTitle = cn.CNTitle;
                                result.NextCorporateNewTitle = next;
                            }
                        }
                        break;
                    }
                }
            }

            return result;
        }

        public void SwitchNewStatus(long id, int releaseStatus, JwtTokenUserInfo userInfo)
        {
            var corporateNew = _corporateNewRepository.GetEntity(x => x.Id == id && x.IsDeleted == false).Result;
            if (corporateNew == null)
            {
                throw new BusinessException(ErrorCode.CorporateNewNotExist);
            }
            //if (releaseStatus == 1)
            //{
            //    var releaseCount = _corporateNewRepository.CountEntity(x => x.Id != id && x.ReleaseStatus == 1).Result;
            //    if (releaseCount >= 20)
            //    {
            //        throw new BusinessException(ErrorCode.CorporateNewReleaseError);
            //    }
            //}
            corporateNew.ReleaseStatus = releaseStatus;
            corporateNew.UpdatedBy = userInfo.UserName;
            corporateNew.UpdatedTime = DateTime.Now;
            corporateNew.ModifierId = userInfo.UserId;
            _corporateNewRepository.Update(corporateNew).Wait();
        }

        public int GetRecommendCountByNewColumn(int NewColumn)
        {
            return _corporateNewRepository.CountEntity(x => x.NewColumn == NewColumn && x.NewRecommend == 1 && !x.IsDeleted).Result;
        }

        public PageRows<CorporateNewDetailResponse> GetCorporateNewList(int newColumn, int? newRecommend, int pageIndex, int pageSize, string orderField, OrderByType orderByType)
        {
            var result = new PageRows<CorporateNewDetailResponse>();
            var totalNumb = 0;
            var data = DbScoped.SugarScope.Queryable<CorporateNew>()
                .Where(x => x.NewColumn == newColumn && x.ReleaseStatus == 1)
                .WhereIF(newRecommend != null, x => x.NewRecommend == newRecommend)
                .Where(x => x.IsDeleted == false)
                .OrderBy(x => x.CNPublishDate, orderByType)
                .ToPageList(pageIndex, pageSize, ref totalNumb);
            var resultData = new List<CorporateNewDetailResponse>();
            foreach (var corporateNew in data)
            {
                CorporateNewDetailResponse itemData = new CorporateNewDetailResponse();
                itemData.NewId = corporateNew.Id;
                itemData.UpdatedBy = corporateNew.UpdatedBy;
                itemData.CreatedBy = corporateNew.CreatedBy;
                itemData.UpdatedTime = corporateNew.UpdatedTime.Value;
                itemData.CreatedTime = corporateNew.CreatedTime;
                itemData.CNItem.NewTitle = corporateNew.CNTitle;
                itemData.CNItem.NewContent = corporateNew.CNContent;
                itemData.CNItem.NewImage = corporateNew.CNImage;
                itemData.ENItem.NewTitle = corporateNew.ENTitle;
                itemData.ENItem.NewContent = corporateNew.ENContent;
                itemData.ENItem.NewImage = corporateNew.ENImage;
                itemData.CNItem.PublishDate = corporateNew.CNPublishDate;
                itemData.ENItem.PublishDate = corporateNew.ENPublishDate;
                itemData.ENItem.Description = corporateNew.ENDescription;
                itemData.CNItem.Description = corporateNew.CNDescription;
                itemData.NewColumn = corporateNew.NewColumn;
                itemData.NewRecommend = corporateNew.NewRecommend;
                itemData.CNPublishDate = corporateNew.CNPublishDate;

                resultData.Add(itemData);
            }
            result.Total = totalNumb;
            result.Data = resultData;
            return result;
        }
    }
}
