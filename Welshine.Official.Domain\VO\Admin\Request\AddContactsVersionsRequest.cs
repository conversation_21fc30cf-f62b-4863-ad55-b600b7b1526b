﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;
using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加联系方式版本条目
    /// </summary>
    public class AddContactsVersionsRequest
    {
        /// <summary>
        /// 保存类型: 0->保存; 1->保存提交审核
        /// </summary>
        [Required(ErrorMessage = "保存类型必填,请完善")]
        [Range(0, 1, ErrorMessage = "保存类型参数错误")]
        public int? SaveType { get; set; }

        /// <summary>
        /// 中文
        /// </summary>
        [Required(ErrorMessage = "中文是必填项")]
        public ContactsVersionsItem CNInfo { get; set; }

        /// <summary>
        /// 英文
        /// </summary>
        [Required(ErrorMessage = "英文是必填项")]
        public ContactsVersionsItem ENInfo { get; set; }
    }

    /// <summary>
    /// 联系方式信息条目
    /// </summary>
    public class ContactsVersionsItem
    {
        /// <summary>
        /// 门店名称
        /// </summary>
        [Required(ErrorMessage = "门店名称是必填项")]
        [StringLength(40, ErrorMessage = "门店名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[(?:\s)*\u4e00-\u9fa5_a-zA-Z]+$", ErrorMessage = "门店名称格式不正确")]
        public string StoreName { get; set; }

        /// <summary>
        /// 门店图片
        /// </summary>
        [Required(ErrorMessage = "门店图片是必填项")]
        public string StorePicture { get; set; }

        /// <summary>
        /// 门店地址
        /// </summary>
        [Required(ErrorMessage = "门店地址必填,请完善")]
        [StringLength(200, ErrorMessage = "地址长度不符,请完善", MinimumLength = 1)]
        public string StoreAddress { get; set; }

        ///// <summary>
        ///// 联系我们
        ///// </summary>
        //public string StoreRelation { get; set; }

        /// <summary>
        /// 门店开始营业时间
        /// </summary>
        [Required(ErrorMessage = "每天开业时间必填,请完善")]
        [StringLength(5, ErrorMessage = "每天开业时间长度不符", MinimumLength = 1)]
        [RegularExpression(@"^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$", ErrorMessage = "开业时间格式错误")]
        public string StoreStartTime { get; set; }

        /// <summary>
        /// 门店结束营业时间
        /// </summary>
        [Required(ErrorMessage = "每天停业时间必填,请完善")]
        [StringLength(5, ErrorMessage = "每天停业时间长度不符", MinimumLength = 1)]
        [RegularExpression(@"^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$", ErrorMessage = "停业时间格式错误")]
        public string StoreEndTime { get; set; }

        ///// <summary>
        ///// 营业状态
        ///// </summary>
        //[Required(ErrorMessage = "营业状态必填,请完善")]
        //[EnumDataType(typeof(EnumStoreStatus), ErrorMessage = "营业状态参数错误")]
        //public EnumStoreStatus? StoreStatus { get; set; }

        /// <summary>
        /// 电话
        /// </summary> 
        [Required(ErrorMessage = "电话必填,请完善")]
        [ArrayRequired(ErrorMessage = "电话必填,请完善")]
        [StringArray("^[0-9_\\-@&=`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", 15, ErrorMessage = "电话格式错误")]
        [ArrayMaxLength(3, ErrorMessage = "手机最多三个")]
        public List<string> StorePhoneList { get; set; }

        ///// <summary>
        ///// 传真
        ///// </summary>
        //[StringLength(20, ErrorMessage = "传真长度不符", MinimumLength = 1)]
        //[RegularExpression("^[0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "传真格式错误")]
        //public string StoreFax { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(30, ErrorMessage = "邮箱长度不符", MinimumLength = 1)]
        [RegularExpression("^[A-Za-z0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "邮箱格式错误")]
        public string StoreEmail { get; set; }
    }
}
