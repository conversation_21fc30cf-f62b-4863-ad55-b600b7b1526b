﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class AddVersionsRequest<T>
    {
        /// <summary>
        /// 保存类型: 0->保存; 1->保存提交审核
        /// </summary>
        [Required(ErrorMessage = "保存类型必填,请完善")]
        [Range(0, 1, ErrorMessage = "保存类型参数错误")]
        public int? SaveType { get; set; }

        /// <summary>
        /// 版本标题
        /// </summary>
        [Required(ErrorMessage = "版本标题是必填项")]
        [StringLength(30, ErrorMessage = "版本标题长度不符", MinimumLength = 1)]
        public string Title { get; set; }

        /// <summary>
        /// 版本内容
        /// </summary>
        [Required(ErrorMessage = "版本内容是必填项")]
        [StringLength(50, ErrorMessage = "版本内容长度不符", MinimumLength = 1)]
        public string Content { get; set; }

        [Required(ErrorMessage = "Data是必填项")]
        public T Data { get; set; }

        ///// <summary>
        ///// PC端
        ///// </summary>
        //[Required(ErrorMessage = "PC端是必填项")]
        //public T PC { get; set; }

        ///// <summary>
        ///// 手机端
        ///// </summary>
        //[Required(ErrorMessage = "手机端是必填项")]
        //public T Mobile { get; set; }

    }
}
