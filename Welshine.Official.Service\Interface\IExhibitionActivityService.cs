﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Response;
using SqlSugar;

namespace Welshine.Official.Service.Interface
{
    public interface IExhibitionActivityService
    {
        /// <summary>
        /// 添加展会活动
        /// </summary>
        /// <param name="exhibitionActivityName">活动名称</param>
        /// <param name="exhibitionActivityStartTime">活动开始时间</param>
        /// <param name="exhibitionActivityEndTime">活动结束时间</param>
        /// <param name="exhibitionActivityAddress">活动地点</param>
        /// <param name="addressLongitude">经度</param>
        /// <param name="addressLatitude">纬度</param>
        /// <param name="codePrefix">券码前缀</param>
        /// <param name="entryPicture">活动入口图</param>
        /// <param name="participationPicture">活动参与图</param>
        /// <param name="codePicture">活动券码图</param>
        /// <param name="userName">创建人名称</param>
        /// <param name="userId">创建人id</param>
        /// <returns></returns>
        Task<bool> AddExhibitionActivity(string exhibitionActivityName, DateTime exhibitionActivityStartTime, DateTime exhibitionActivityEndTime, string exhibitionActivityAddress, decimal addressLongitude, decimal addressLatitude, string codePrefix, string entryPicture, string participationPicture, string codePicture, string userName, string userId);

        /// <summary>
        /// 编辑展会活动
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <param name="exhibitionActivityName">活动名称</param>
        /// <param name="exhibitionActivityStartTime">活动开始时间</param>
        /// <param name="exhibitionActivityEndTime">活动结束时间</param>
        /// <param name="exhibitionActivityAddress">活动地点</param>
        /// <param name="addressLongitude">经度</param>
        /// <param name="addressLatitude">纬度</param>
        /// <param name="entryPicture">活动入口图</param>
        /// <param name="participationPicture">活动参与图</param>
        /// <param name="codePicture">活动券码图</param>
        /// <param name="userName">创建人名称</param>
        /// <param name="userId">创建人id</param>
        /// <returns></returns>
        Task<bool> EditExhibitionActivity(long exhibitionActivityId, string exhibitionActivityName, DateTime exhibitionActivityStartTime, DateTime exhibitionActivityEndTime, string exhibitionActivityAddress, decimal addressLongitude, decimal addressLatitude, string entryPicture, string participationPicture, string codePicture, string userName, string userId);

        /// <summary>
        /// 展会活动列表
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="orderBy"></param>
        /// <param name="exhibitionActivityName">活动名称</param>
        /// <param name="exhibitionActivityStartTime">活动开始时间</param>
        /// <param name="exhibitionActivityEndTime">活动结束时间</param>
        /// <param name="status">活动状态:0->停用;1->启用;</param>
        /// <returns></returns>
        Task<PageRows<ExhibitionActivityResponse>> GetExhibitionActivityPageList(int pageIndex, int pageSize, string orderField, OrderByType orderByType, string exhibitionActivityName, DateTime? exhibitionActivityStartTime, DateTime? exhibitionActivityEndTime, int? status);

        /// <summary>
        /// 编辑展会活动状态
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <param name="status">活动状态:0->停用;1->启用;</param>
        /// <param name="userName">修改人名称</param>
        /// <param name="userId">修改人id</param>
        /// <returns></returns>
        Task<bool> EditExhibitionActivityStatus(long exhibitionActivityId, int status, string userName, string userId);

        /// <summary>
        /// 删除展会活动
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <param name="userName">修改人名称</param>
        /// <param name="userId">修改人id</param>
        /// <returns></returns>
        Task<bool> DeleteExhibitionActivity(long exhibitionActivityId, string userName, string userId);

        /// <summary>
        /// 获取展会活动详情
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <returns></returns>
        Task<ExhibitionActivityDetailResponse> GetExhibitionActivityDetail(long exhibitionActivityId);

    }
}
