﻿using Refit;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Welshine.Official.SDK.Model
{
    public class LoginResponse
    {
        [AliasAs("userId")]
        public long UserId { get; set; }
        [AliasAs("userNo")]
        public string UserNo { get; set; }
        [AliasAs("userName")]
        public string UserName { get; set; }
        [AliasAs("loginName")]
        public string LoginName { get;set; }
        [AliasAs("tel")]
        public string Tel { get; set; }
        [AliasAs("isFirstLogin")]
        public string IsFirstLogin { get; set; }
        [AliasAs("token")]
        public string Token { get; set; }
        [AliasAs("expiration")]
        public long Expiration { get; set; }
        [AliasAs("rbacMenu")]
        public List<string> RbacMenu { get; set; }
    }
}
