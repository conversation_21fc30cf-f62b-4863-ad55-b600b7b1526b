﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 分类产品服务接口
    /// </summary>
    public interface ICategoryProductService
    {

        /// <summary>
        /// 添加分类产品
        /// </summary>
        /// <param name="categoryProduct"></param>
        /// <param name="imgIds"></param>
        /// <param name="cornerMarkImgId"></param>
        /// <param name="IntroduceList"></param>
        /// <returns></returns>
        Task<CategoryProduct> AddCategoryProduct(CategoryProduct categoryProduct, List<string> imgIds, List<string> cornerMarkImgId, List<ProductIntroduce> IntroduceList);

        /// <summary>
        /// 编辑分类产品
        /// </summary>
        /// <param name="categoryProduct"></param>
        /// <param name="imgIds"></param>
        /// <param name="cornerMarkImgId"></param>
        /// <param name="IntroduceList"></param>
        /// <returns></returns>
        Task<CategoryProduct> EditCategoryProduct(CategoryProduct categoryProduct, List<string> imgIds, List<string> cornerMarkImgId, List<ProductIntroduce> IntroduceList);

        /// <summary>
        /// 删除分类产品
        /// </summary>
        /// <param name="categoryProduct"></param>
        /// <returns></returns>
        Task<bool> DeleteCategoryProduct(CategoryProduct categoryProduct);

        /// <summary>
        /// 获取分类产品详情
        /// </summary>
        /// <param name="categoryProductId"></param>
        /// <returns></returns>
        Task<GetCategoryProductResponse> GetCategoryProductDetail(long categoryProductId);

        /// <summary>
        /// 发布分类产品
        /// </summary>
        /// <param name="categoryProductId">分类产品Id</param>
        /// <param name="Status">发布状态</param>
        /// <param name="modifierId">修改人id</param>
        /// <param name="updatedBy">修改人</param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        Task<bool> ReleaseCategoryProduct(long categoryProductId, int status, string modifierId, string updatedBy);

        /// <summary>
        /// 获取分类产品列表
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="productName">商品名称</param>
        /// <param name="ReleaseStatus">内容状态</param>
        /// <param name="times">创建时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PageRows<CategoryProductListResponse>> GetCategoryProductPageList(int? productLanguage, string productName, int? ReleaseStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取所有未删除的分类产品
        /// </summary>
        /// <returns></returns>
        Task<List<CategoryProduct>> GetAllCategoryProductList();

        /// <summary>
        /// 根据分类Id获取已发布的分类商品
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="categoryId">分类id</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PageRows<GetWebCategoryProductListResponse>> GetCategoryProductListByCategoryId(long? categoryId, int? productLanguage, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 根据分类Id获取已发布的分类商品
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        Task<GetCategoryProductListOrderSortResponse> GetCategoryProductListByCategoryId(long categoryId);

        /// <summary>
        /// 修改产品排序
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="list">排序商品</param>
        /// <returns></returns>
        Task<bool> EditCategoryProductSort(long categoryId, int productLanguage, List<CategoryProduct> list);

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <param name="categoryProductId">分类产品Id</param>
        /// <param name="ModifierId">修改人Id</param>
        /// <param name="UpdatedBy">修改人名称</param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        Task<bool> EditProductCategory(long categoryId, long categoryProductId, string ModifierId, string UpdatedBy);
    }
}
