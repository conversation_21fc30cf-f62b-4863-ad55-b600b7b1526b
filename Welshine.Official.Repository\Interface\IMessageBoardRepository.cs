﻿using DTHY.Core.Repository;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 留言区仓储接口
    /// </summary>
    public interface IMessageBoardRepository : IBaseRepository<MessageBoard>
    {
        /// <summary>
        /// 获取留言区列表
        /// </summary>
        /// <param name="phone">电话</param>
        /// <param name="times">提交时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PageRows<MessageBoardPageListResponse>> GetMessageBoardPageList(string phone, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取留言区列表(不分页)
        /// </summary>
        /// <param name="phone">电话</param>
        /// <param name="times">提交时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<List<MessageBoardPageListResponse>> GetMessageBoardList(string phone, TimeHorizon times);

        /// <summary>
        /// 添加留言
        /// </summary>
        /// <param name="messageBoard"></param>
        /// <returns></returns>
        Task<bool> AddMessageBoard(MessageBoard messageBoard);
    }
}
