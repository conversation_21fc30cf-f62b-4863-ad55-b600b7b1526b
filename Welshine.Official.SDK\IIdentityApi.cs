﻿using Refit;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.SDK.Model;

namespace Welshine.Official.SDK
{
    [Headers(new string[] { "Content-Type: application/json" })]
    public interface IIdentityApi
    {
        /// <summary>
        /// 校验token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [Post("/identityManage/checkToken")]
        Task<CheckTokenResponse> CheckToken([Body] string token);
        /// <summary>
        /// 登陆
        /// </summary>
        /// <returns></returns>
        [Post("/login")]
        Task<string> Login([Body] UserLoginRequest request);

    }
}
