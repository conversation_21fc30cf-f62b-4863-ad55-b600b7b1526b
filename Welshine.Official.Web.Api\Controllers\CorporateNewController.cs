﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System.Collections.Generic;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service.Interface;
using Welshine.Official.Web.Api.Core;

namespace Welshine.Official.Web.Api.Controllers
{
    /// <summary>
    /// 企业资讯
    /// </summary>
    public class CorporateNewController : BaseApiController
    {
        private ICorporateNewService _corporateNewService;


        public CorporateNewController(ICorporateNewService service)
        {
            _corporateNewService = service;
        }
        /// <summary>
        /// 获取企业资讯明细
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<CorporateNewDetailResponse> GetCorporateNewDetail([FromBody]BaseRequest<OperateCorporateNewRequest> request)
        {
            var data = _corporateNewService.FindNewDetail(request.Body.NewId.Value);
            return Success("请求成功", data);
        }
        ///// <summary>
        ///// 获取企业资讯列表
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public BaseResponse<List<CorporateNewDetailResponse>> GetCorporateNewList()
        //{
        //    var data = _corporateNewService.GetNewDetailPageList();
        //    return Success("请求成功", data);
        //}

        /// <summary>
        /// 分页查询企业资讯
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<PageRows<CorporateNewDetailResponse>> GetCorporateNewList([FromBody] RequestPageModel<GetCorporateNewListRequest> request)
        {
            var body = request.RequestParams;
            var orderType = OrderByType.Desc;
            if (request.SortType == SortType.Asc)
            {
                orderType = OrderByType.Asc;
            }
            var data = _corporateNewService.GetCorporateNewList(body.NewColumn.Value, body.NewRecommend, request.PageIndex, request.PageSize, request.OrderFile, orderType);
            return Success("请求成功", data);
        }
    }
}
