﻿using SqlSugar;
using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 地区表
    /// </summary>
    [SqlSugar.SugarTable("cms_area")]
    public class Area
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "name")]
        public string Name { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "parent_id")]
        public int ParentId { get; set; }

        /// <summary>
        /// 简称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "short_name")]
        public string ShortName { get; set; }

        /// <summary>
        /// 级别
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "level")]
        public int Level { get; set; }

        /// <summary>
        /// 城市代码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "city_code")]
        public string CityCode { get; set; }

        /// <summary>
        /// 邮编
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "zip_code")]
        public string ZipCode { get; set; }

        /// <summary>
        /// 邮编
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "merger_name")]
        public string MergerName { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "longitude")]
        public string Longitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "latitude")]
        public string Latitude { get; set; }

        /// <summary>
        /// 拼音
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "pinyin")]
        public string PinYin { get; set; }

        /// <summary>
        /// 拼音前缀
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "pinyin_prefix")]
        public string PinyinPrefix { get; set; }
    }
}
