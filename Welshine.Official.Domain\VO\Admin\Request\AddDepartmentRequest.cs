﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class AddDepartmentRequest
    {
        /// <summary>
        /// 部门名称
        /// </summary>
        [Required(ErrorMessage = "部门名称是必填项")]
        [RegularExpression(@"^[\u4e00-\u9fa5]{2,10}$", ErrorMessage = "部门名称应为2-10个中文字")]
        public string DepartmentName { get; set; }
    }
}
