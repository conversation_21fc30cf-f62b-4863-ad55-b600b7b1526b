﻿using DTHY.Core.Repository;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 分类产品规格仓储接口
    /// </summary>
    public interface ICategoryProductIntroduceRepository : IBaseRepository<CategoryProductIntroduce>
    {
        Task<List<ProductIntroduce>> GetList(long categoryProductId);
    }
}
