<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile>.\Welshine.Official.WxApp.Api.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Welshine.Official.Repository\Welshine.Official.Repository.csproj" />
    <ProjectReference Include="..\Welshine.Official.Service\Welshine.Official.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile_jenkins">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Welshine.Official.WxApp.Api.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>


</Project>
