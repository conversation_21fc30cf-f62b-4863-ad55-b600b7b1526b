﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Dto;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 侧边浮层
    /// </summary>
    public class SidebarController : BaseApiController
    {
        private readonly IVersionsService _versionsService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="versionsService"></param>
        public SidebarController(IVersionsService versionsService)
        {
            _versionsService = versionsService;
        }

        /// <summary>
        /// 添加Sidebar并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2205">版本标题已存在</response>
        /// <response code="2239">序号不允许重复</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddSidebarVersions([FromBody] BaseRequest<AddSidebarVersionsRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                SidebarData saveData = new SidebarData()
                {
                    CNInfo = request.Body.CNInfo.OrderBy(x=>x.Sort).ToList(),
                    ENInfo = request.Body.ENInfo.OrderBy(x => x.Sort).ToList()
                };

                if (saveData.CNInfo.Select(x=>x.Sort).Distinct().ToList().Count != saveData.CNInfo.Count)
                {
                    throw new BusinessException(ErrorCode.SidebarSortError.ToDescriptionName(), ErrorCode.SidebarSortError.GetHashCode());
                }

                if (saveData.ENInfo.Select(x => x.Sort).Distinct().ToList().Count != saveData.ENInfo.Count)
                {
                    throw new BusinessException(ErrorCode.SidebarSortError.ToDescriptionName(), ErrorCode.SidebarSortError.GetHashCode());
                }

                VersionsDto<SidebarData> versions = new VersionsDto<SidebarData>()
                {
                    VersionType = EnumVersionType.Sidebar.GetHashCode(),
                    Title = request.Body.VersionsTitle,
                    Content = request.Body.VersionsContent,
                    Data = saveData,
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.AddVersions(entity);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddSidebarVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取首页Sidebar版本详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">版本Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetVersionsResponse<SidebarData>>> GetSidebarVersions([FromBody] BaseRequest<GetVersionsIdRequest> request)
        {
            GetVersionsResponse<SidebarData> result = null;
            try
            {
                result = await _versionsService.GetVersions<SidebarData>(EnumVersionType.Sidebar.GetHashCode(), request.Body.VersionsId.Value);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetSidebarVersions Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 修改首页Sidebar信息并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">Id不存在</response>
        /// <response code="2205">标题已存在</response>
        /// <response code="2208">状态为已发布,暂时无法编辑</response>
        /// <response code="2209">审核状态不为待提交或审核驳回,暂时无法编辑</response>
        /// <response code="2239">序号不允许重复</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditSidebarVersions([FromBody] BaseRequest<EditSidebarVersionsRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                SidebarData saveData = new SidebarData()
                {
                    CNInfo = request.Body.CNInfo.OrderBy(x => x.Sort).ToList(),
                    ENInfo = request.Body.ENInfo.OrderBy(x => x.Sort).ToList()
                };

                if (saveData.CNInfo.Select(x => x.Sort).Distinct().ToList().Count != saveData.CNInfo.Count)
                {
                    throw new BusinessException(ErrorCode.SidebarSortError.ToDescriptionName(), ErrorCode.SidebarSortError.GetHashCode());
                }

                if (saveData.ENInfo.Select(x => x.Sort).Distinct().ToList().Count != saveData.ENInfo.Count)
                {
                    throw new BusinessException(ErrorCode.SidebarSortError.ToDescriptionName(), ErrorCode.SidebarSortError.GetHashCode());
                }

                VersionsDto<SidebarData> versions = new VersionsDto<SidebarData>()
                {
                    Id = request.Body.VersionsId,
                    VersionType = EnumVersionType.Sidebar.GetHashCode(),
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    Title = request.Body.VersionsTitle,
                    Content = request.Body.VersionsContent,
                    Data = saveData,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.EditVersions(versions);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditSidebarVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> SidebarExistsRelease()
        {
            bool result = false;
            try
            {
                result = await _versionsService.ExistsRelease(EnumVersionType.Sidebar.GetHashCode());
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("SidebarExistsRelease Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 删除版本信息
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">版本Id不存在</response>
        /// <response code="2210">状态为已发布,暂时无法删除</response>
        /// <response code="2211">审核状态为待审核,暂时无法删除</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteSidebarVersions([FromBody] BaseRequest<GetVersionsIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    Id = request.Body.VersionsId.Value,
                    VersionType = EnumVersionType.Sidebar.GetHashCode(),
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now,
                };

                result = await _versionsService.DeleteVersions(versions);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteSidebarVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 版本信息审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">版本Id不存在</response>
        /// <response code="2214">审核状态不为待审核,暂时无法审核</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> SidebarVersionsApprove([FromBody] BaseRequest<SidebarVersionsApproveRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    Id = request.Body.VersionsId.Value,
                    VersionType = EnumVersionType.Sidebar.GetHashCode(),
                    ApproverStatus = request.Body.ApproverStatus.Value,
                    ApprovalOpinion = request.Body.ApprovalOpinion,
                    ApproverId = user.UserId,
                    Approver = user.UserName.ToString() ?? "system",
                    ApprovalTime = DateTime.Now,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _versionsService.VersionsApprove(versions);
                return Success("审核成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("SidebarVersionsApprove Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取版本列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<VersionsListResponse>>> GetSidebarPageList([FromBody] RequestPageModel<VersionsListRequest> request)
        {
            PageRows<VersionsListResponse> pageRows = null;
            try
            {
                var user = GetUserInfo();

                pageRows = await _versionsService.GetVersionsPageList(user.UserId, EnumVersionType.Sidebar.GetHashCode(), request.RequestParams.VersionsTitle, request.RequestParams.ReleaseStatus, request.RequestParams.ApproverStatus, request.RequestParams.ReleaseTimeScope, request.OrderFile, request.SortType, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetSidebarPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<VersionsListResponse>>> GetSidebarReleaseVersions()
        {
            List<VersionsListResponse> pageRows = null;
            try
            {
                pageRows = await _versionsService.GetAllReleaseVersions();

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetSidebarReleaseVersions " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
    }
}
