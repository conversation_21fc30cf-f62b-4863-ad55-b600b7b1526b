﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Office2021.DocumentTasks;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using MySqlX.XDevAPI.Common;
using Org.BouncyCastle.Utilities;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class CategoryProductService : ICategoryProductService
    {
        private readonly IFileRepository _fileRepository;
        private readonly ICategoryService _categoryService;
        private readonly ICategoryProductRepository _categoryProductRepository;
        private readonly ICategoryProductIntroduceRepository _categoryProductIntroduceRepository;

        public CategoryProductService(ICategoryProductRepository categoryProductRepository, ICategoryService categoryService, IFileRepository fileRepository, ICategoryProductIntroduceRepository categoryProductIntroduceRepository)
        {
            _categoryProductRepository = categoryProductRepository;
            _categoryService = categoryService;
            _fileRepository = fileRepository;
            _categoryProductIntroduceRepository = categoryProductIntroduceRepository;
        }

        /// <summary>
        /// 添加分类产品
        /// </summary>
        /// <param name="categoryProduct"></param>
        /// <param name="imgIds"></param>
        /// <param name="cornerMarkImgId"></param>
        /// <returns></returns>
        public async Task<CategoryProduct> AddCategoryProduct(CategoryProduct categoryProduct, List<string> imgIds, List<string> cornerMarkImgId, List<ProductIntroduce> IntroduceList)
        {
            if(imgIds.Distinct().ToList().Count != imgIds.Count)
            {
                throw new BusinessException(ErrorCode.CategoryProductImgDistinctError.ToDescriptionName(), ErrorCode.CategoryProductImgDistinctError.GetHashCode());
            }

            Domain.Entity.Category category = await _categoryService.GetCategory(categoryProduct.CategoryId);
            if (category == null)
            {
                throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
            }
            if (category.Level != 2)
            {
                throw new BusinessException(ErrorCode.CategoryProductAddCategoryError.ToDescriptionName(), ErrorCode.CategoryProductAddCategoryError.GetHashCode());
            }

            foreach (var item in imgIds)
            {
                if (!await _fileRepository.Exists(item))
                {
                    throw new BusinessException(ErrorCode.CategoryProductImgNoFontError.ToDescriptionName(), ErrorCode.CategoryProductImgNoFontError.GetHashCode());
                }
            }

            if (cornerMarkImgId != null && cornerMarkImgId.Count > 0)
            {
                foreach (var item in cornerMarkImgId)
                {
                    if (!await _fileRepository.Exists(item))
                    {
                        throw new BusinessException(ErrorCode.CategoryProductCornerMarkImgNoFontError.ToDescriptionName(), ErrorCode.CategoryProductCornerMarkImgNoFontError.GetHashCode());
                    }
                }
            }

            int count = await DbScoped.SugarScope.Queryable<CategoryProduct>().CountAsync(x => x.CategoryId == categoryProduct.CategoryId);
            if (count >= 999)
            {
                throw new BusinessException(ErrorCode.CategoryProductAddCountError.ToDescriptionName(), ErrorCode.CategoryProductAddCountError.GetHashCode());
            }

            var result = await _categoryProductRepository.AddBigIdentity(categoryProduct);
            if (result)
            {
                //商品图
                foreach (var item in imgIds)
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = item,
                        ObjectId = categoryProduct.Id,
                        TableEnum = EnumRelationType.CategoryProductPicture,
                        CreatedBy = categoryProduct.UpdatedBy,
                        CreatorId = categoryProduct.CreatorId,
                        UpdatedBy = categoryProduct.UpdatedBy,
                        ModifierId = categoryProduct.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }

                //角标
                if (cornerMarkImgId != null && cornerMarkImgId.Count > 0)
                {
                    foreach (var item in cornerMarkImgId)
                    {
                        FilesRelation fr = new FilesRelation()
                        {
                            FilesId = item,
                            ObjectId = categoryProduct.Id,
                            TableEnum = EnumRelationType.CategoryProductCornerMarkPicture,
                            CreatedBy = categoryProduct.UpdatedBy,
                            CreatorId = categoryProduct.CreatorId,
                            UpdatedBy = categoryProduct.UpdatedBy,
                            ModifierId = categoryProduct.ModifierId
                        };
                        await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                    }
                }

                //规格
                if (IntroduceList != null && IntroduceList.Count > 0)
                { 
                    foreach(var item in IntroduceList)
                    {
                        CategoryProductIntroduce pi = new CategoryProductIntroduce()
                        {
                            CategoryProductId = categoryProduct.Id,
                            IntroduceModel = item.IntroduceModel,
                            IntroduceSize = item.IntroduceSize,
                            IntroduceCapacity = item.IntroduceCapacity,
                            ImgUrl = item.imgUrl
                        };
                        await DbScoped.SugarScope.Insertable(pi).ExecuteCommandAsync();
                    }
                }

                return categoryProduct;
            }

            return categoryProduct;
        }

        /// <summary>
        /// 编辑分类产品
        /// </summary>
        /// <param name="categoryProduct"></param>
        /// <param name="imgIds"></param>
        /// <param name="cornerMarkImgId"></param>
        /// <returns></returns>
        public async Task<CategoryProduct> EditCategoryProduct(CategoryProduct categoryProduct, List<string> imgIds, List<string> cornerMarkImgId, List<ProductIntroduce> IntroduceList)
        {
            CategoryProduct entity = await _categoryProductRepository.GetCategoryProductById(categoryProduct.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.CategoryProductNoFoundError.ToDescriptionName(), ErrorCode.CategoryProductNoFoundError.GetHashCode());
            }

            if (entity.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.CategoryProductEditReleaseStatusError.ToDescriptionName(), ErrorCode.CategoryProductEditReleaseStatusError.GetHashCode());
            }

            if (imgIds.Distinct().ToList().Count != imgIds.Count)
            {
                throw new BusinessException(ErrorCode.CategoryProductImgDistinctError.ToDescriptionName(), ErrorCode.CategoryProductImgDistinctError.GetHashCode());
            }

            Domain.Entity.Category category = await _categoryService.GetCategory(categoryProduct.CategoryId);
            if (category == null)
            {
                throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
            }
            if (category.Level != 2)
            {
                throw new BusinessException(ErrorCode.CategoryProductAddCategoryError.ToDescriptionName(), ErrorCode.CategoryProductAddCategoryError.GetHashCode());
            }

            foreach (var item in imgIds)
            {
                if (!await _fileRepository.Exists(item))
                {
                    throw new BusinessException(ErrorCode.CategoryProductImgNoFontError.ToDescriptionName(), ErrorCode.CategoryProductImgNoFontError.GetHashCode());
                }
            }

            if (cornerMarkImgId != null && cornerMarkImgId.Count > 0)
            {
                foreach (var item in cornerMarkImgId)
                {
                    if (!await _fileRepository.Exists(item))
                    {
                        throw new BusinessException(ErrorCode.CategoryProductCornerMarkImgNoFontError.ToDescriptionName(), ErrorCode.CategoryProductCornerMarkImgNoFontError.GetHashCode());
                    }
                }
            }

            entity.ProductLanguage = categoryProduct.ProductLanguage;
            entity.CategoryId = categoryProduct.CategoryId;
            entity.ProductName = categoryProduct.ProductName;
            entity.ProductTexture = categoryProduct.ProductTexture;
            entity.ProductColor = categoryProduct.ProductColor;
            entity.ProductSpecification = categoryProduct.ProductSpecification;
            entity.ProductIntroduce = categoryProduct.ProductIntroduce;
            entity.ModifierId = categoryProduct.ModifierId;
            entity.UpdatedBy = categoryProduct.UpdatedBy;
            entity.UpdatedTime = categoryProduct.UpdatedTime;

            var result = await _categoryProductRepository.Update(entity);
            if (result)
            {
                //清空关联文件
                await DbScoped.SugarScope.Updateable<FilesRelation>()
                    .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = categoryProduct.ModifierId, UpdatedBy = categoryProduct.UpdatedBy, UpdatedTime = categoryProduct.UpdatedTime })
                    .Where(x => x.ObjectId == categoryProduct.Id && x.TableEnum == EnumRelationType.CategoryProductPicture)
                    .ExecuteCommandAsync();

                foreach (var item in imgIds)
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = item,
                        ObjectId = categoryProduct.Id,
                        TableEnum = EnumRelationType.CategoryProductPicture,
                        CreatedBy = categoryProduct.UpdatedBy,
                        CreatorId = categoryProduct.CreatorId,
                        UpdatedBy = categoryProduct.UpdatedBy,
                        ModifierId = categoryProduct.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }

                //清空关联文件
                await DbScoped.SugarScope.Updateable<FilesRelation>()
                    .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = categoryProduct.ModifierId, UpdatedBy = categoryProduct.UpdatedBy, UpdatedTime = categoryProduct.UpdatedTime })
                    .Where(x => x.ObjectId == categoryProduct.Id && x.TableEnum == EnumRelationType.CategoryProductCornerMarkPicture)
                    .ExecuteCommandAsync();

                //角标
                if (cornerMarkImgId != null && cornerMarkImgId.Count > 0)
                {
                    foreach (var item in cornerMarkImgId)
                    {
                        FilesRelation fr = new FilesRelation()
                        {
                            FilesId = item,
                            ObjectId = categoryProduct.Id,
                            TableEnum = EnumRelationType.CategoryProductCornerMarkPicture,
                            CreatedBy = categoryProduct.UpdatedBy,
                            CreatorId = categoryProduct.CreatorId,
                            UpdatedBy = categoryProduct.UpdatedBy,
                            ModifierId = categoryProduct.ModifierId
                        };
                        await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                    }
                }

                //清空规格
                await DbScoped.SugarScope.Updateable<CategoryProductIntroduce>()
                    .SetColumns(x => new CategoryProductIntroduce() { IsDeleted = true, ModifierId = categoryProduct.ModifierId, UpdatedBy = categoryProduct.UpdatedBy, UpdatedTime = categoryProduct.UpdatedTime })
                    .Where(x => x.CategoryProductId == categoryProduct.Id)
                    .ExecuteCommandAsync();

                //规格
                if (IntroduceList != null && IntroduceList.Count > 0)
                {
                    foreach (var item in IntroduceList)
                    {
                        CategoryProductIntroduce pi = new CategoryProductIntroduce()
                        {
                            CategoryProductId = categoryProduct.Id,
                            IntroduceModel = item.IntroduceModel,
                            IntroduceSize = item.IntroduceSize,
                            IntroduceCapacity = item.IntroduceCapacity,
                            ImgUrl = item.imgUrl
                        };
                        await DbScoped.SugarScope.Insertable(pi).ExecuteCommandAsync();
                    }
                }

                return entity;
            }
            return entity;
        }

        /// <summary>
        /// 删除分类产品
        /// </summary>
        /// <param name="categoryProduct"></param>
        /// <returns></returns>
        public async Task<bool> DeleteCategoryProduct(CategoryProduct categoryProduct)
        {
            CategoryProduct entity = await _categoryProductRepository.GetCategoryProductById(categoryProduct.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.CategoryProductNoFoundError.ToDescriptionName(), ErrorCode.CategoryProductNoFoundError.GetHashCode());
            }

            if (entity.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.CategoryProductDeleteReleaseStatusError.ToDescriptionName(), ErrorCode.CategoryProductDeleteReleaseStatusError.GetHashCode());
            }

            entity.IsDeleted = categoryProduct.IsDeleted;
            entity.ModifierId = categoryProduct.ModifierId;
            entity.UpdatedBy = categoryProduct.UpdatedBy;
            entity.UpdatedTime = categoryProduct.UpdatedTime;

            var result = await _categoryProductRepository.Update(entity);
            if (result)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取分类产品详情
        /// </summary>
        /// <param name="categoryProductId">分类产品Id</param>
        /// <returns></returns>
        public async Task<GetCategoryProductResponse> GetCategoryProductDetail(long categoryProductId)
        {
            CategoryProduct categoryProduct = await _categoryProductRepository.GetCategoryProductById(categoryProductId);
            if (categoryProduct == null)
            {
                throw new BusinessException(ErrorCode.CategoryProductNoFoundError.ToDescriptionName(), ErrorCode.CategoryProductNoFoundError.GetHashCode());
            }

            GetCategoryProductResponse result = new GetCategoryProductResponse()
            {
                CategoryProductId = categoryProduct.Id,
                ProductLanguage = categoryProduct.ProductLanguage,
                CategoryId = categoryProduct.CategoryId,
                ProductName = categoryProduct.ProductName,
                //ProductTexture = categoryProduct.ProductTexture,
                //ProductColor = categoryProduct.ProductColor,
                //ProductSpecification = categoryProduct.ProductSpecification,
                ProductIntroduce = categoryProduct.ProductIntroduce,
                ReleaseStatus = categoryProduct.ReleaseStatus,
            };

            var files = await _fileRepository.GetFileList(new List<long> { categoryProductId }, EnumRelationType.CategoryProductPicture);
            List<FileResponse> pictureList = new List<FileResponse>();
            foreach (var item in files)
            {
                FileResponse file = new FileResponse();
                file.FileId = item.FileId;
                file.FileName = item.Name;
                file.Url = item.Url;

                pictureList.Add(file);
            }
            result.ProductPicture = pictureList;

            files = await _fileRepository.GetFileList(new List<long> { categoryProductId }, EnumRelationType.CategoryProductCornerMarkPicture);
            List<FileResponse> cornerMarkList = new List<FileResponse>();
            foreach (var item in files)
            {
                FileResponse file = new FileResponse();
                file.FileId = item.FileId;
                file.FileName = item.Name;
                file.Url = item.Url;

                cornerMarkList.Add(file);
            }
            result.ProductCornerMarkPicture = cornerMarkList;

            result.IntroduceList = await _categoryProductIntroduceRepository.GetList(categoryProductId);

            return result;
        }

        /// <summary>
        /// 发布分类产品
        /// </summary>
        /// <param name="categoryProductId">分类产品Id</param>
        /// <param name="status">发布状态</param>
        /// <param name="modifierId">修改人id</param>
        /// <param name="updatedBy">修改人</param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public async Task<bool> ReleaseCategoryProduct(long categoryProductId, int status, string modifierId, string updatedBy)
        {
            CategoryProduct entity = await _categoryProductRepository.GetCategoryProductById(categoryProductId);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.CategoryProductNoFoundError.ToDescriptionName(), ErrorCode.CategoryProductNoFoundError.GetHashCode());
            }

            if (entity.ReleaseStatus == 0 && status == 0)
            {
                throw new BusinessException(ErrorCode.CategoryProductReleaseStatusDowmError.ToDescriptionName(), ErrorCode.CategoryProductReleaseStatusDowmError.GetHashCode());
            }

            if (entity.ReleaseStatus == 1 && status == 1)
            {
                throw new BusinessException(ErrorCode.CategoryProductReleaseStatusUpError.ToDescriptionName(), ErrorCode.CategoryProductReleaseStatusUpError.GetHashCode());
            }

            entity.ReleaseStatus = status;
            if (status == 1)
            {
                entity.ReleaseTime = DateTime.Now;
                int sort = await _categoryProductRepository.GetMaxSort(entity.CategoryId, entity.ProductLanguage);
                entity.Sort = sort;
            }
            else
            {
                entity.Sort = null;
            }

            entity.UpdatedBy = updatedBy;
            entity.ModifierId = modifierId;
            entity.UpdatedTime = DateTime.Now;

            var result = await _categoryProductRepository.Update(entity);
            if (result)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取分类产品列表
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="productName">商品名称</param>
        /// <param name="times">创建时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageRows<CategoryProductListResponse>> GetCategoryProductPageList(int? productLanguage, string productName, int? ReleaseStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            return await _categoryProductRepository.GetCategoryProductPageList(productLanguage, productName, ReleaseStatus, times, orderFile, sortType, pageIndex, pageSize);
        }

        /// <summary>
        /// 获取所有未删除的分类产品
        /// </summary>
        /// <returns></returns>
        public async Task<List<CategoryProduct>> GetAllCategoryProductList()
        {
            return await _categoryProductRepository.GetEntityList(x => !x.IsDeleted);
        }

        /// <summary>
        /// 根据分类Id获取已发布的分类商品
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="categoryId">分类id</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageRows<GetWebCategoryProductListResponse>> GetCategoryProductListByCategoryId(long? categoryId, int? productLanguage, int pageIndex = 1, int pageSize = 10)
        {
            return await _categoryProductRepository.GetCategoryProductListByCategoryId(categoryId, productLanguage, pageIndex, pageSize);
        }

        /// <summary>
        /// 根据分类Id获取已发布的分类商品(并按sort排序)
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        public async Task<GetCategoryProductListOrderSortResponse> GetCategoryProductListByCategoryId(long categoryId)
        {
            return await _categoryProductRepository.GetCategoryProductListByCategoryId(categoryId);
        }

        /// <summary>
        /// 修改产品排序
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="list">排序商品</param>
        /// <returns></returns>
        public async Task<bool> EditCategoryProductSort(long categoryId, int productLanguage, List<CategoryProduct> list)
        {
            List<int?> sortList = list.Select(x => x.Sort).Distinct().ToList();
            if (sortList.Count != list.Count)
            {
                throw new BusinessException(ErrorCode.SidebarSortError.ToDescriptionName(), ErrorCode.SidebarSortError.GetHashCode());
            }

            Domain.Entity.Category category = await _categoryService.GetCategory(categoryId);
            if (category == null)
            {
                throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
            }

            List<CategoryProduct> updateList = new List<CategoryProduct>();

            var categoryProductIdList = list.Select(x => x.Id).ToList();
            List<CategoryProduct> cpList = await _categoryProductRepository.GetEntityList(x => x.CategoryId == categoryId && x.ProductLanguage == productLanguage && x.ReleaseStatus == 1 && categoryProductIdList.Contains(x.Id));
            foreach (CategoryProduct categoryProduct in list)
            {
                CategoryProduct entity = cpList.FirstOrDefault(x => x.Id == categoryProduct.Id);
                if (entity == null)
                {
                    throw new BusinessException($"{categoryProduct.Id}:{ErrorCode.CategoryProductNoFoundError.ToDescriptionName()}", ErrorCode.CategoryProductNoFoundError.GetHashCode());
                }
                entity.Sort = categoryProduct.Sort;
                updateList.Add(entity);
            }

            return await _categoryProductRepository.Update(updateList);
        }

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <param name="categoryProductId">分类产品Id</param>
        /// <param name="ModifierId">修改人Id</param>
        /// <param name="UpdatedBy">修改人名称</param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public async Task<bool> EditProductCategory(long categoryId, long categoryProductId, string ModifierId, string UpdatedBy)
        {
            Domain.Entity.Category category = await _categoryService.GetCategory(categoryId);
            if (category == null)
            {
                throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
            }
            if (category.Level != 2)
            {
                throw new BusinessException(ErrorCode.CategoryProductAddCategoryError.ToDescriptionName(), ErrorCode.CategoryProductAddCategoryError.GetHashCode());
            }

            CategoryProduct entity = await _categoryProductRepository.GetCategoryProductById(categoryProductId);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.CategoryProductNoFoundError.ToDescriptionName(), ErrorCode.CategoryProductNoFoundError.GetHashCode());
            }
            int sort = await _categoryProductRepository.GetMaxSort(categoryId, entity.ProductLanguage);
            entity.Sort = sort;
            entity.CategoryId = categoryId;
            entity.ModifierId = ModifierId;
            entity.UpdatedBy = UpdatedBy;
            entity.UpdatedTime = DateTime.Now;

            var result = await _categoryProductRepository.Update(entity);
            if (result)
            {
                return true;
            }
            return false;
        }

    }
}
