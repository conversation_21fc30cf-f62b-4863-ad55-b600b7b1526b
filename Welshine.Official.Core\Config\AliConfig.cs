﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Core.Config
{
    /// <summary>
    /// 阿里OSS库对接
    /// </summary>
    public class AliOssSettings
    {
        /// <summary>
        /// 地域节点
        /// </summary>
        public string endpoint { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string bucketName { get; set; }
        /// <summary>
        /// AccessKey ID
        /// </summary>
        public string accessKeyId { get; set; }

        /// <summary>
        /// AccessKey Secret
        /// </summary>
        public string accessKeySecret { get; set; }
        /// <summary>
        ///.png,.jpg,.txt
        /// </summary>
        public string allowType { get; set; } = ".png,.jpg,.txt,.pdf,.jpeg,.image";
        /// <summary>
        /// 以M为单位
        /// </summary>
        public int mLength { get; set; } = 5;
        /// <summary>
        /// 加密的地域节点
        /// </summary>
        public string privateEndpoint { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string bucketPrivateName { get; set; }

        public string arn { get; set; }

        public long durationSeconds { get; set; }
    }
}
