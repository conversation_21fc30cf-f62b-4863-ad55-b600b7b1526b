﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Newtonsoft.Json;
using System.Buffers;
using System.Threading.Tasks;

namespace Welshine.Official.Core
{
    /// <summary>
    /// 
    /// </summary>
    public class CustomJsonInputFormatter : NewtonsoftJsonInputFormatter
    {
        ILogger logger;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="serializerSettings"></param>
        /// <param name="charPool"></param>
        /// <param name="objectPoolProvider"></param>
        /// <param name="options"></param>
        /// <param name="jsonOptions"></param>
        public CustomJsonInputFormatter(ILogger logger, JsonSerializerSettings serializerSettings, ArrayPool<char> charPool,
            ObjectPoolProvider objectPoolProvider,
            MvcOptions options, MvcNewtonsoftJsonOptions jsonOptions)
            : base(logger, serializerSettings, charPool, objectPoolProvider, options, jsonOptions)
        {
            this.logger = logger;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<InputFormatterResult> ReadRequestBodyAsync(InputFormatterContext context)
        {
            var result = await base.ReadRequestBodyAsync(context);
            string typeName = context.ModelType.FullName;
            foreach (var key in context.ModelState.Keys)
            {
                string ErrMsg = "";
                for (int i = 0; i < context.ModelState[key].Errors.Count; i++)
                {

                    var error = context.ModelState[key].Errors[i];
                    ErrMsg = error.ErrorMessage;
                    // context.ModelState[key].Errors.Remove(error);
                    //context.ModelState[key].Errors.Add($"This is translated error { error.ErrorMessage }");
                    context.ModelState[key].Errors.Clear();
                }
                logger.LogError($"传入参数格式错误。ModelType:{typeName} key:{key} ErrMsg:{ErrMsg}");
                context.ModelState[key].Errors.Add($"请求报文出错.");
            }
            return result;
        }

    }
}
