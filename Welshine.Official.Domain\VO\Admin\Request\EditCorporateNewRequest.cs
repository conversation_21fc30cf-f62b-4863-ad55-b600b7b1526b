﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class EditCorporateNewRequest
    {
        /// <summary>
        /// 资讯id
        /// </summary>
        [Required(ErrorMessage = "资讯id为必填")]
        public long? NewId { get; set; }
        /// <summary>
        /// 中文企业资讯
        /// </summary>
        [Required(ErrorMessage = "中文企业资讯为必填")]
        public EditCorporateNewItem CNItem { get; set; }
        /// <summary>
        /// 英文企业资讯
        /// </summary>
        [Required(ErrorMessage = "英文企业资讯为必填")]
        public EditCorporateNewItem ENItem { get; set; }

        /// <summary>
        /// 文章栏目: 0->企业快讯;1->新品上市;
        /// </summary>
        [Required(ErrorMessage = "文章栏目为必填")]
        [Range(0, 1, ErrorMessage = "文章栏目参数只能为 0->企业快讯; 1->新品上市")]
        public int? NewColumn { get; set; }

        /// <summary>
        /// 热点推荐: 0->不推荐;1->推荐;
        /// </summary>
        [Required(ErrorMessage = "热点推荐为必填")]
        [Range(0, 1, ErrorMessage = "热点推荐参数只能为 0->不推荐; 1->推荐")]
        public int? NewRecommend { get; set; }

        /*/// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [Required(ErrorMessage = "内容状态为必填")]
        [Range(0, 1, ErrorMessage = "内容状态参数只能为 0->未发布; 1->已发布")]
        public int? ReleaseStatus { get; set; }*/
        /*/// <summary>
        /// 中文版本标题
        /// </summary>
        [StringLength(100, ErrorMessage = "中文标题最多为100字")]
        public string CNTitle { get; set; }

        /// <summary>
        /// 中文文章内容
        /// </summary>
        [StringLength(20000, ErrorMessage = "中文文章内容最多为20000字")]
        public string CNContent { get; set; }

        /// <summary>
        /// 中文文章大图
        /// </summary>
        [RegularExpression(@"^[\s\S]*\.(jpeg|jpg|png)$", ErrorMessage = "中文文章大图仅支持jpg，jpeg，png格式图片")]
        public string CNImage { get; set; }

        /// <summary>
        /// 英文版本标题
        /// </summary>
        [StringLength(300, ErrorMessage = "英文标题最多为300字")]
        public string ENTitle { get; set; }

        /// <summary>
        /// 英文文章内容
        /// </summary>
        [StringLength(20000, ErrorMessage = " 英文文章内容最多为20000字")]
        public string ENContent { get; set; }

        /// <summary>
        /// 英文文章大图
        /// </summary>
        [RegularExpression(@"^[\s\S]*\.(jpeg|jpg|png)$", ErrorMessage = "英文文章大图仅支持jpg，jpeg，png格式图片")]
        public string ENImage { get; set; }*/
    }

    public class EditCorporateNewItem
    {
        /// <summary>
        /// 版本标题
        /// </summary>
        [Required(ErrorMessage = "文章标题为必填")]
        public string NewTitle { get; set; }

        /// <summary>
        /// 文章内容
        /// </summary>
        [Required(ErrorMessage = "文章内容为必填")]
        [StringLength(20000, ErrorMessage = " 文章内容最多为20000字")]
        public string NewContent { get; set; }

        /// <summary>
        /// 文章大图
        /// </summary>
        [Required(ErrorMessage = "文章大图为必填")]
        //[RegularExpression(@"^[\s\S]*\.(jpeg|jpg|png)$", ErrorMessage = "文章大图仅支持jpg，jpeg，png格式图片")]
        public string NewImage { get; set; }
        /// <summary>
        /// 发布时间
        /// </summary>
        [Required(ErrorMessage ="发布时间为必填")]
        public DateTime? PublishDate { get; set; }

        /// <summary>
        /// 文章摘要
        /// </summary>
        [Required(ErrorMessage = "文章摘要为必填")]
        [StringLength(200, ErrorMessage = "文章摘要最多为200字")]
        public string Description { get; set; }
    }
}
