﻿using SqlSugar.IOC;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Repository;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 留言区服务接口实现
    /// </summary>
    public class MessageBoardService : IMessageBoardService
    {
        private readonly IMessageBoardRepository _messageBoardRepository;

        public MessageBoardService(IMessageBoardRepository messageBoardRepository)
        {
            _messageBoardRepository = messageBoardRepository;
        }

        /// <summary>
        /// 获取留言区列表
        /// </summary>
        /// <param name="phone">电话</param>
        /// <param name="times">提交时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageRows<MessageBoardPageListResponse>> GetMessageBoardPageList(string phone, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            return await _messageBoardRepository.GetMessageBoardPageList(phone, times, orderFile, sortType, pageIndex, pageSize);
        }

        /// <summary>
        /// 获取留言区列表(不分页)
        /// </summary>
        /// <param name="phone">电话</param>
        /// <param name="times">提交时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<List<MessageBoardPageListResponse>> GetMessageBoardList(string phone, TimeHorizon times)
        {
            return await _messageBoardRepository.GetMessageBoardList(phone, times);
        }

        /// <summary>
        /// 添加留言
        /// </summary>
        /// <param name="messageBoard"></param>
        /// <returns></returns>
        public async Task<bool> AddMessageBoard(MessageBoard messageBoard)
        {
            return await _messageBoardRepository.AddMessageBoard(messageBoard);
        }

    }
}
