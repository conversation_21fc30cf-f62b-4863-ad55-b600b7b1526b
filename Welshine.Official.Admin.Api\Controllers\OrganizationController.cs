﻿
using DocumentFormat.OpenXml.Drawing.Diagrams;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Service;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 组织管理
    /// </summary>
    public class OrganizationController : BaseApiController
    {
        private readonly OrganizationService _organizationService = new OrganizationService();

       /* OrganizationController()
        {
            _organizationService = new OrganizationService();
        }*/
        /// <summary>
        /// 添加部门
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse AddDepartment([FromBody] BaseRequest<AddDepartmentRequest> request)
        {
            var currentUser = GetUserInfo();
            _organizationService.AddDepartment(request.Body.DepartmentName, currentUser);
            return Success("添加成功");
        }
        /// <summary>
        /// 编辑部门
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse EditDepartment([FromBody] BaseRequest<EditDepartmentRequest> request)
        {
            var currentUser = GetUserInfo();
            var body = request.Body;
            _organizationService.EditDepartment(body.DepartmentId.Value, body.DepartmentName, currentUser);
            return Success("修改成功");
        }
        /// <summary>
        /// 删除部门
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse DeleteDepartment([FromBody] BaseRequest<DeleteDepartmentRequest> request)
        {
            _organizationService.DeleteDepartment(request.Body.DepartmentId.Value);
            return Success("删除成功");
        }

        /// <summary>
        /// 获取部门列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<List<Department>> GetDepartmentList()
        {
            var departmentList = _organizationService.GetDepartments();
            return Success<List<Department>>("请求成功", departmentList);
        }
        /// <summary>
        /// 获取岗位列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<List<Post>> GetPostList([FromBody] BaseRequest<GetPostListRequest> request)
        {
            var postList = _organizationService.GetPosts(request.Body.DepartmentId.Value);
            return Success("请求成功", postList);
        }
        /// <summary>
        /// 保存岗位列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse SavePostList([FromBody] BaseRequest<SavePostListRequest> request)
        {
            var currentUser = GetUserInfo();
            _organizationService.SaveDepartmentPosts(request.Body.DepartmentId.Value, request.Body.PostNameList, currentUser);
            return Success("保存成功");
        }
    }
}
