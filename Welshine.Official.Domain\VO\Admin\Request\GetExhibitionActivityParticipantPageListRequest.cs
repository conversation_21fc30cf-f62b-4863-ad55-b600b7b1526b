﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class GetExhibitionActivityParticipantPageListRequest
    {
        /// <summary>
        /// 展会活动Id
        /// </summary>
        [Required(ErrorMessage = "展会活动Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "展会活动Id参数错误")]
        public long ExhibitionActivityId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [StringLength(5, ErrorMessage = "姓名长度不符", MinimumLength = 1)]
        public string Name { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [StringLength(11, ErrorMessage = "电话长度不符", MinimumLength = 1)]
        public string Mobile { get; set; }

        /// <summary>
        /// 券码
        /// </summary>
        [StringLength(8, ErrorMessage = "券码长度不符", MinimumLength = 1)]
        public string Code { get; set; }

        /// <summary>
        /// 参与时间
        /// </summary>
        public TimeHorizon CreatedTimeScope { get; set; }

        /// <summary>
        /// 类型:0->经销;1->终端;
        /// </summary>
        [Range(0, 1, ErrorMessage = "类型错误")]
        public int? Type { get; set; }

        /// <summary>
        /// 是否现场客户:0->否;1->是;
        /// </summary>
        [Range(0, 1, ErrorMessage = "是否现场客户错误")]
        public int? IsScene { get; set; }

    }
}
