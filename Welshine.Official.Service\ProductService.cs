﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 产品服务实现
    /// </summary>
    public class ProductService : IProductService
    {
        private readonly IProductRepository _productRepository;
        private readonly IFileRepository _fileRepository;

        public ProductService(IProductRepository productRepository, IFileRepository fileRepository)
        {
            _productRepository = productRepository;
            _fileRepository = fileRepository;
        }

        /// <summary>
        /// 添加产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> AddProduct(Product product, string thumbnail, List<string> detailPicture)
        {
            await AddProductVaild(product.ProductName, thumbnail, detailPicture);

            var result = await _productRepository.AddProduct(product, thumbnail, detailPicture);

            return result;
        }

        /// <summary>
        /// 添加产品分类校验
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task AddProductVaild(string productName, string thumbnail, List<string> detailPicture)
        {
            //总数校验
            var count = await _productRepository.GetCount();
            if (count >= 21)
            {
                throw new BusinessException(ErrorCode.ProductCountError.ToDescriptionName(), ErrorCode.ProductCountError.GetHashCode());
            }

            //产品名称校验
            var result = await _productRepository.ExistsProductName(productName);
            if (result)
            {
                throw new BusinessException(ErrorCode.ProductNameError.ToDescriptionName(), ErrorCode.ProductNameError.GetHashCode());
            }

            //图片校验
            var fileList = await _fileRepository.GetList(new List<string>() { thumbnail }.Union(detailPicture).ToList());
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //略缩图校验
            result = await _fileRepository.Exists(thumbnail);
            if (!result)
            {
                throw new BusinessException(ErrorCode.ProductThumbnailNotFind.ToDescriptionName(), ErrorCode.ProductThumbnailNotFind.GetHashCode());
            }

            //详情图校验
            if (detailPicture.Count > 5)
            {
                throw new BusinessException(ErrorCode.ProductDetailPictureLimitError.ToDescriptionName(), ErrorCode.ProductDetailPictureLimitError.GetHashCode());
            }
            foreach (var item in detailPicture)
            {
                result = await _fileRepository.Exists(item);
                if (!result)
                {
                    throw new BusinessException(ErrorCode.ProductDetailPictureNotFind.ToDescriptionName(), ErrorCode.ProductDetailPictureNotFind.GetHashCode());
                }
            }
        }

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="DetailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> EditProduct(Product product, string thumbnail, List<string> detailPicture)
        {
            var result = false;

            await EditProductVaild(product.ProductName, product.Id, thumbnail, detailPicture);

            var entity = await _productRepository.GetProduct(product.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.ProductNotFind.ToDescriptionName(), ErrorCode.ProductNotFind.GetHashCode());
            }

            entity.ProductName = product.ProductName;
            entity.UpdatedTime = DateTime.Now;
            entity.ModifierId = product.ModifierId;
            entity.UpdatedBy = product.UpdatedBy;

            await _productRepository.EditProduct(entity, thumbnail, detailPicture);

            return result;
        }

        /// <summary>
        /// 修改产品分类校验
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="productId">产品Id</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task EditProductVaild(string productName, long productId, string thumbnail, List<string> detailPicture)
        {
            //产品名称校验
            var result = await _productRepository.ExistsProductName(productName, productId);
            if (result)
            {
                throw new BusinessException(ErrorCode.ProductNameError.ToDescriptionName(), ErrorCode.ProductNameError.GetHashCode());
            }

            //图片校验
            var fileList = await _fileRepository.GetList(new List<string>() { thumbnail }.Union(detailPicture).ToList());
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //略缩图校验
            result = await _fileRepository.Exists(thumbnail);
            if (!result)
            {
                throw new BusinessException(ErrorCode.ProductThumbnailNotFind.ToDescriptionName(), ErrorCode.ProductThumbnailNotFind.GetHashCode());
            }

            //详情图校验
            if (detailPicture.Count > 5)
            {
                throw new BusinessException(ErrorCode.ProductDetailPictureLimitError.ToDescriptionName(), ErrorCode.ProductDetailPictureLimitError.GetHashCode());
            }
            foreach (var item in detailPicture)
            {
                result = await _fileRepository.Exists(item);
                if (!result)
                {
                    throw new BusinessException(ErrorCode.ProductDetailPictureNotFind.ToDescriptionName(), ErrorCode.ProductDetailPictureNotFind.GetHashCode());
                }
            }
        }

        /// <summary>
        /// 获取产品详情
        /// </summary>
        /// <param name="productId">产品Id</param>
        /// <returns></returns>
        public async Task<ProductDetailResponse> GetProductById(long productId)
        {
            var entity = await _productRepository.GetProduct(productId);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.ProductNotFind.ToDescriptionName(), ErrorCode.ProductNotFind.GetHashCode());
            }

            ProductDetailResponse result = new ProductDetailResponse()
            {
                ProductId = entity.Id,
                ProductName = entity.ProductName,
                ProductCode = entity.ProductCode
            };

            var files = await _fileRepository.GetFileList(new List<long> { productId });

            var thumbnail = files.Where(x => x.TableEnum == EnumRelationType.ProductThumbnail).FirstOrDefault();
            if (thumbnail != null)
            {
                result.Thumbnail = new Domain.VO.FileResponse() { FileId = thumbnail.FileId, FileName = thumbnail.Name, Url = thumbnail.Url };
            }

            var detailPicture = files.Where(x => x.TableEnum == EnumRelationType.ProductDetailPicture).OrderBy(x => x.CreatedTime).ToList();
            List<FileResponse> pictureList = new List<FileResponse>();
            foreach (var item in detailPicture)
            {
                FileResponse file = new FileResponse();
                file.FileId = item.FileId;
                file.FileName = item.Name;
                file.Url = item.Url;

                pictureList.Add(file);
            }
            result.DetailPicture = pictureList;

            return result;
        }

        /// <summary>
        /// 删除产品分类
        /// </summary>
        /// <param name="productId">产品信息</param>
        /// <returns></returns>
        public async Task<bool> DeleteProduct(Product product)
        {
            var entity = await _productRepository.GetProduct(product.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.ProductNotFind.ToDescriptionName(), ErrorCode.ProductNotFind.GetHashCode());
            }

            return await _productRepository.DeleteProduct(product);
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<ProductListReponse>> GetProductPageList(string productName, int pageIndex = 1, int pageSize = 10)
        {
            return await _productRepository.GetProductPageList(productName, pageIndex, pageSize);
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<List<WXProductListReponse>> WX_GetProductPageList()
        {
            return await _productRepository.WX_GetProductPageList();
        }
    }
}
