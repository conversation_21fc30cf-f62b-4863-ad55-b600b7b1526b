﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    [SqlSugar.SugarTable("cms_corporate_new")]
    public class CorporateNew:BaseBigEntity
    {
        /// <summary>
        /// 中文版本标题
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "cn_title")]
        public string CNTitle { get; set; }

        /// <summary>
        /// 中文文章内容
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "cn_content")]
        public string CNContent { get; set; }

        /// <summary>
        /// 中文文章大图
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "cn_image")]
        public string CNImage { get; set; }

        /// <summary>
        /// 英文版本标题
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "en_title")]
        public string ENTitle { get; set; }

        /// <summary>
        /// 英文文章内容
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "en_content")]
        public string ENContent { get; set; }

        /// <summary>
        /// 英文文章大图
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "en_image")]
        public string ENImage { get; set; }

        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "release_status")]
        public int ReleaseStatus { get; set; }
        /// <summary>
        /// 中文发布日期
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "cn_publish_date")]
        public DateTime? CNPublishDate { get; set; }
        /// <summary>
        /// 英文发布日期
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "en_publish_date")]
        public DateTime? ENPublishDate { get; set; }

        /// <summary>
        /// 文章栏目: 0->企业快讯;1->新品上市;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "new_column")]
        public int NewColumn { get; set; }

        /// <summary>
        /// 热点推荐: 0->不推荐;1->推荐;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "new_recommend")]
        public int NewRecommend { get; set; }

        /// <summary>
        /// 中文文章摘要
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "cn_description")]
        public string CNDescription { get; set; }

        /// <summary>
        /// 英文文章摘要
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "en_description")]
        public string ENDescription { get; set; }
    }
}
