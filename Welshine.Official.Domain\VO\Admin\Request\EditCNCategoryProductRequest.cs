﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class EditCNCategoryProductRequest : AddCNCategoryProductRequest
    {
        /// <summary>
        /// 分类产品Id
        /// </summary>
        [Required(ErrorMessage = "分类产品Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "分类产品Id参数错误")]
        public long? CategoryProductId { get; set; }
    }
}
