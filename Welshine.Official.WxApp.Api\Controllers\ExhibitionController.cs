﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 展会管理
    /// </summary>
    public class ExhibitionController : BaseApiController
    {
        private readonly IExhibitionService _exhibitionService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="exhibitionService"></param>
        public ExhibitionController(IExhibitionService exhibitionService)
        {
            _exhibitionService = exhibitionService;
        }

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<WXExhibitionListReponse>>> GetExhibitionPageList([FromBody] RequestPageModel request)
        {
            PageRows<WXExhibitionListReponse> pageRows = null;
            try
            {
                pageRows = await _exhibitionService.WX_GetExhibitionPageList(request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 获取展会详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2011">展会Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<WXExhibitionDetailResponse>> GetExhibitionById([FromBody] BaseRequest<ExhibitionIdRequest> request)
        {
            WXExhibitionDetailResponse result = null;
            try
            {
                result = await _exhibitionService.WX_GetExhibitionById(request.Body.ExhibitionId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionById Error {u}", ex.Message);
                return Failure<WXExhibitionDetailResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }
    }
}
