# 打印机小程序服务端功能调整说明

## 功能概述

本次调整为打印机小程序服务端增加了**系统默认标签模版**分类管理功能，与现有的"用户自定义标签模板"完全分离，包括：

1. **获取系统标签模版分类列表接口** - 返回所有可用的系统模版分类
2. **通过分类获取系统标签模版接口** - 支持按分类筛选系统预设模版，支持分页
3. **搜索系统标签模版接口** - 支持根据标签尺寸、模版名称与适用场景查询系统标签

## 重要说明

- **系统模版** vs **用户模版**：
  - 系统模版：由系统预设的标准模版，所有用户共享，只读
  - 用户模版：用户自定义创建的模版，与OpenId关联，可增删改
- 本次新增功能专门处理系统模版，不影响现有的用户自定义模版功能

## 新增功能详情

### 1. 获取系统标签模版分类接口

**接口路径：** `POST /PrinterManager/GetSystemLabelTemplateCategories`

**请求参数：**
```json
{
  "Head": {
    "Message": "",
    "Code": "",
    "CallTime": ""
  },
  "Body": {}
}
```

**响应内容：**
```json
{
  "Head": {
    "Message": "获取成功",
    "Code": "200",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": [
    {
      "CategoryId": 1,
      "CategoryName": "通用标签"
    },
    {
      "CategoryId": 2,
      "CategoryName": "商品标签"
    }
  ]
}
```

### 2. 通过分类获取系统标签模版接口

**接口路径：** `POST /PrinterManager/GetSystemLabelTemplatesByCategory`

**请求参数：**
```json
{
  "Head": {
    "Message": "",
    "Code": "",
    "CallTime": ""
  },
  "RequestParams": {
    "CategoryId": 1  // 可选，为空则获取全部系统模版
  },
  "PageIndex": 1,
  "PageSize": 10
}
```

**响应内容：**
```json
{
  "Head": {
    "Message": "获取成功",
    "Code": "200",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Total": 25,
    "Data": [
      {
        "TemplateId": 1,
        "TemplateName": "存储标签1",
        "TemplateContent": "{\"TemplateName\":\"存储标签1\",...}",
        "TemplateThumbnail": "base64图片数据",
        "TemplateDescription": "存储标签模版，包含品名、操作人、日期字段",
        "LabelSize": "50x30mm",
        "SceneTags": "存储,仓库,管理"
      }
    ]
  }
}
```

### 3. 搜索系统标签模版接口

**接口路径：** `POST /PrinterManager/SearchSystemLabelTemplates`

**请求参数：**
```json
{
  "Head": {
    "Message": "",
    "Code": "",
    "CallTime": ""
  },
  "RequestParams": {
    "TemplateName": "存储",      // 可选，模版名称模糊搜索
    "LabelSize": "50x30mm",      // 可选，标签尺寸精确匹配
    "SceneTags": "存储,仓库",    // 可选，适用场景标签模糊搜索，支持多关键词
    "CategoryId": 1              // 可选，分类ID筛选
  },
  "PageIndex": 1,
  "PageSize": 10
}
```

**响应内容：** 与分类获取接口相同的响应格式

## 数据库变更

### 1. 新增系统标签模版分类表

执行脚本：`database_scripts/create_label_template_category_table.sql`

**表名：** `ext_label_template_category`
**表结构：**
- `id` - 主键ID
- `category_name` - 分类名称
- `category_description` - 分类描述
- `sort` - 排序
- `is_enabled` - 是否启用
- 其他基础字段（创建时间、更新时间、删除标记等）

**默认数据：**
- 通用标签
- 商品标签
- 地址标签
- 价格标签
- 条码标签

### 2. 新增系统标签模版表

执行脚本：`database_scripts/create_system_label_template_table.sql`

**表名：** `ext_system_label_template`
**表结构：**
- `id` - 主键ID
- `template_name` - 模版名称
- `template_content` - 模版内容（JSON）
- `template_thumbnail` - 模版缩略图（Base64）
- `category_id` - 分类ID（外键）
- `template_description` - 模版描述
- `sort` - 排序
- `is_enabled` - 是否启用
- `label_size` - 标签尺寸
- `scene_tags` - 适用场景标签
- 其他基础字段

**预设数据：**
包含6个真实模版：
- 存储标签1 (50x30mm) - 品名、操作人、日期
- 存储标签2 (50x30mm) - 品名、存储量、责任人
- 食品留样标签1 (50x30mm) - 餐次、品名、时间、留样人
- 食品留样标签2 (50x30mm) - 餐次、菜品、重量、时间、留样人、制作人
- 物料/区域标签1 (50x30mm) - 中英文双语显示
- 物料/区域标签2 (50x30mm) - 带边框的中文标识

## 代码变更说明

### 1. 实体类变更

- **新增：** `LabelTemplateCategory.cs` - 系统标签模版分类实体
- **新增：** `SystemLabelTemplate.cs` - 系统标签模版实体
- **保持不变：** `LabelTemplate.cs` - 用户自定义模版实体（未修改）

### 2. 请求响应模型

- **新增：** `SystemLabelTemplateResponse.cs` - 系统模版响应模型
- **新增：** `SearchSystemLabelTemplatesRequest.cs` - 搜索请求模型
- **修改：** `LabelRequests.cs` - 添加系统模版接口的请求模型
- **保持：** `LabelTemplateCategoryResponse.cs` - 分类响应模型

### 3. 服务层变更

- **修改：** `ILabelService.cs` - 添加系统模版相关方法定义
- **修改：** `LabelService.cs` - 实现系统模版业务逻辑

### 4. 控制器变更

- **修改：** `PrinterManagerController.cs` - 添加三个新的系统模版API接口

## 部署说明

1. **数据库更新：**
   ```sql
   -- 执行分类表建表脚本
   source database_scripts/create_label_template_category_table.sql;
   
   -- 执行系统模版表建表脚本
   source database_scripts/create_system_label_template_table.sql;
   ```

2. **代码部署：**
   - 编译并部署更新后的代码
   - 确保所有依赖项正确引用

3. **测试验证：**
   - 测试获取系统模版分类列表接口
   - 测试按分类获取系统模版接口
   - 测试搜索系统模版接口
   - 验证分页功能正常
   - 确认与用户自定义模版功能不冲突

## 注意事项

1. **数据隔离**：系统模版与用户自定义模版完全分离，互不影响
2. **只读性质**：系统模版为只读，用户无法修改，只能查看和使用
3. **分类查询**：只返回启用状态的分类和模版
4. **排序规则**：系统模版按sort字段和ID排序，保证显示顺序稳定
5. **搜索功能**：支持多条件组合搜索，提高模版查找效率
6. **异常处理**：所有接口都包含完整的异常处理和日志记录
7. **兼容性**：不影响现有的用户自定义模版功能

## 数据表关系

```
ext_label_template_category (分类表)
    ↓ (一对多)
ext_system_label_template (系统模版表)

ext_label_template (用户模版表) - 独立存在，不关联分类
```

## 后续扩展建议

1. **后台管理**：可添加系统模版的后台管理界面（增删改查）
2. **模版预览**：可添加模版预览功能，展示缩略图
3. **使用统计**：可统计各模版的使用频率
4. **模版导入**：可支持批量导入系统模版
5. **分类管理**：可添加分类的后台管理功能
6. **模版标签**：可基于SceneTags实现标签搜索功能
7. **更多模版**：后续可继续添加更多预设模版（目前已提供6个）
