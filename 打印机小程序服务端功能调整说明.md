# 打印机小程序服务端功能调整说明

## 功能概述

本次调整为打印机小程序服务端增加了标签模版分类管理功能，包括：

1. **获取标签模版分类列表接口** - 返回所有可用的标签模版分类
2. **通过分类获取标签模版接口** - 支持按分类筛选标签模版，支持分页

## 新增功能详情

### 1. 获取标签模版分类接口

**接口路径：** `POST /PrinterManager/GetLabelTemplateCategories`

**请求参数：**
```json
{
  "Head": {
    "Message": "",
    "Code": "",
    "CallTime": ""
  },
  "Body": {}
}
```

**响应内容：**
```json
{
  "Head": {
    "Message": "获取成功",
    "Code": "200",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": [
    {
      "CategoryId": 1,
      "CategoryName": "通用标签"
    },
    {
      "CategoryId": 2,
      "CategoryName": "商品标签"
    }
  ]
}
```

### 2. 通过分类获取标签模版接口

**接口路径：** `POST /PrinterManager/GetLabelTemplatesByCategory`

**请求参数：**
```json
{
  "Head": {
    "Message": "",
    "Code": "",
    "CallTime": ""
  },
  "RequestParams": {
    "CategoryId": 1  // 可选，为空则获取全部模版
  },
  "PageIndex": 1,
  "PageSize": 10
}
```

**响应内容：**
```json
{
  "Head": {
    "Message": "获取成功",
    "Code": "200",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Total": 25,
    "Data": [
      {
        "TemplateId": 1,
        "TemplateName": "商品价格标签",
        "TemplateContent": "{\"width\":100,\"height\":50,...}"
      }
    ]
  }
}
```

## 数据库变更

### 1. 新增标签模版分类表

执行脚本：`database_scripts/create_label_template_category_table.sql`

**表结构：**
- `id` - 主键ID
- `category_name` - 分类名称
- `category_description` - 分类描述
- `sort` - 排序
- `is_enabled` - 是否启用
- 其他基础字段（创建时间、更新时间、删除标记等）

**默认数据：**
- 通用标签
- 商品标签
- 地址标签
- 价格标签
- 条码标签

### 2. 修改标签模版表

执行脚本：`database_scripts/alter_label_template_table.sql`

**新增字段：**
- `template_name` - 模版名称
- `category_id` - 分类ID（外键关联到分类表）

## 代码变更说明

### 1. 实体类变更

- **新增：** `LabelTemplateCategory.cs` - 标签模版分类实体
- **修改：** `LabelTemplate.cs` - 添加模版名称和分类ID字段

### 2. 请求响应模型

- **新增：** `LabelResponses.cs` - 包含分类和模版的响应模型
- **修改：** `LabelRequests.cs` - 添加新接口的请求模型

### 3. 服务层变更

- **修改：** `ILabelService.cs` - 添加新方法定义
- **修改：** `LabelService.cs` - 实现新的业务逻辑

### 4. 控制器变更

- **修改：** `PrinterManagerController.cs` - 添加两个新的API接口

## 部署说明

1. **数据库更新：**
   ```sql
   -- 执行建表脚本
   source database_scripts/create_label_template_category_table.sql;
   
   -- 执行表结构修改脚本
   source database_scripts/alter_label_template_table.sql;
   ```

2. **代码部署：**
   - 编译并部署更新后的代码
   - 确保所有依赖项正确引用

3. **测试验证：**
   - 测试获取分类列表接口
   - 测试按分类获取模版接口
   - 验证分页功能正常

## 注意事项

1. 新增的分类ID字段允许为空，兼容现有数据
2. 模版名称字段允许为空，默认显示"未命名模版"
3. 分类查询支持启用状态筛选，只返回启用的分类
4. 模版查询按更新时间倒序排列
5. 所有接口都包含完整的异常处理和日志记录

## 后续扩展建议

1. 可考虑添加分类管理的后台接口（增删改查）
2. 可为模版添加更多属性（如标签尺寸、适用场景等）
3. 可考虑添加模版的收藏、评分等功能
4. 可添加模版的使用统计功能
