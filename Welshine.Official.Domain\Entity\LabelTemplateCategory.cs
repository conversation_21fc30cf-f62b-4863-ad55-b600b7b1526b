using System;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 标签模版分类表
    /// </summary>
    [SqlSugar.SugarTable("ext_label_template_category")]
    public class LabelTemplateCategory : BaseBigEntity
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "category_name")]
        public string CategoryName { get; set; }

        /// <summary>
        /// 分类描述
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "category_description")]
        public string CategoryDescription { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "sort")]
        public int Sort { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "is_enabled")]
        public bool IsEnabled { get; set; } = true;
    }
}
