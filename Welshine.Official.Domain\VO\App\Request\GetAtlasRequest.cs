﻿using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.App.Request
{
    /// <summary>
    /// 获取最新图册
    /// </summary>
    public class GetAtlasRequest
    {
        ///// <summary>
        ///// 手机号
        ///// </summary>
        //[StringLength(11, ErrorMessage = "手机长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[0-9]+$", ErrorMessage = "手机格式错误")]
        //public string Phone { get; set; }

        /// <summary>
        /// 用户OpenId
        /// </summary>
        [Required(ErrorMessage = "OpenId是必填项,请完善", AllowEmptyStrings = false)]
        [StringLength(128, ErrorMessage = "OpenId长度不符,请完善", MinimumLength = 1)]
        public string OpenId { get; set; }
    }
}
