﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 校验两个时间
    /// https://stackoverflow.com/questions/2848684/compare-dates-dataannotations-validation-asp-net-mvc
    /// </summary>
    public sealed class CompareDatesValidatorAttribute : ValidationAttribute
    {
        private string _dateToCompare;
        private const string _errorMessage = "'{0}' must be greater or equal'{1}'";
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dateToCompare"></param>
        public CompareDatesValidatorAttribute(string dateToCompare)
            : base(_errorMessage)
        {
            _dateToCompare = dateToCompare;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public override string FormatErrorMessage(string name)
        {
            return string.Format(_errorMessage, name, _dateToCompare);
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="value"></param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var dateToCompare = validationContext.ObjectType.GetProperty(_dateToCompare);
            var dateToCompareValue = dateToCompare.GetValue(validationContext.ObjectInstance, null);
            if (dateToCompareValue != null && value != null && (DateTime)value < (DateTime)dateToCompareValue)
            {
                return new ValidationResult(ErrorMessage ?? FormatErrorMessage(validationContext.DisplayName));
            }
            return null;
        }
    }
}
