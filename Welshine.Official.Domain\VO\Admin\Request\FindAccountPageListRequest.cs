﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class FindAccountPageListRequest
    {
        /// <summary>
        /// 用户编号
        /// </summary>
        [RegularExpression(@"[a-zA-Z0-9]{0,20}", ErrorMessage = "用户编号必须为20位长度以内的数字字母组合")]
        public string UserNo { get; set; }
        /// <summary>
        /// 用户账号
        /// </summary>
        [RegularExpression(@"[a-zA-Z0-9]{0,20}", ErrorMessage = "用户账号必须为20位长度以内的数字字母组合")]
        public string LoginName { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [RegularExpression(@"[0-9]{0,13}", ErrorMessage = "电话必须为13位长度以内的数字")]
        public string Tel { get; set; } 
        /// <summary>
        /// 部门id
        /// </summary>
        public long? DepartmentId { get; set; }
        /// <summary>
        /// 岗位id
        /// </summary>
        public long? PostId { get; set; }
        /// <summary>
        /// 启用禁用状态
        /// </summary>
        public bool? Status { get; set; }
        /// <summary>
        /// 创建时间段
        /// </summary>
        public TimeHorizon CreatedTimeScope { get; set; }
    }
}
