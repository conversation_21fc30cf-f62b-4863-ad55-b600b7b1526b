﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Dto;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 产品介绍(中文)
    /// </summary>
    public class ProductCNController : BaseApiController
    {
        private readonly IVersionsService _versionsService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="versionsService"></param>
        public ProductCNController(IVersionsService versionsService)
        {
            _versionsService = versionsService;
        }

        /// <summary>
        /// 添加中文产品介绍并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2205">版本标题已存在</response>
        /// <response code="2241">详情图至少上传2张</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddProductCNVersions([FromBody] BaseRequest<AddVersionsRequest<ProductCNData>> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                VersionsDto<ProductCNData> versions = new VersionsDto<ProductCNData>()
                {
                    VersionType = EnumVersionType.ProductCN.GetHashCode(),
                    Title = request.Body.Title,
                    Content = request.Body.Content,
                    Data = request.Body.Data,
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };
                if (request.Body.Data.PC.Img.Count < 2)
                {
                    throw new BusinessException(ErrorCode.ProductCNImgCountError.ToDescriptionName(), ErrorCode.ProductCNImgCountError.GetHashCode());
                }
                if (request.Body.Data.Mobile.Img.Count < 2)
                {
                    throw new BusinessException(ErrorCode.ProductCNImgCountError.ToDescriptionName(), ErrorCode.ProductCNImgCountError.GetHashCode());
                }
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.AddVersions(entity);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddProductCNVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取中文产品介绍详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">版本Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetVersionsResponse<ProductCNData>>> GetProductCNVersions([FromBody] BaseRequest<GetVersionsIdRequest> request)
        {
            GetVersionsResponse<ProductCNData> result = null;
            try
            {
                result = await _versionsService.GetVersions<ProductCNData>(EnumVersionType.ProductCN.GetHashCode(), request.Body.VersionsId.Value);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductCNVersions Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 修改中文产品介绍信息并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">版本Id不存在</response>
        /// <response code="2205">标题已存在</response>
        /// <response code="2208">状态为已发布,暂时无法编辑</response>
        /// <response code="2209">审核状态不为待提交或审核驳回,暂时无法编辑</response>
        /// <response code="2241">详情图至少上传2张</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditProductCNVersions([FromBody] BaseRequest<EditVersionsRequest<ProductCNData>> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                VersionsDto<ProductCNData> versions = new VersionsDto<ProductCNData>()
                {
                    Id = request.Body.VersionsId.Value,
                    VersionType = EnumVersionType.ProductCN.GetHashCode(),
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    Title = request.Body.Title,
                    Content = request.Body.Content,
                    Data = request.Body.Data,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };
                if (request.Body.Data.PC.Img.Count < 2)
                {
                    throw new BusinessException(ErrorCode.ProductCNImgCountError.ToDescriptionName(), ErrorCode.ProductCNImgCountError.GetHashCode());
                }
                if (request.Body.Data.Mobile.Img.Count < 2)
                {
                    throw new BusinessException(ErrorCode.ProductCNImgCountError.ToDescriptionName(), ErrorCode.ProductCNImgCountError.GetHashCode());
                }
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.EditVersions(versions);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditProductCNVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> ProductCNExistsRelease()
        {
            bool result = false;
            try
            {
                result = await _versionsService.ExistsRelease(EnumVersionType.ProductCN.GetHashCode());
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("BannerExistsRelease Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 删除版本信息
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">版本Id不存在</response>
        /// <response code="2210">状态为已发布,暂时无法删除</response>
        /// <response code="2211">审核状态为待审核,暂时无法删除</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteProductCNVersions([FromBody] BaseRequest<GetVersionsIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    Id = request.Body.VersionsId.Value,
                    VersionType = EnumVersionType.ProductCN.GetHashCode(),
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now,
                };

                result = await _versionsService.DeleteVersions(versions);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteProductCNVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 版本信息审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">版本Id不存在</response>
        /// <response code="2214">审核状态不为待审核,暂时无法审核</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> ProductCNVersionsApprove([FromBody] BaseRequest<BannerVersionsApproveRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    Id = request.Body.VersionsId.Value,
                    VersionType = EnumVersionType.ProductCN.GetHashCode(),
                    ApproverStatus = request.Body.ApproverStatus.Value,
                    ApprovalOpinion = request.Body.ApprovalOpinion,
                    ApproverId = user.UserId,
                    Approver = user.UserName.ToString() ?? "system",
                    ApprovalTime = DateTime.Now,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _versionsService.VersionsApprove(versions);
                return Success("审核成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("ProductCNVersionsApprove Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取版本列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<VersionsListResponse>>> GetProductCNPageList([FromBody] RequestPageModel<VersionsListRequest> request)
        {
            PageRows<VersionsListResponse> pageRows = null;
            try
            {
                var user = GetUserInfo();

                pageRows = await _versionsService.GetVersionsPageList(user.UserId, EnumVersionType.ProductCN.GetHashCode(), request.RequestParams.VersionsTitle, request.RequestParams.ReleaseStatus, request.RequestParams.ApproverStatus, request.RequestParams.ReleaseTimeScope, request.OrderFile, request.SortType, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductCNPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<VersionsListResponse>>> GetProductCNReleaseVersions()
        {
            List<VersionsListResponse> pageRows = null;
            try
            {
                pageRows = await _versionsService.GetAllReleaseVersions();

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductCNReleaseVersions " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
    }
}
