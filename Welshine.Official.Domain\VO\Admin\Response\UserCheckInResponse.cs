﻿using CsvHelper.Configuration.Attributes;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 用户登记
    /// </summary>
    public class UserCheckInResponse
    {
        /// <summary>
        /// 资料编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "id")]
        [Ignore]
        public long UserCheckId{ get; set; }
        /// <summary>
        /// 资料编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "code")]
        [Name("资料编码")]
        public string UserCheckCode { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "phone")]
        [Name("手机号码")]
        public string UserCheckPhone { get; set; }

        /// <summary>
        /// 图册编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "atlas_code")]
        [Ignore]
        public string AtlasCode { get; set; }

        /// <summary>
        /// 图册名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "atlas_name")]
        [Name("下载图册名称")]
        public string AtlasName { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_time", IsOnlyIgnoreUpdate = true)]
        [Name("创建时间")]
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(ColumnName = "created_by", IsOnlyIgnoreUpdate = true)]
        [Ignore]
        public string CreatedBy { get; set; } = "system";

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "updated_time")]
        [Ignore]
        public DateTime? UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(ColumnName = "updated_by")]
        [Ignore]
        public string UpdatedBy { get; set; } = "system";
    }
}
