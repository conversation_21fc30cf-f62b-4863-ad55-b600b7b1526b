﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 联系方式列表条目
    /// </summary>
    public class GetContactsPageListResponse
    {
        /// <summary>
        /// 联系方式Id
        /// </summary>
        public long ContactsId { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// 门店地址
        /// </summary>
        public string StoreAddress { get; set; }

        /// <summary>
        /// 门店开始营业时间
        /// </summary>
        public string StoreStartTime { get; set; }

        /// <summary>
        /// 门店结束营业时间
        /// </summary>
        public string StoreEndTime { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        public int ReleaseStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedName { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }

        /// <summary>
        /// 审批人
        /// </summary>
        public string ApproverUserName { get; set; }

        /// <summary>
        /// 审核状态: 0->待提交; 1->待审核; 2->审核通过; 3->审核驳回
        /// </summary>
        public int? ApproverStatus { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime? ApprovalTime { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? ReleaseTime { get; set; }

    }
}
