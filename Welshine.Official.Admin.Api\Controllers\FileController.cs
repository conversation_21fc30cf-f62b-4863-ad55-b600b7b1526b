﻿using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Dto;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;
using static AlibabaCloud.SDK.Sts20150401.Models.AssumeRoleResponseBody;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 文件管理
    /// </summary>
    public class FileController : BaseApiController
    {
        private IOssService _ossService;
        IOptionsMonitor<AliOssSettings> _aliOss;
        IFileService _fileService;

        /// <summary>
        /// 
        /// </summary>
        public FileController(IOssService ossService, IOptionsMonitor<AliOssSettings> aliOss, IFileService fileService)
        {
            _ossService = ossService;
            _aliOss = aliOss;
            _fileService = fileService;
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="iFormFile"></param>
        /// <returns></returns>
        [HttpPost()]
        [Obsolete]
        public async Task<string> UploadFile([FromForm] IFormFile iFormFile)
        {
            if (iFormFile == null || iFormFile.Length == 0)
                return "No file selected for upload.";
            using (var stream = new MemoryStream())
            {
                await iFormFile.CopyToAsync(stream);
            }
            var keyName = "Test/" + iFormFile.FileName;
            _ossService.PutObject(keyName, iFormFile.OpenReadStream());
            return _ossService.BaseUrl+ keyName;
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="files"></param>
        /// <param name="IsOverlay"></param>
        /// <returns></returns>
        [HttpPost]
        [Obsolete]
        public async Task<BaseResponse<List<FileDto>>> UploadFiles([FromForm] IFormFileCollection files,  bool IsOverlay)
        {
            List<FileDto> result = new List<FileDto>();
            try
            {
                if (files == null || files.Count == 0)
                {
                    return NoFind("没有文件", result);
                }
                var userInfo = GetUserInfo();
                string userId = userInfo.UserId;
                string userName = userInfo.UserName?? "system";
                var allowType = new[] { ".jpg", ".png", ".txt", ".jpeg", ".pdf", ".csv", ".image", ".mp4", ".avi", ".mkv", ".wmv", ".gif" };
                if (files.Any(b => !allowType.Any(x => b.FileName.ToLower().EndsWith(x))))
                {
                    return Failure(ErrorCode.SaveFail.GetHashCode(), $"只能上传{string.Join(",", allowType)}格式的文件。", result);
                }
                var mLength = _aliOss.CurrentValue?.mLength ?? 5;
                //大小限制
                if (files.Any(b => b.Length >= 1024 * 1024 * mLength))
                {
                    return Failure(ErrorCode.SaveFail.GetHashCode(), $"上传文件的大小只能在{mLength}M以下。", result);
                }

                foreach (var file in files)
                {
                    FileDto fileDto = await _fileService.UploadFile(file, userId, userName, IsOverlay);
                    result.Add(fileDto);
                }
                return Success("上传成功", result);
            }
            catch (Exception e)
            {
                Log.Error("UploadFiles 失败{u}", e.Message + e.StackTrace);
                return Failure<List<FileDto>>(ErrorCode.SystemError, result);
            }

        }

        /// <summary>
        /// 获取阿里云oss临时凭证
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<AliOssCredentialsResponse> GetAliOssCredentials()
        {
            var currentUser = GetUserInfo();
            var data = _ossService.GetCredentials(currentUser.LoginName);
            return Success("请求成功", data);
        }

        /// <summary>
        /// 添加文件
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<FileDto>> AddUploadFiles([FromBody] BaseRequest<AddUploadFilesRequest> request)
        {
            FileDto result = null;
            try
            {
                var user = GetUserInfo();

                Files file = new Files()
                {
                    Id = Guid.NewGuid().ToString("N"),
                    BaseUrl = request.Body.BaseUrl,
                    Url = request.Body.Url,
                    Length = request.Body.Length,
                    ContentType = request.Body.ContentType,
                    MD5 = request.Body.Md5,
                    UserId = user.UserId,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _fileService.AddUploadFiles(file);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddUploadFiles Error {u}", ex.Message);
                return Failure<FileDto>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }
    }
}
