﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    [SqlSugar.SugarTable("rbac_group")]
    public class RBACGroup:BaseBigEntity
    {
        [SqlSugar.SugarColumn(ColumnName = "group_no")]
        public string GroupNo { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "group_name")]
        public string GroupName { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "description")]
        public string Description { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "role_id")]
        public long RoleId { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "user_ids")]
        public string UserIds { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "user_names")]
        public string UserNames { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "rbac_url_json")]
        public string RBACUrlJson { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "rbac_menu_json")]
        public string RBACMenuJson { get; set; }
    }
}
