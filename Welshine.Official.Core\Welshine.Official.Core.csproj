﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="6.4.0" />    
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="7.1.0" />    
    <PackageReference Include="Autofac.Extras.CommonServiceLocator" Version="6.0.1" />    
    <PackageReference Include="AutofacSerilogIntegration" Version="2.0.0" />
    <PackageReference Include="AutoMapper" Version="11.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="CsvHelper.Excel.Core" Version="27.2.1" />
    <PackageReference Include="FreeRedis" Version="1.0.1" />
    <PackageReference Include="IdentityModel.AspNetCore.OAuth2Introspection" Version="4.0.0" />
    <PackageReference Include="JWT" Version="10.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.29" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.10" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="3.1.10" />
    <PackageReference Include="Polly" Version="7.2.3" />
    <PackageReference Include="Refit.HttpClientFactory" Version="5.2.4" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="6.0.1" />
    <PackageReference Include="SharpZipLib" Version="1.3.3" />
    <PackageReference Include="SqlSugar.IOC" Version="1.9.0" />
    <PackageReference Include="SqlSugarCore" Version="*******" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>

</Project>
