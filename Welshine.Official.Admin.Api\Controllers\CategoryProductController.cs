﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 分类产品
    /// </summary>
    public class CategoryProductController : BaseApiController
    {
        private readonly ICategoryProductService _categoryProductService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="categoryProductService"></param>
        public CategoryProductController(ICategoryProductService categoryProductService)
        {
            _categoryProductService = categoryProductService;
        }

        private void baseValid(List<string> CornerMarkImgId, List<string> ProductImgId)
        {
            if (CornerMarkImgId != null && CornerMarkImgId.Count > 0)
            {
                foreach (string str in CornerMarkImgId)
                {
                    if (str == null)
                    {
                        throw new BusinessException(ErrorCode.CategoryProductCornerMarkError.ToDescriptionName(), ErrorCode.CategoryProductCornerMarkError.GetHashCode());
                    }
                    if (str.Length > 40)
                    {
                        throw new BusinessException(ErrorCode.CategoryProductCornerMarkLengthError.ToDescriptionName(), ErrorCode.CategoryProductCornerMarkLengthError.GetHashCode());
                    }
                }
            }

            foreach (string str in ProductImgId)
            {
                if (str == null)
                {
                    throw new BusinessException(ErrorCode.CategoryProductImgError.ToDescriptionName(), ErrorCode.CategoryProductImgError.GetHashCode());
                }
                if (str.Length > 40)
                {
                    throw new BusinessException(ErrorCode.CategoryProductImgLengthError.ToDescriptionName(), ErrorCode.CategoryProductImgLengthError.GetHashCode());
                }
            }
        }

        #region 英文

        /// <summary>
        /// 添加英文分类产品
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2225">分类不存在</response>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2227">商品图片不存在</response>
        /// <response code="2235">只允许三级分类新增产品</response>
        /// <response code="2240">二级分类底下的英文产品介绍数量限制999个</response>
        /// <response code="2242">商品角图图片不存在</response>
        /// <response code="2246">商品角图Id超出长度限制</response>
        /// <response code="2247">商品图片Id超出长度限制</response>
        /// <response code="2248">商品图片Id是必填项</response>
        /// <response code="2249">商品角图Id是必填项</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<CategoryProduct>> AddENCategoryProduct([FromBody] BaseRequest<AddENCategoryProductRequest> request)
        {
            CategoryProduct result = null;
            try
            {
                baseValid(request.Body.CornerMarkImgId, request.Body.ProductImgId);

                var user = GetUserInfo();

                CategoryProduct categoryProduct = new CategoryProduct()
                {
                    ProductLanguage = 0,
                    CategoryId = request.Body.CategoryId.Value,
                    ProductName = request.Body.ProductName,
                    //ProductTexture = request.Body.ProductTexture,
                    //ProductColor = request.Body.ProductColor,
                    //ProductSpecification = request.Body.ProductSpecification,
                    ProductIntroduce = request.Body.ProductIntroduce,
                    ReleaseStatus = 0,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _categoryProductService.AddCategoryProduct(categoryProduct, request.Body.ProductImgId, request.Body.CornerMarkImgId, request.Body.IntroduceList);
                if (result.Id > 0)
                {
                    return Success("新增成功", result);
                }
                else
                {
                    return Success("新增失败", result);
                }
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddCategoryProduct Error {u}", ex.Message);
                return Failure<CategoryProduct>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 编辑英文分类产品
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2228">分类产品不存在</response>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2225">分类不存在</response>
        /// <response code="2227">商品图片不存在</response>
        /// <response code="2231">产品已发布,不允许编辑</response>
        /// <response code="2235">只允许三级分类新增产品</response>
        /// <response code="2242">商品角图图片不存在</response>
        /// <response code="2246">商品角图Id超出长度限制</response>
        /// <response code="2247">商品图片Id超出长度限制</response>
        /// <response code="2248">商品图片Id是必填项</response>
        /// <response code="2249">商品角图Id是必填项</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<CategoryProduct>> EditENCategoryProduct([FromBody] BaseRequest<EditENCategoryProductRequest> request)
        {
            CategoryProduct result = null;
            try
            {
                baseValid(request.Body.CornerMarkImgId, request.Body.ProductImgId);

                var user = GetUserInfo();

                CategoryProduct categoryProduct = new CategoryProduct()
                {
                    Id = request.Body.CategoryProductId.Value,
                    ProductLanguage = 0,
                    CategoryId = request.Body.CategoryId.Value,
                    ProductName = request.Body.ProductName,
                    //ProductTexture = request.Body.ProductTexture,
                    //ProductColor = request.Body.ProductColor,
                    //ProductSpecification = request.Body.ProductSpecification,
                    ProductIntroduce = request.Body.ProductIntroduce,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _categoryProductService.EditCategoryProduct(categoryProduct, request.Body.ProductImgId, request.Body.CornerMarkImgId, request.Body.IntroduceList);
                return Success("编辑成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditCategoryProduct Error {u}", ex.Message);
                return Failure<CategoryProduct>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        #endregion

        #region 中文

        /// <summary>
        /// 添加中文分类产品
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2225">分类不存在</response>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2227">商品图片不存在</response>
        /// <response code="2235">只允许三级分类新增产品</response>
        /// <response code="2240">二级分类底下的英文产品介绍数量限制999个</response>
        /// <response code="2242">商品角图图片不存在</response>
        /// <response code="2246">商品角图Id超出长度限制</response>
        /// <response code="2247">商品图片Id超出长度限制</response>
        /// <response code="2248">商品图片Id是必填项</response>
        /// <response code="2249">商品角图Id是必填项</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<CategoryProduct>> AddCNCategoryProduct([FromBody] BaseRequest<AddCNCategoryProductRequest> request)
        {
            CategoryProduct result = null;
            try
            {
                baseValid(request.Body.CornerMarkImgId, request.Body.ProductImgId);

                var user = GetUserInfo();

                CategoryProduct categoryProduct = new CategoryProduct()
                {
                    ProductLanguage = 1,
                    CategoryId = request.Body.CategoryId.Value,
                    ProductName = request.Body.ProductName,
                    ProductIntroduce = request.Body.ProductIntroduce,
                    ReleaseStatus = 0,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _categoryProductService.AddCategoryProduct(categoryProduct, request.Body.ProductImgId, request.Body.CornerMarkImgId, request.Body.IntroduceList);
                if (result.Id > 0)
                {
                    return Success("新增成功", result);
                }
                else
                {
                    return Success("新增失败", result);
                }
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddCategoryProduct Error {u}", ex.Message);
                return Failure<CategoryProduct>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 编辑中文分类产品
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2228">分类产品不存在</response>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2226">商品图片重复</response>
        /// <response code="2225">分类不存在</response>
        /// <response code="2227">商品图片不存在</response>
        /// <response code="2231">产品已发布,不允许编辑</response>
        /// <response code="2235">只允许三级分类新增产品</response>
        /// <response code="2242">商品角图图片不存在</response>
        /// <response code="2246">商品角图Id超出长度限制</response>
        /// <response code="2247">商品图片Id超出长度限制</response>
        /// <response code="2248">商品图片Id是必填项</response>
        /// <response code="2249">商品角图Id是必填项</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<CategoryProduct>> EditCNCategoryProduct([FromBody] BaseRequest<EditCNCategoryProductRequest> request)
        {
            CategoryProduct result = null;
            try
            {
                baseValid(request.Body.CornerMarkImgId, request.Body.ProductImgId);

                var user = GetUserInfo();

                CategoryProduct categoryProduct = new CategoryProduct()
                {
                    Id = request.Body.CategoryProductId.Value,
                    ProductLanguage = 1,
                    CategoryId = request.Body.CategoryId.Value,
                    ProductName = request.Body.ProductName,
                    ProductIntroduce = request.Body.ProductIntroduce,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _categoryProductService.EditCategoryProduct(categoryProduct, request.Body.ProductImgId, request.Body.CornerMarkImgId, request.Body.IntroduceList);
                return Success("编辑成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditCategoryProduct Error {u}", ex.Message);
                return Failure<CategoryProduct>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        #endregion

        /// <summary>
        /// 删除分类产品
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2228">分类产品不存在</response>
        /// <response code="2232">产品已发布,不允许删除</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteCategoryProduct([FromBody] BaseRequest<CategoryProductIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                CategoryProduct categoryProduct = new CategoryProduct()
                {
                    Id = request.Body.CategoryProductId.Value,
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _categoryProductService.DeleteCategoryProduct(categoryProduct);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteCategoryProduct Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 获取分类产品详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2228">分类产品不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetCategoryProductResponse>> GetCategoryProductDetail([FromBody] BaseRequest<CategoryProductIdRequest> request)
        {
            GetCategoryProductResponse result = null;
            try
            {
                result = await _categoryProductService.GetCategoryProductDetail(request.Body.CategoryProductId.Value);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategoryProductDetail Error {u}", ex.Message);
                return Failure<GetCategoryProductResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 发布分类产品
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2228">分类产品不存在</response>
        /// <response code="2229">产品已下架</response>
        /// <response code="2230">产品已发布</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> ReleaseCategoryProduct([FromBody] BaseRequest<ReleaseCategoryProductRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                result = await _categoryProductService.ReleaseCategoryProduct(request.Body.CategoryProductId.Value, request.Body.ReleaseStatus.Value, user.UserId, user.UserName.ToString());
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("ReleaseCategoryProduct Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }


        /// <summary>
        /// 获取分类产品列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<CategoryProductListResponse>>> GetCategoryProductPageList([FromBody] RequestPageModel<GetCategoryProductPageListRequest> request)
        {
            PageRows<CategoryProductListResponse> pageRows = null;
            try
            {
                var user = GetUserInfo();

                pageRows = await _categoryProductService.GetCategoryProductPageList(request.RequestParams.ProductLanguage, request.RequestParams.ProductName, request.RequestParams.ReleaseStatus, request.RequestParams.TimeScope, request.OrderFile, request.SortType, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategoryProductPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 根据分类Id获取已发布的分类商品(并按sort排序)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetCategoryProductListOrderSortResponse>> GetCategoryProductListByCategoryId([FromBody] BaseRequest<GetCategoryProductOrderSortRequest> request)
        {
            GetCategoryProductListOrderSortResponse result = null;
            try
            {
                var user = GetUserInfo();

                result = await _categoryProductService.GetCategoryProductListByCategoryId(request.Body.CategoryId);

                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategoryProductListByCategoryId " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 修改产品排序
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2225">分类不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditCategoryProductSort([FromBody] BaseRequest<EditCategoryProductSortRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                List<CategoryProduct> list = new List<CategoryProduct>();
                foreach (var item in request.Body.CategoryProductSortList)
                {
                    CategoryProduct category = new CategoryProduct()
                    {
                        Id = item.CategoryProductId.Value,
                        Sort = item.Sort.Value
                    };
                    list.Add(category);
                }

                result = await _categoryProductService.EditCategoryProductSort(request.Body.CategoryId.Value, request.Body.ProductLanguage.Value, list);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditCategoryProductSort Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2225">分类不存在</response>
        /// <response code="2228">分类产品不存在</response>
        /// <response code="2235">只允许三级分类新增产品</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditProductCategory([FromBody] BaseRequest<EditProductCategoryRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                string ModifierId = user.UserId;
                string UpdatedBy = user.UserName.ToString() ?? "system";

                result = await _categoryProductService.EditProductCategory(request.Body.CategoryId.Value, request.Body.CategoryProductId.Value, ModifierId, UpdatedBy);
                return Success("修改产品分类成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteCategoryProduct Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

    }
}
