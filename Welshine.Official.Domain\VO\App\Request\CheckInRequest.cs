﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.App.Request
{
    /// <summary>
    /// 
    /// </summary>
    public class CheckInRequest
    {
        /// <summary>
        /// 用户Id
        /// </summary>
        [Required(ErrorMessage = "OpenId是必填项,请完善", AllowEmptyStrings = false)]
        [StringLength(128, ErrorMessage = "OpenId长度不符,请完善", MinimumLength = 1)]
        public string OpenId { get; set; }
        /// <summary>
        /// 图册编码
        /// </summary>
        [Required(ErrorMessage = "图册编码是必填项,请完善", AllowEmptyStrings = false)]
        [StringLength(30,ErrorMessage = "图册编码长度不符",MinimumLength =1)]
        [RegularExpression(@"^[a-zA-Z0-9]+$", ErrorMessage = "编码格式错误")]
        public string AtlasCode { get; set; }
    }
}
