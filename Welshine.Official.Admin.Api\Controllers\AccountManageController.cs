﻿using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 用户管理
    /// </summary>
    public class AccountManageController:BaseApiController
    {
        private AccountService _accountService = new AccountService();
        /// <summary>
        /// 分页查询用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<PageRows<AccountResponse>> FindAccountPageList([FromBody] RequestPageModel<FindAccountPageListRequest> request)
        {
            var body = request.RequestParams;
            var orderType = OrderByType.Desc;
            if(request.SortType == SortType.Asc)
            {
                orderType = OrderByType.Asc;
            }
            
            var data = _accountService.FindAccountPageList(body.UserNo, body.LoginName, body.Tel, body.CreatedTimeScope
                , body.DepartmentId, body.PostId, body.Status
                , request.PageIndex, request.PageSize, request.OrderFile, orderType);
            return Success("请求成功", data);
        }
        /// <summary>
        /// 添加用户
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse AddAccount([FromBody] BaseRequest<AddAccountRequest> request)
        {
            var currentUser = GetUserInfo();
            var body = request.Body;
            _accountService.AddAccount(body.DepartmentId.Value, body.PostId.Value, body.LoginName, body.UserName, body.Tel, currentUser);
            return Success("添加成功");
        }
        /// <summary>
        /// 修改用户
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse EditAccount([FromBody] BaseRequest<EditAccountRequest> request)
        {
            var body = request.Body;
            _accountService.EditAccount(body.AccountId.Value, body.DepartmentId.Value, body.PostId.Value, body.UserName, body.Tel);
            return Success("请求成功");
        }
        /// <summary>
        /// 修改用户启用状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse ChangeAccountStatus([FromBody] BaseRequest<ChangeAccountStatusRequest> request)
        {
            var currentUser = GetUserInfo();
            var body = request.Body;
            _accountService.ChangeAccountStatus(body.AccountId.Value, body.Status.Value,currentUser);
            return Success("请求成功");
        }
        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse ResetAccountPassword([FromBody] BaseRequest<ResetAccountPasswordRequest> request)
        {
            var currentUser = GetUserInfo();
            var body = request.Body;
            _accountService.ResetAccountPassword(body.AccountId.Value, currentUser);
            return Success("请求成功");
        }
    }
}
