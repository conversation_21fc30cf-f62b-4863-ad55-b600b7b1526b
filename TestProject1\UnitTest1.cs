using JWT;
using JWT.Algorithms;
using JWT.Builder;
using JWT.Serializers;
using Org.BouncyCastle.Crypto.Tls;
using System;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using Welshine.Official.Core.JwtToken;
using Welshine.Official.SDK;
using Xunit;
using BCrypt.Net;
namespace TestProject1
{
    public class UnitTest1
    {
        public readonly IShopAdminApi _shopAdminApiApi;
        public UnitTest1(IShopAdminApi shopAdminApi) 
        {
            _shopAdminApiApi = shopAdminApi;
        }
        [Fact]
        public void Test1()
        {
           
            var enhancedHashPassword = BCrypt.Net.BCrypt.EnhancedHashPassword("1qaz1qaz");
            var validatePassword = BCrypt.Net.BCrypt.EnhancedVerify("dangerous", enhancedHashPassword);

            string aq = "12345".PadLeft(4, '0');
            var payload = new Dictionary<string, object>();
            payload.Add("userid", 1);
            payload.Add("roles",new List<string>() { "A","B","C"});
            var exp = DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds();
            var tt = JwtUtils.Encode(payload, exp);
            var r = JwtUtils.Decode(tt);
            if (_shopAdminApiApi!=null)
            {
                var a = _shopAdminApiApi.Login(new Welshine.Official.SDK.Model.LoginRequest() { LoginName="admin",PassWord= "1qaz1qaz" }).Result;
                var token = "Bearer "+a.Data.Token;
                var b = _shopAdminApiApi.FreshToken(token).Result;
                
            }
        }
    }
}
