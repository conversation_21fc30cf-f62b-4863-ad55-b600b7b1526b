﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Linq;
namespace Welshine.Official.Core.Attributes.ModelValid
{
    public class ArrayMaxLengthAttribute : ValidationAttribute
    {
        public int MaxLen { get; set; }
        public ArrayMaxLengthAttribute(int maxLen)
        {
            MaxLen = maxLen;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                if (value is Array array && array.Length > MaxLen)
                {
                    return new ValidationResult(ErrorMessage);
                }
                var type = value.GetType();
                if (type.IsGenericType && value is System.Collections.IEnumerable list)
                {
                    if(list.Cast<dynamic>().Count() > MaxLen)
                    {
                        return new ValidationResult(ErrorMessage);
                    }
                }
            }
            return ValidationResult.Success;
        }
    }
}
