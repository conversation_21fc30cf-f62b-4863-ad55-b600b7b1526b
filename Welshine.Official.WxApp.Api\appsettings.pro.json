{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "sqlsuger": {"DbType": 0, "InitKeyType": 1, "IsAutoCloseConnection": true, "ConnectionString": "Data Source=************;port=3306; Initial Catalog=official_db;uid=welshineoffical; pwd=************;Convert Zero Datetime=True;Allow Zero Datetime=True"}, "ConnectionStrings": {"RedisStr": "************,password=dthy@123456,defaultDatabase=10,poolsize=10,ssl=false,writeBuffer=10240,prefix="}, "AppConfig": {"AppId": "wx0fe9df0a15f8223e", "AppSecret": "2d6933e4fb4b586619d9f852761a7c80", "PrinterAppId": "wx1c8c0500e04d4b70", "PrinterAppSecret": ""}, "Serilog": {"WriteTo": [{"Name": "RollingFile", "Args": {"pathFormat": "logs\\{Date}.txt", "RestrictedToMinimumLevel": "Warning"}}, {"Name": "<PERSON><PERSON><PERSON>"}], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Information"}}}, "AliOss": {"endpoint": "oss-cn-guangzhou.aliyuncs.com", "bucketName": "welshine-official-pro01", "accessKeyId": "LTAI5t8jy9LqNTzyPoPzLjBR", "accessKeySecret": "******************************", "allowType": ".png,.jpg,.txt,.pdf,.jpeg,.image,.mp4,.avi,.mkv,.wmv,.gif", "mLength": 500, "arn": "acs:ram::1493852429391277:role/ossapi", "durationSeconds": 3600}}