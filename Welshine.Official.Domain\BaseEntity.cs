﻿using SqlSugar;
using System;

namespace Welshine.Official.Domain
{
    public class BaseEntity<TId> : BaseEntityId<TId>
    {
        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(ColumnName = "created_by", IsOnlyIgnoreUpdate = true)]
        public string CreatedBy { get; set; } = "system";

        /// <summary>
        /// 创建人Id
        /// </summary>
        [SugarColumn(ColumnName = "creator_id", IsOnlyIgnoreUpdate = true)]
        public string CreatorId { get; set; } = "0";

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_time", IsOnlyIgnoreUpdate = true)]
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(ColumnName = "updated_by")]
        public string UpdatedBy { get; set; } = "system";

        /// <summary>
        /// 更新人Id
        /// </summary>
        [SugarColumn(ColumnName = "modifier_id")]
        public string ModifierId { get; set; } = "0";

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "updated_time")]
        public DateTime? UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 逻辑删除
        /// </summary>
        [SugarColumn(ColumnName = "is_deleted")]
        public bool IsDeleted { get; set; }
    }
    
    public class BaseEntityId<TId>
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true)]
        public TId Id { get; set; }
    }

    /// <summary>
    /// 自增Id
    /// </summary>
    public class BaseBigEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(ColumnName = "created_by", IsOnlyIgnoreUpdate = true)]
        public string CreatedBy { get; set; } = "system";

        /// <summary>
        /// 创建人Id
        /// </summary>
        [SugarColumn(ColumnName = "creator_id", IsOnlyIgnoreUpdate = true)]
        public string CreatorId { get; set; } = "0";

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_time", IsOnlyIgnoreUpdate = true)]
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(ColumnName = "updated_by")]
        public string UpdatedBy { get; set; } = "system";

        /// <summary>
        /// 更新人Id
        /// </summary>
        [SugarColumn(ColumnName = "modifier_id")]
        public string ModifierId { get; set; } = "0";

        /// <summary>
        /// 修改时间
        /// </summary>
        [SugarColumn(ColumnName = "updated_time")]
        public DateTime? UpdatedTime { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 逻辑删除
        /// </summary>
        [SugarColumn(ColumnName = "is_deleted")]
        public bool IsDeleted { get; set; }
    }
}
