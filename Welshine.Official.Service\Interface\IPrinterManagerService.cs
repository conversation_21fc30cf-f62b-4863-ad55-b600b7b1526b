using System.Threading.Tasks;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 打印机管理服务接口
    /// </summary>
    public interface IPrinterManagerService
    {
        /// <summary>
        /// 检查打印机设备是否在库
        /// </summary>
        /// <param name="model">打印机型号</param>
        /// <param name="sn">设备SN码</param>
        /// <returns></returns>
        Task<CheckPrinterDeviceResponse> CheckPrinterDevice(string model, string sn);
    }
}
