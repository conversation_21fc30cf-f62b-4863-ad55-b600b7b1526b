using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.App.Request
{
    public class AddLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class Delete<PERSON><PERSON>lRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }

    public class AddPrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetPrintedLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class DeletePrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }
}

