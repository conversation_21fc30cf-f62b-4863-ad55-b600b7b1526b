using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.App.Request
{
    public class AddLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class DeleteLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }

    public class AddPrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetPrintedLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class DeletePrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }

    /// <summary>
    /// 获取系统标签模版分类请求
    /// </summary>
    public class GetSystemLabelTemplateCategoriesRequest
    {
        // 无需参数，获取所有分类
    }

    /// <summary>
    /// 通过分类获取系统标签模版请求
    /// </summary>
    public class GetSystemLabelTemplatesByCategoryRequest
    {
        /// <summary>
        /// 分类ID，为空则获取全部系统模版
        /// </summary>
        public long? CategoryId { get; set; }
    }

    /// <summary>
    /// 搜索系统标签模版请求
    /// </summary>
    public class SearchSystemLabelTemplatesRequest
    {
        /// <summary>
        /// 模版名称（模糊搜索）
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 标签尺寸（精确匹配）
        /// </summary>
        public string LabelSize { get; set; }

        /// <summary>
        /// 适用场景标签（模糊搜索，支持多个关键词用逗号分隔）
        /// </summary>
        public string SceneTags { get; set; }

        /// <summary>
        /// 分类ID（可选）
        /// </summary>
        public long? CategoryId { get; set; }
    }
}

