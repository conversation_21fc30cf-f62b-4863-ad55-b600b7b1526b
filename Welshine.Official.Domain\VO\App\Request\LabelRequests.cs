using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.App.Request
{
    public class AddLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class DeleteLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }

    public class AddPrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetPrintedLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class DeletePrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }

    /// <summary>
    /// 获取系统标签模版分类请求
    /// </summary>
    public class GetSystemLabelTemplateCategoriesRequest
    {
        // 无需参数，获取所有分类
    }

    /// <summary>
    /// 通过分类获取系统标签模版请求
    /// </summary>
    public class GetSystemLabelTemplatesByCategoryRequest
    {
        /// <summary>
        /// 分类ID，为空则获取全部系统模版
        /// </summary>
        public long? CategoryId { get; set; }
    }
}

