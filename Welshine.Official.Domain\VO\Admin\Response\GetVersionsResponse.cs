﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 版本详情
    /// </summary>
    public class GetVersionsResponse<T>
    {
        /// <summary>
        /// 版本Id
        /// </summary>
        public long VersionsId { get; set; }

        /// <summary>
        /// 版本标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 版本内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 审批人
        /// </summary>
        public string Approver { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime? ApprovalTime { get; set; }

        /// <summary>
        /// 审批意见
        /// </summary>
        public string ApprovalOpinion { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 版本状态: 0->未发布; 1->已发布;
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 审核状态: 0->待提交; 1->待审核; 2->审核通过; 3->审核驳回
        /// </summary>
        public int? ApproverStatus { get; set; }

        public T Data
        {
            get; set;
        }
    }
}
