﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;

namespace Welshine.Official.Core.Extensions
{

    /// <summary>
    /// 枚举扩展属性
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// 获取枚举的DescriptionAttribute属性
        /// </summary>
        /// <param name="enumeration"></param>
        /// <returns></returns>
        public static string ToDescriptionName(this Enum enumeration)
        {
            var type = enumeration.GetType();
            var memInfo = type.GetMember(enumeration.ToString());
            if (memInfo.Length > 0)
            {
                var attrs = memInfo[0].GetCustomAttributes(typeof(DescriptionAttribute), false);
                if (attrs.Length > 0)
                    return ((DescriptionAttribute)attrs[0]).Description;
            }
            return enumeration.ToString();
        }

        /// <summary>
        /// 将枚举转换为字典(返回Dictionary<int, string>类型)
        /// </summary>
        /// <param name="enumeration"></param>
        /// <returns></returns>
        public static Dictionary<int, string> ToDictionaryDescription(this Enum enumeration)
        {
            var list = (from Enum d in Enum.GetValues(enumeration.GetType())
                        select new
                        {
                            ID = (int)Enum.Parse(enumeration.GetType(), Enum.GetName(enumeration.GetType(), d) ?? string.Empty)
                            ,
                            Name = d.ToDescriptionName()
                        }
            ).ToList();
            return list.ToDictionary(c => c.ID, c => c.Name);
        }

        /// <summary>
        /// 将枚举转换为字典(返回Dictionary<string, string>类型)
        /// </summary>
        /// <param name="enumeration"></param>
        /// <returns></returns>
        public static Dictionary<string, string> ToDictionaryDescriptionNew(this Enum enumeration)
        {
            var list = (from Enum d in Enum.GetValues(enumeration.GetType())
                        select new
                        {
                            ID = ((int)Enum.Parse(enumeration.GetType(), Enum.GetName(enumeration.GetType(), d) ?? string.Empty)).ToString()
                            ,
                            Name = d.ToDescriptionName()
                        }
            ).ToList();
            return list.ToDictionary(c => c.ID, c => c.Name);
        }

        /// <summary>
        /// 将枚举值转换为字典，只要是用了Flags的枚举，获取它值对应的Description字典
        /// </summary>
        /// <param name="enumeration"></param>
        /// <returns></returns>
        public static Dictionary<int, string> ToDictionaryValueDescription(this Enum enumeration)
        {
            if (enumeration == null)
            {
                return new Dictionary<int, string>();
            }
            var list = Enum.GetValues(enumeration.GetType()).Cast<Enum>().Where(item => enumeration.HasFlag(item));
            var dis = list.ToDictionary(item => item.GetHashCode(), item => item.ToDescriptionName());
            return dis;
        }

        public static int GetMax(this Enum enumeration)
        {
            return Enum.GetValues(enumeration.GetType()).Cast<int>().Max();
        }

        /// <summary>
        /// 将字符串对应枚举转为输出枚举的Description
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="type">枚举类型</param>
        /// <returns></returns>
        public static string ToEnumDescriptionName(this Type type, string str)
        {
            if (!type.IsEnum)
            {
                throw new ArgumentException("必须为枚举类型", "type");
            }
            if (string.IsNullOrWhiteSpace(str))
            {
                return str;
            }
            if (Enum.IsDefined(type, str))
            {
                var obj = Enum.Parse(type, str) as Enum;
                return obj.ToDescriptionName();
            }
            return str;
        }

        /// <summary>
        /// 位枚举,得到对应的枚举值
        /// </summary>
        /// <param name="en"></param>
        /// <returns></returns>
        public static IList<int> FlagsToList(this Enum en)
        {
            var list = new List<int>();
            if (en != null)
            {
                var type = en.GetType();
                var strList = en.ToString().Split(',');
                foreach (var s in strList)
                {
                    var enm = Enum.Parse(type, s.Trim());
                    if (enm != null)
                    {
                        list.Add(enm.GetHashCode());
                    }
                }
            }
            return list;
        }

        /// <summary>
        /// 枚举位或运算
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="enums"></param>
        /// <returns></returns>
        public static object BitOrNull<T>(this T[] enums) where T : struct
        {
            if (enums == null ||
                enums.Length == 0)
                return null;

            var value = enums.Aggregate(0, (current, t) => current | t.GetHashCode());

            return (T)((object)value);
        }

        /// <summary>
        /// 枚举位或运算
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="enums"></param>
        /// <returns></returns>
        public static T BitOr<T>(this T[] enums) where T : struct
        {
            return (T?)enums.BitOrNull() ?? default(T);
        }


        /// <summary>
        /// 获得某个Enum类型的绑定列表<br/>
        /// </summary>
        /// <param name="en">枚举的类型，例如：typeof(Sex)</param>
        /// <returns>
        /// 键值集合 key为Man,value为"男"(特性的描述值,如果没有特性描述就显示英文的字段名)       
        /// </returns>
        public static Dictionary<string, int> ToDictionary(this Enum en)
        {
            var enumType = en.GetType();
            //建立DataTable的列信息
            var dic = new Dictionary<string, int>();

            //获得特性Description的类型信息
            Type typeDescription = typeof(DescriptionAttribute);

            //获得枚举的字段信息（因为枚举的值实际上是一个static的字段的值）
            System.Reflection.FieldInfo[] fields = enumType.GetFields();

            //检索所有字段
            foreach (FieldInfo field in fields)
            {
                //过滤掉一个不是枚举值的，记录的是枚举的源类型
                if (field.FieldType.IsEnum == true)
                {
                    int value = -1;
                    string text = string.Empty;
                    // 通过字段的名字得到枚举的值
                    // 枚举的值如果是long的话，ToChar会有问题，但这个不在本文的讨论范围之内
                    value = (int)enumType.InvokeMember(field.Name, BindingFlags.GetField, null, null, null);

                    //获得这个字段的所有自定义特性，这里只查找Description特性
                    object[] arr = field.GetCustomAttributes(typeDescription, true);
                    if (arr.Length > 0)
                    {
                        //因为Description这个自定义特性是不允许重复的，所以我们只取第一个就可以了！
                        DescriptionAttribute aa = (DescriptionAttribute)arr[0];
                        //获得特性的描述值，也就是‘男’‘女’等中文描述
                        text = aa.Description;
                    }
                    else
                    {
                        //如果没有特性描述（-_-!忘记写了吧~）那么就显示英文的字段名
                        text = field.Name;
                    }
                    dic.Add(text, value);
                }
            }

            return dic;
        }

        /// <summary>
        /// 枚举转新的枚举
        /// </summary>
        /// <typeparam name="TEnum"></typeparam>
        /// <param name="enum"></param>
        /// <param name="defaultEnum">默认枚举</param>
        /// <returns></returns>
        public static TEnum ToNewEnum<TEnum>(this Enum @enum, TEnum defaultEnum) where TEnum : struct
        {
            var result = Enum.TryParse(@enum.ToString(), out TEnum defaultValue);

            return result ? defaultValue : defaultEnum;
        }

        /// <summary>
        /// 枚举转新的枚举
        /// </summary>
        /// <typeparam name="TEnum"></typeparam>
        /// <param name="enum"></param>
        /// <returns></returns>
        public static TEnum ToNewEnumValue<TEnum>(this Enum @enum) where TEnum : struct
        {
            var value = @enum.GetHashCode();

            return (TEnum)Enum.ToObject(typeof(TEnum), value);
        }
    }
}
