﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Dto;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 直营门店
    /// </summary>
    public class ContactsController : BaseApiController
    {
        private readonly IVersionsService _versionsService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="versionsService"></param>
        public ContactsController(IVersionsService versionsService)
        {
            _versionsService = versionsService;
        }

        /// <summary>
        /// 添加直营门店并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2218">门店状态不一致,请重新编辑</response>
        /// <response code="2219">门店营业时间不一致,请重新编辑</response>
        /// <response code="2220">门店传真不一致,请重新编辑</response>
        /// <response code="2221">门店电话不一致,请重新编辑</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddContactsVersions([FromBody] BaseRequest<AddContactsVersionsRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                ContactsData saveData = new ContactsData()
                {
                    CNInfo = request.Body.CNInfo,
                    ENInfo = request.Body.ENInfo
                };

                VersionsDto<ContactsData> versions = new VersionsDto<ContactsData>()
                {
                    VersionType = EnumVersionType.Contacts.GetHashCode(),
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    Title = saveData.CNInfo.StoreName,
                    Content = "",
                    Data = saveData,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                //if (request.Body.CNInfo.StoreStatus != request.Body.ENInfo.StoreStatus)
                //{
                //    throw new BusinessException(ErrorCode.ContactsStatusError.ToDescriptionName(), ErrorCode.ContactsStatusError.GetHashCode());
                //}
                if (request.Body.CNInfo.StoreStartTime != request.Body.ENInfo.StoreStartTime || request.Body.CNInfo.StoreEndTime != request.Body.ENInfo.StoreEndTime)
                {
                    throw new BusinessException(ErrorCode.ContactsTimeError.ToDescriptionName(), ErrorCode.ContactsTimeError.GetHashCode());
                }
                //if (request.Body.CNInfo.StoreFax != request.Body.ENInfo.StoreFax)
                //{
                //    throw new BusinessException(ErrorCode.ContactsFaxError.ToDescriptionName(), ErrorCode.ContactsFaxError.GetHashCode());
                //}
                if (request.Body.CNInfo.StorePhoneList.Except(request.Body.ENInfo.StorePhoneList).ToList().Count > 0)
                {
                    throw new BusinessException(ErrorCode.ContactsPhoneError.ToDescriptionName(), ErrorCode.ContactsPhoneError.GetHashCode());
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.AddVersions(entity);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddContactsVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取直营门店详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetVersionsResponse<ContactsData>>> GetContactsVersions([FromBody] BaseRequest<GetContactsIdRequest> request)
        {
            GetVersionsResponse<ContactsData> result = null;
            try
            {
                result = await _versionsService.GetVersions<ContactsData>(EnumVersionType.Contacts.GetHashCode(), request.Body.ContactsId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetContactsVersions Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 修改直营门店并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <response code="2208">状态为已发布,暂时无法编辑</response>
        /// <response code="2209">审核状态不为待提交或审核驳回,暂时无法编辑</response>
        /// <response code="2218">门店状态不一致,请重新编辑</response>
        /// <response code="2219">门店营业时间不一致,请重新编辑</response>
        /// <response code="2220">门店传真不一致,请重新编辑</response>
        /// <response code="2221">门店电话不一致,请重新编辑</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditContactsVersions([FromBody] BaseRequest<EditContactsVersionsRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                ContactsData saveData = new ContactsData()
                {
                    CNInfo = request.Body.CNInfo,
                    ENInfo = request.Body.ENInfo
                };

                VersionsDto<ContactsData> versions = new VersionsDto<ContactsData>()
                {
                    Id = request.Body.ContactsId,
                    VersionType = EnumVersionType.Contacts.GetHashCode(),
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    Title = saveData.CNInfo.StoreName,
                    Content = "",
                    Data = saveData,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                //if (request.Body.CNInfo.StoreStatus != request.Body.ENInfo.StoreStatus)
                //{
                //    throw new BusinessException(ErrorCode.ContactsStatusError.ToDescriptionName(), ErrorCode.ContactsStatusError.GetHashCode());
                //}
                if (request.Body.CNInfo.StoreStartTime != request.Body.ENInfo.StoreStartTime || request.Body.CNInfo.StoreEndTime != request.Body.ENInfo.StoreEndTime)
                {
                    throw new BusinessException(ErrorCode.ContactsTimeError.ToDescriptionName(), ErrorCode.ContactsTimeError.GetHashCode());
                }
                //if (request.Body.CNInfo.StoreFax != request.Body.ENInfo.StoreFax)
                //{
                //    throw new BusinessException(ErrorCode.ContactsFaxError.ToDescriptionName(), ErrorCode.ContactsFaxError.GetHashCode());
                //}
                if (request.Body.CNInfo.StorePhoneList.Except(request.Body.ENInfo.StorePhoneList).ToList().Count > 0)
                {
                    throw new BusinessException(ErrorCode.ContactsPhoneError.ToDescriptionName(), ErrorCode.ContactsPhoneError.GetHashCode());
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.EditVersions(versions);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditContactsVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> ContactsExistsRelease()
        {
            bool result = false;
            try
            {
                result = await _versionsService.ExistsRelease(EnumVersionType.Contacts.GetHashCode());
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("ExistsRelease Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 删除版本信息
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <response code="2210">状态为已发布,暂时无法删除</response>
        /// <response code="2211">审核状态为待审核,暂时无法删除</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteContactsVersions([FromBody] BaseRequest<GetContactsIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    Id = request.Body.ContactsId,
                    VersionType = EnumVersionType.Contacts.GetHashCode(),
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now,
                };

                result = await _versionsService.DeleteVersions(versions);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 版本信息审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <response code="2214">审核状态不为待审核,暂时无法审核</response>
        /// <response code="2237">当前发布数量已达上限，请先下架后再操作</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> ContactsVersionsApprove([FromBody] BaseRequest<ContactsVersionsApproveRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    Id = request.Body.ContactsId,
                    VersionType = EnumVersionType.Contacts.GetHashCode(),
                    ApproverStatus = request.Body.ApproverStatus,
                    ApprovalOpinion = request.Body.ApprovalOpinion,
                    ApproverId = user.UserId,
                    Approver = user.UserName.ToString() ?? "system",
                    ApprovalTime = DateTime.Now,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _versionsService.VersionsApprove(versions);
                return Success("审核成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("VersionsApprove Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取直营门店列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<GetContactsPageListResponse>>> GetContactsPageList([FromBody] RequestPageModel<GetContactsPageListRequest> request)
        {
            PageRows<GetContactsPageListResponse> pageRows = null;
            try
            {
                var user = GetUserInfo();

                pageRows = await _versionsService.GetContactsPageList(user.UserId, request.RequestParams.StoreName, request.RequestParams.ReleaseStatus, request.RequestParams.ApproverStatus, request.RequestParams.ReleaseTimeScope, request.OrderFile, request.SortType, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetContactsPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<VersionsListResponse>>> GetContactsReleaseVersions()
        {
            List<VersionsListResponse> pageRows = null;
            try
            {
                pageRows = await _versionsService.GetAllReleaseVersions();

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetAllReleaseVersions " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 版本发布/下线
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <response code="2216">版本已发布</response>
        /// <response code="2217">版本审核状态不为审核通过,暂时无法发布</response>
        /// <response code="2237">当前发布数量已达上限，请先下架后再操作</response>
        /// <response code="2238">版本未发布</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditContactsVersionsReleaseStatus([FromBody] BaseRequest<EditContactsVersionsReleaseStatus> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    VersionType = EnumVersionType.Contacts.GetHashCode(),
                    Id = request.Body.VersionsId.Value,
                    ReleaseStatus = request.Body.ReleaseStatus.Value,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now,
                };

                result = await _versionsService.VersionsRelease(versions);
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("VersionsRelease Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }
    }
}
