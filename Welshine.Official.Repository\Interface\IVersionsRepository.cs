﻿using DTHY.Core.Repository;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 版本仓储接口
    /// </summary>
    public interface IVersionsRepository : IBaseRepository<Versions>
    {
        /// <summary>
        /// 判断版本标题是否存在
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <param name="title">标题</param>
        /// <returns></returns>
        Task<bool> ExistsTitle(int versionType, string title);

        /// <summary>
        /// 判断版本标题是否存在
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <param name="title">标题</param>
        /// <param name="versionId">版本Id</param>
        /// <returns></returns>
        Task<bool> ExistsTitle(int versionType, string title, long versionId);

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <returns></returns>
        Task<bool> ExistsRelease(int versionType);

        /// <summary>
        /// 添加版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        Task<bool> AddVersions(Versions versions);

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <param name="versionsId">版本Id</param>
        /// <returns></returns>
        Task<Versions> GetVersions(long versionsId);

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <param name="versionsId">版本Id</param>
        /// <returns></returns>
        Task<Versions> GetVersions(int versionType, long versionsId);

        /// <summary>
        /// 修改版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        Task<bool> EditVersions(Versions versions);

        /// <summary>
        /// 根据类型设置所有版本发布状态为未发布
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <param name="status">发布状态</param>
        /// <returns></returns>
        Task<int> ResetReleaseStatus(int versionType);

        /// <summary>
        /// 获取版本列表
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="VersionType">版本类型</param>
        /// <param name="title">版本标题</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<VersionsListResponse>> GetVersionsPageList(string userId, int VersionType, string title, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取所有已发布的版本
        /// </summary>
        /// <returns></returns>
        Task<List<VersionsListResponse>> GetAllReleaseVersions();

        /// <summary>
        /// 获取版本列表(直营门店)
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="storeName">门店名称</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<GetContactsPageListResponse>> GetContactsPageList(string userId, string storeName, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取版本列表(直营门店)
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<GetWebContactsPageListResponse> GetWebContactsPageList(int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取已发布中文产品
        /// </summary>
        /// <returns></returns>
        Task<Versions> GetProductCN();

        /// <summary>
        /// 获取当前已发布版本数据
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <returns></returns>
        Task<int> GetReleaseCount(int versionType);

    }
}
