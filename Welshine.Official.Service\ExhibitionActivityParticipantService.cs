﻿using System.Collections.Generic;
using System.Linq.Expressions;
using System;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service.Interface;
using AutoMapper;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Repository;
using Welshine.Official.Core.SNGeneration;
using SqlSugar.IOC;
using SqlSugar;

namespace Welshine.Official.Service
{
    public class ExhibitionActivityParticipantService : IExhibitionActivityParticipantService
    {

        IExhibitionActivityRepository _exhibitionActivityRepository;
        IExhibitionActivityParticipantRepository _exhibitionActivityParticipantRepository;
        IMapper _mapper;
        IAreaRepository _areaRepository;

        private const double EarthRadiusKm = 6371.0; // 地球平均半径，单位：公里  

        public ExhibitionActivityParticipantService(IExhibitionActivityRepository exhibitionActivityRepository, IExhibitionActivityParticipantRepository exhibitionActivityParticipantRepository, IMapper mapper, IAreaRepository areaRepository)
        {
            _exhibitionActivityRepository = exhibitionActivityRepository;
            _exhibitionActivityParticipantRepository = exhibitionActivityParticipantRepository;
            _mapper = mapper;
            _areaRepository = areaRepository;
        }

        /// <summary>
        /// 展会活动参与人员列表
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="orderBy"></param>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="name">姓名</param>
        /// <param name="mobile">电话</param>
        /// <param name="code">券码</param>
        /// <param name="createdStartTime">参与开始时间</param>
        /// <param name="createdEndTime">参与结束时间</param>
        /// <param name="type">类型:0->经销;1->终端;</param>
        /// <param name="isScene">是否现场客户:0->否;1->是;</param>
        /// <returns></returns>
        public async Task<PageRows<ExhibitionActivityParticipantResponse>> GetExhibitionActivityParticipantPageList(int pageIndex, int pageSize, string orderBy, long exhibitionActivityId, string name, string mobile, string code, DateTime? createdStartTime, DateTime? createdEndTime, int? type, int? isScene)
        {
            var result = new PageRows<ExhibitionActivityParticipantResponse>();
            var totalNumb = 0;
            var data = await DbScoped.SugarScope.Queryable<ExhibitionActivityParticipant>()
                .Where(x => x.ExhibitionActivityId == exhibitionActivityId)
                .WhereIF(!string.IsNullOrEmpty(name), x => x.Name.Contains(name))
                .WhereIF(mobile != null, x => x.Mobile == mobile)
                .WhereIF(code != null, x => x.Code == code)
                .WhereIF(type != null, x => x.Type == type)
                .WhereIF(isScene != null, x => x.IsScene == isScene)
                .WhereIF(createdStartTime != null, x => x.CreatedTime >= createdStartTime)
                .WhereIF(createdEndTime != null, x => x.CreatedTime < createdEndTime)
            .Where(x => x.IsDeleted == false)
                .OrderByDescending(x => x.CreatedTime)
                .ToPageListAsync(pageIndex, pageSize, totalNumb);
            var resultData = new List<ExhibitionActivityParticipantResponse>();
            if (data.Count > 0)
            {
                foreach (ExhibitionActivityParticipant item in data)
                {
                    ExhibitionActivityParticipantResponse response = new ExhibitionActivityParticipantResponse();
                    response.Name = item.Name;
                    response.Mobile = item.Mobile;
                    response.Type = item.Type == 0 ? "经销" : "终端";
                    response.Company = item.Company;
                    response.ProvinceName = item.ProvinceName;
                    response.CityName = item.CityName;
                    response.Code = item.Code;
                    response.IsScene = item.IsScene == 0 ? "否" : "是";
                    response.CreatedTime = item.CreatedTime;

                    resultData.Add(response);
                }
            }
            result.Total = totalNumb;
            result.Data = resultData;
            return result;
        }

        /// <summary>
        /// 展会活动参与人员总数
        /// </summary>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="name">姓名</param>
        /// <param name="mobile">电话</param>
        /// <param name="code">券码</param>
        /// <param name="createdStartTime">参与开始时间</param>
        /// <param name="createdEndTime">参与结束时间</param>
        /// <param name="type">类型:0->经销;1->终端;</param>
        /// <param name="isScene">是否现场客户:0->否;1->是;</param>
        /// <returns></returns>
        public async Task<int> GetExhibitionActivityParticipantCount(long exhibitionActivityId, string name, string mobile, string code, DateTime? createdStartTime, DateTime? createdEndTime, int? type, int? isScene)
        {
            Expression<Func<ExhibitionActivityParticipant, bool>> where = x => x.ExhibitionActivityId == exhibitionActivityId && x.IsDeleted == false;
            if (!string.IsNullOrWhiteSpace(name))
            {
                where = where.And(x => x.Name.Contains(name));
            }
            if (!string.IsNullOrWhiteSpace(mobile))
            {
                where = where.And(x => x.Mobile.Contains(mobile));
            }
            if (!string.IsNullOrWhiteSpace(code))
            {
                where = where.And(x => x.Code.Contains(code));
            }
            if (createdStartTime != null && createdEndTime != null)
            {
                where = where.And(x => x.CreatedTime >= createdStartTime && x.CreatedTime <= createdEndTime);
            }
            if (type != null)
            {
                where = where.And(x => x.Type == type.Value);
            }
            if (isScene != null)
            {
                where = where.And(x => x.IsScene == isScene.Value);
            }
            return await _exhibitionActivityParticipantRepository.CountEntity(where);
        }


        /// <summary>
        /// 获取参与人员信息
        /// </summary>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="openId">用户openid</param>
        /// <returns></returns>
        public async Task<ExhibitionActivityParticipant> GetParticipantCheckInInfo(long exhibitionActivityId, string openId)
        {
            ExhibitionActivityParticipant exhibitionActivityParticipant = await _exhibitionActivityParticipantRepository.GetEntity(x=>x.ExhibitionActivityId == exhibitionActivityId && x.OpenId == openId);
            return exhibitionActivityParticipant;
        }

        /// <summary>
        /// 展会活动参与人员登记
        /// </summary>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="name">姓名</param>
        /// <param name="mobile">手机</param>
        /// <param name="type">类型:0->经销;1->终端;</param>
        /// <param name="company">公司名称</param>
        /// <param name="provinceId">省id</param>
        /// <param name="cityId">市id</param>
        /// <param name="longitude">经度</param>
        /// <param name="latitude">纬度</param>
        /// <param name="openId">用户openid</param>
        /// <returns></returns>
        public async Task<string> ParticipantCheckIn(long exhibitionActivityId, string name, string mobile, int type, string company, long? provinceId, long? cityId, decimal longitude, decimal latitude, string openId)
        {
            ExhibitionActivity exhibitionActivity = await _exhibitionActivityRepository.GetEntity(x => !x.IsDeleted && x.Id == exhibitionActivityId);
            if(exhibitionActivity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityNoFoundError);
            }
            if (exhibitionActivity.Status == 0)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityEndError);
            }

            ExhibitionActivityParticipant exhibitionActivityParticipant = await _exhibitionActivityParticipantRepository.GetEntity(x => x.ExhibitionActivityId == exhibitionActivityId && x.OpenId == openId);
            if (exhibitionActivityParticipant == null)
            {
                exhibitionActivityParticipant = new ExhibitionActivityParticipant();
                exhibitionActivityParticipant.ExhibitionActivityId = exhibitionActivityId;
                exhibitionActivityParticipant.Name = name;
                exhibitionActivityParticipant.Mobile = mobile;
                exhibitionActivityParticipant.Type = type;
                exhibitionActivityParticipant.Company = company;
                exhibitionActivityParticipant.IsScene = CalculateDistance(exhibitionActivity.AddressLatitude, exhibitionActivity.AddressLongitude, latitude, longitude) <= 1 ? 1 : 0;
                exhibitionActivityParticipant.Code = await CodeHelper.GetExhibitionActivityCode(exhibitionActivity.Id, exhibitionActivity.CodePrefix);
                exhibitionActivityParticipant.OpenId = openId;
                exhibitionActivityParticipant.CreatedTime = DateTime.Now;
                if (provinceId != null)
                {
                    Area provinceArea = await _areaRepository.GetEntity(x => x.Id == provinceId.Value && x.Level == 0);
                    if (provinceArea == null)
                    {
                        throw new BusinessException(ErrorCode.ProvinceNoFoundError);
                    }
                    exhibitionActivityParticipant.ProvinceId = provinceArea.Id;
                    exhibitionActivityParticipant.ProvinceName = provinceArea.Name;
                }
                if (cityId != null)
                {
                    Area cityArea = await _areaRepository.GetEntity(x => x.Id == cityId.Value && x.Level == 1);
                    if (cityArea == null)
                    {
                        throw new BusinessException(ErrorCode.CityNoFoundError);
                    }
                    exhibitionActivityParticipant.CityId = cityArea.Id;
                    exhibitionActivityParticipant.CityName = cityArea.Name;
                }

                await _exhibitionActivityParticipantRepository.Add(exhibitionActivityParticipant);
            }
            return exhibitionActivityParticipant.Code;
        }

        public decimal CalculateDistance(decimal lat1, decimal lon1, decimal lat2, decimal lon2)
        {
            // 将decimal经纬度转换为double进行计算  
            double lat1Rad = Math.PI * Convert.ToDouble(lat1) / 180.0;
            double lon1Rad = Math.PI * Convert.ToDouble(lon1) / 180.0;
            double lat2Rad = Math.PI * Convert.ToDouble(lat2) / 180.0;
            double lon2Rad = Math.PI * Convert.ToDouble(lon2) / 180.0;

            // 哈弗赛公式  
            double dLat = lat2Rad - lat1Rad;
            double dLon = lon2Rad - lon1Rad;

            double a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                       Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                       Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            double c = 2 * Math.Asin(Math.Sqrt(a));

            double distanceKm = EarthRadiusKm * c; // 转换为公里  

            // 如果需要将结果转换为decimal（尽管这里可能不太需要）  
            decimal distanceDecimal = Convert.ToDecimal(distanceKm);

            return distanceDecimal;
        }

    }
}
