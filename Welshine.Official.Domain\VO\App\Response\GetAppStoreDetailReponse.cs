﻿using System;
using System.Collections.Generic;
using System.Text;
using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.VO.App.Response
{
    /// <summary>
    /// 
    /// </summary>
    public class GetAppStoreDetailReponse
    {
        /// <summary>
        /// 主键
        /// </summary>
        public long StoreId { get; set; }
        

        /// <summary>
        /// 门店名称
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// 门店地址
        /// </summary>
        public string StoreAddress { get; set; }

        /// <summary>
        /// 门店开始营业时间
        /// </summary>
        public string StoreStartTime { get; set; }

        /// <summary>
        /// 门店结束营业时间
        /// </summary>
        public string StoreEndTime { get; set; }

    

        /// <summary>
        /// 手机
        /// </summary>
        public string StorePhone { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        public string StoreFax { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string StoreEmail { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public decimal StoreLongitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        public decimal StoreLatitude { get; set; }

       
        /// <summary>
        /// 是否首页
        /// </summary>
        public bool StoreShowHomepage { get; set; }
        /// <summary>
        /// 略缩图地址
        /// </summary>
        public string StoreThumbnailUrl { get; set; }

        /// <summary>
        /// 联系图地址
        /// </summary>
        public string StoreRelationUrl { get; set; }
        /// <summary>
        /// 门店详情图
        /// </summary>
        public List<FileDto> StoreDetailUrlList { get; set; }
    }
}
