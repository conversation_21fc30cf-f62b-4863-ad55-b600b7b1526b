﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 校验时间
    /// </summary>
    public class DateTimeValueAttibute : ValidationAttribute
    {
        private const string _errorMessage = "时间范围错误";
        private long _dateToCompare = 0;
        EnumDateTimeCompare dateTimeCompare = EnumDateTimeCompare.LessThen;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dateToCompare"></param>
        public DateTimeValueAttibute(long dateToCompare, EnumDateTimeCompare compare)
            : base(_errorMessage)
        {

            dateTimeCompare = compare;
            _dateToCompare = dateToCompare;
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="value"></param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                var time = DateTime.UtcNow.AddSeconds(_dateToCompare);
                if (dateTimeCompare == EnumDateTimeCompare.LessThen && (DateTime)value > time)
                {
                    return new ValidationResult(ErrorMessage);
                }
                if (dateTimeCompare == EnumDateTimeCompare.GreaterThan && (DateTime)value < time)
                {
                    return new ValidationResult(ErrorMessage);
                }

            }
            return ValidationResult.Success;
        }
    }
    /// <summary>
    /// 时间比较枚举
    /// </summary>
    public enum EnumDateTimeCompare
    {
        /// <summary>
        /// 小于
        /// </summary>
        LessThen = 0,
        /// <summary>
        /// 大于
        /// </summary>
        GreaterThan = 1

    }
}
