﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加展会活动条目
    /// </summary>
    public class AddExhibitionActivityRequest
    {
        /// <summary>
        /// 活动名称
        /// </summary>
        [Required(ErrorMessage = "活动名称必填,请完善")]
        [StringLength(20, ErrorMessage = "活动名称长度不符", MinimumLength = 1)]
        public string ExhibitionActivityName { get; set; }

        /// <summary>
        /// 活动开始时间
        /// </summary>
        [Required(ErrorMessage = "活动开始时间为必填")]
        public DateTime ExhibitionActivityStartTime { get; set; }

        /// <summary>
        /// 活动结束时间
        /// </summary>
        [Required(ErrorMessage = "活动结束时间为必填")]
        public DateTime ExhibitionActivityEndTime { get; set; }

        /// <summary>
        /// 活动地点
        /// </summary>
        [Required(ErrorMessage = "活动地点必填,请完善")]
        [StringLength(100, ErrorMessage = "活动地点长度限制,请完善", MinimumLength = 1)]
        public string ExhibitionActivityAddress { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        [Required(ErrorMessage = "经度是必填项,请填写后再提交")]
        [Range(-180.0d, 180.0d, ErrorMessage = "经度错误")]
        public decimal AddressLongitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        [Required(ErrorMessage = "纬度是必填项,请填写后再提交")]
        [Range(-90.0d, 90.0d, ErrorMessage = "纬度错误")]
        public decimal AddressLatitude { get; set; }

        /// <summary>
        /// 券码前缀
        /// </summary>
        [Required(ErrorMessage = "券码前缀必填,请完善")]
        [StringLength(2, ErrorMessage = "券码前缀长度不符", MinimumLength = 1)]
        public string CodePrefix { get; set; }

        /// <summary>
        /// 活动入口图
        /// </summary>
        [Required(ErrorMessage = "活动入口图必填,请完善")]
        [StringLength(128, ErrorMessage = "活动入口图长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9a-zA-Z-_]+$", ErrorMessage = "活动入口图格式错误")]
        public string EntryPicture { get; set; }

        /// <summary>
        /// 活动参与图
        /// </summary>
        [Required(ErrorMessage = "活动参与图必填,请完善")]
        [StringLength(128, ErrorMessage = "活动参与图长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9a-zA-Z-_]+$", ErrorMessage = "活动参与图格式错误")]
        public string ParticipationPicture { get; set; }

        /// <summary>
        /// 活动券码图
        /// </summary>
        [Required(ErrorMessage = "活动券码图必填,请完善")]
        [StringLength(128, ErrorMessage = "活动券码图长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9a-zA-Z-_]+$", ErrorMessage = "略缩图文件Id格式错误")]
        public string CodePicture { get; set; }

    }
}
