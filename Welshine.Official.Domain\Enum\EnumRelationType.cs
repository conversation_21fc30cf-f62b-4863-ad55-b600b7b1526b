﻿using System.ComponentModel;

namespace Welshine.Official.Domain.Enum
{
    /// <summary>
    /// 关联类型
    /// </summary>
    public enum EnumRelationType
    {
        /// <summary>
        /// 产品略缩图
        /// </summary>
        [Description("产品略缩图")]
        ProductThumbnail = 0,

        /// <summary>
        /// 产品详情图
        /// </summary>
        [Description("产品详情图")]
        ProductDetailPicture = 1,

        /// <summary>
        /// 门店详情图
        /// </summary>
        [Description("门店详情图")]
        StoreDetailPicture = 2,

        /// <summary>
        /// 展会略缩图
        /// </summary>
        [Description("展会略缩图")]
        ExhibitionThumbnail = 3,

        /// <summary>
        /// 展会详情图
        /// </summary>
        [Description("展会详情图")]
        ExhibitionDetailPicture = 4,

        /// <summary>
        /// 图册首页图
        /// </summary>
        [Description("图册首页图")]
        AtlasThumbnail = 5,

        /// <summary>
        /// 图册文件
        /// </summary>
        [Description("图册文件")]
        AtlasFile = 6,

        /// <summary>
        /// 分类产品图
        /// </summary>
        [Description("分类产品图")]
        CategoryProductPicture = 7,

        /// <summary>
        /// 分类产品角标图
        /// </summary>
        [Description("分类产品角标图")]
        CategoryProductCornerMarkPicture = 8, 
    }
}
