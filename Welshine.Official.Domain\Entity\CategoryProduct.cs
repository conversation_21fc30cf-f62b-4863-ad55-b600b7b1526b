﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 分类产品
    /// </summary>
    [SqlSugar.SugarTable("cms_category_product")]
    public class CategoryProduct : BaseBigEntity
    {
        /// <summary>
        /// 语言: 0->英文; 1->中文
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "product_language")]

        public int ProductLanguage { get; set; }

        /// <summary>
        /// 分类Id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "category_id")]
        public long CategoryId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "product_name")]
        public string ProductName { get; set; }

        /// <summary>
        /// 材质
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "product_texture")]
        public string ProductTexture { get; set; }

        /// <summary>
        /// 颜色
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "product_color")]
        public string ProductColor { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "product_specification")]
        public string ProductSpecification { get; set; }

        /// <summary>
        /// 商品介绍
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "product_introduce")]
        public string ProductIntroduce { get; set; }

        /// <summary>
        /// 发布状态: 0->未发布; 1->已发布;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "release_status")]
        public int ReleaseStatus { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "release_time")]
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "sort")]
        public int? Sort { get; set; }
    }
}
