﻿using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Newtonsoft.Json;
using Polly.Caching;
using SqlSugar;
using SqlSugar.Extensions;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Welshine.Official.Core;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.JwtToken;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Service
{
    public class AccountService
    {
        public PageRows<AccountResponse> FindAccountPageList(string userNo,string loginName,string tel, TimeHorizon timeHorizon
            , long? departmentId,long? postId,bool? status,int pageIndex, int pageSize,string orderfield, OrderByType orderByType)
        {
            var totalCount = 0;
            var data = DbScoped.SugarScope.Queryable<Account>()
                .LeftJoin<Department>((x, y) => x.DepartmentId == y.Id)
                .LeftJoin<Post>((x, y, z) => x.PostId == z.Id)
                .Where(x => x.DepartmentId != 0 && x.IsDeleted == false)
                .WhereIF(status!=null,x=>x.status == status)
                .WhereIF(departmentId!=null,x=>x.DepartmentId == departmentId)
                .WhereIF(postId != null, x => x.PostId == postId)
                .WhereIF(!string.IsNullOrEmpty(userNo), x => SqlFunc.StartsWith(x.UserNo, userNo))
                .WhereIF(!string.IsNullOrEmpty(loginName), x => SqlFunc.StartsWith(x.LoginName, loginName))
                .WhereIF(!string.IsNullOrEmpty(tel), x => SqlFunc.StartsWith(x.Tel, tel))
                .WhereIF(timeHorizon != null,x=>x.CreatedTime>= timeHorizon.From.Value)
                .WhereIF(timeHorizon != null,x=>x.CreatedTime< timeHorizon.To.Value)
                .OrderByIF(orderfield == "createdTime", x=>x.CreatedTime, orderByType)
                .OrderByIF(orderfield == "loginTime", x => x.LoginTime, orderByType)
                .Select((x,y,z) => new AccountResponse()
                {
                    Id = x.Id,
                    LoginName= x.LoginName,
                    Tel = x.Tel,
                    Status = x.status,
                    LoginTime = x.LoginTime,
                    CreatedTime = x.CreatedTime,
                    UpdatedTime = x.UpdatedTime.Value,
                    UserName = x.UserName,
                    UserNo = x.UserNo,
                    DepartmentId = x.DepartmentId,
                    DepartmentName = y.DepartmentName,
                    PostId = x.PostId,
                    PostName = z.PostName
                })
                .ToPageList(pageIndex, pageSize, ref totalCount);
            var result = new PageRows<AccountResponse>();
            result.Total = totalCount;
            result.Data = data;
            return result;
        }

        public void AddAccount(long departmentId,long postId,string loginName,string userName,string tel, JwtTokenUserInfo userInfo)
        {
            var anyDepartment = DbScoped.SugarScope.Queryable<Department>().Where(x => x.Id == departmentId).Any();
            if (!anyDepartment)
            {
                throw new BusinessException(ErrorCode.DepartmentNotExist);
            }
            var post = DbScoped.SugarScope.Queryable<Post>().Where(x => x.Id == postId).First();
            if (post == null)
            {
                throw new BusinessException(ErrorCode.PostNotExist);
            }
            if (post.DepartmentId != departmentId)
            {
                throw new BusinessException(ErrorCode.DepartmentPostNotMatch);
            }
            var anyRepeat = DbScoped.SugarScope.Queryable<Account>().Where(x => x.LoginName == loginName).Any();
            if (anyRepeat)
            {
                throw new BusinessException(ErrorCode.LoginNameRepeat);
            }
            anyRepeat = DbScoped.SugarScope.Queryable<Account>().Where(x=>x.UserName == userName).Any();
            if(anyRepeat)
            {
                throw new BusinessException(ErrorCode.UserNameRepeat);
            }
            anyRepeat = DbScoped.SugarScope.Queryable<Account>().Where(x => x.Tel == tel).Any();
            if(anyRepeat)
            {
                throw new BusinessException(ErrorCode.TelRepeat);
            }
            Account account = new Account();
            account.UserNo = GetUserNo();
            account.Tel = tel;
            account.UserName = userName;
            account.LoginName = loginName;
            account.DepartmentId = departmentId;
            account.PostId = postId;
            account.CreatedBy = userInfo.UserName;
            account.UpdatedBy = userInfo.UserName;
            account.CreatedTime = DateTime.Now;
            account.UpdatedTime = DateTime.Now;
            account.CreatorId = userInfo.UserId;
            account.ModifierId = userInfo.UserId;
            account.LoginTime = null;
            account.status = true;
            account.IsFirstLogin = true;
            account.Password = BCrypt.Net.BCrypt.EnhancedHashPassword("1qaz1qaz");
            DbScoped.SugarScope.Insertable(account).ExecuteCommand();
        }

        public void ChangeAccountStatus(long accountId,bool status,JwtTokenUserInfo userInfo)
        {
            var account = DbScoped.SugarScope.Queryable<Account>().Where(x => x.Id == accountId).First();
            if(account == null)
            {
                throw new BusinessException(ErrorCode.AccountNotExist);
            }
            if (account.IsDeleted)
            {
                throw new BusinessException(ErrorCode.AccountDeleted);
            }
            if(userInfo.UserName == account.UserName)
            {
                throw new BusinessException(ErrorCode.ChangeStatusError);
            }
            account.status = status;
            DbScoped.SugarScope.Updateable(account).ExecuteCommand();
            if (status == false)
            {
                FreeRedisHelper.DefaultInstance.Del("AccountToken:" + account.LoginName);
            }
        }

        public void EditAccount(long accountId,long departmentId,long postId,string userName,string tel)
        {
            var account = DbScoped.SugarScope.Queryable<Account>().Where(x => x.Id == accountId).First();
            if (account == null)
            {
                throw new BusinessException(ErrorCode.AccountNotExist);
            }
            var anyDepartment = DbScoped.SugarScope.Queryable<Department>().Where(x => x.Id == departmentId).Any();
            if (!anyDepartment)
            {
                throw new BusinessException(ErrorCode.DepartmentNotExist);
            }
            var post = DbScoped.SugarScope.Queryable<Post>().Where(x => x.Id == postId).First();
            if (post == null)
            {
                throw new BusinessException(ErrorCode.PostNotExist);
            }
            if (post.DepartmentId != departmentId)
            {
                throw new BusinessException(ErrorCode.DepartmentPostNotMatch);
            }
            var anyRepeat = DbScoped.SugarScope.Queryable<Account>().Where(x => x.UserName == userName).Where(x=>x.Id != accountId).Any();
            if (anyRepeat)
            {
                throw new BusinessException(ErrorCode.UserNameRepeat);
            }
            anyRepeat = DbScoped.SugarScope.Queryable<Account>().Where(x => x.Tel == tel).Where(x => x.Id != accountId).Any();
            if (anyRepeat)
            {
                throw new BusinessException(ErrorCode.TelRepeat);
            }
            account.DepartmentId = departmentId;
            account.PostId = postId;
            account.UserName = userName;
            account.Tel = tel;
            DbScoped.SugarScope.Updateable(account).ExecuteCommand();
        }

        public void AlterPassword(string loginName,string oldPassword,string newPassword)
        {
            var account = DbScoped.SugarScope.Queryable<Account>().Where(x => x.LoginName == loginName).First();
            var isMatch = BCrypt.Net.BCrypt.EnhancedVerify(oldPassword, account.Password);
            if (!isMatch)
            {
                throw new BusinessException(ErrorCode.AccountLoginError);
            }
            var newEncodePassword = BCrypt.Net.BCrypt.EnhancedHashPassword(newPassword);
            account.Password = newEncodePassword;
            DbScoped.SugarScope.Updateable(account).ExecuteCommand();
            FreeRedisHelper.DefaultInstance.Del("AccountToken:" + account.LoginName);
        }

        public void ResetAccountPassword(long accountId,JwtTokenUserInfo userInfo)
        {
            var account = DbScoped.SugarScope.Queryable<Account>().Where(x => x.Id == accountId).First();
            if(account==null)
            {
                throw new BusinessException(ErrorCode.AccountNotExist);
            }
            if (userInfo.UserName == account.UserName)
            {
                throw new BusinessException(ErrorCode.ChangeStatusError);
            }
            account.Password = BCrypt.Net.BCrypt.EnhancedHashPassword("1qaz1qaz");
            DbScoped.SugarScope.Updateable(account).ExecuteCommand();
            FreeRedisHelper.DefaultInstance.Del("AccountToken:" + account.LoginName);
        }

        public void FreshAccountLoginTime(Account account)
        {
            account.LoginTime = DateTime.Now;
            account.IsFirstLogin = false;
            DbScoped.SugarScope.Updateable(account).ExecuteCommand();
        }

        public PageRows<AccountResponse> SearchAccount(string userName,int pageIndex,int pageSize)
        {
            var totalCount = 0;
            var data = DbScoped.SugarScope.Queryable<Account>()
                .LeftJoin<Department>((x, y) => x.DepartmentId == y.Id)
                .LeftJoin<Post>((x, y, z) => x.PostId == z.Id)
                .Where(x => x.DepartmentId != 0 && x.IsDeleted == false)
                .WhereIF(!string.IsNullOrEmpty(userName), x => SqlFunc.Contains(x.UserName, userName))
                .Select((x, y, z) => new AccountResponse()
                {
                    Id = x.Id,
                    LoginName = x.LoginName,
                    Tel = x.Tel,
                    Status = x.status,
                    LoginTime = x.LoginTime.Value,
                    CreatedTime = x.CreatedTime,
                    UpdatedTime = x.UpdatedTime.Value,
                    UserName = x.UserName,
                    DepartmentId = x.DepartmentId,
                    DepartmentName = y.DepartmentName,
                    PostId = x.PostId,
                    PostName = z.PostName
                })
                .ToPageList(pageIndex, pageSize, ref totalCount);
            var result = new PageRows<AccountResponse>();
            result.Total = totalCount;
            result.Data = data;
            return result;
        }

        public Account GetAccountByLoginName(string loginName)
        {
            var result = DbScoped.SugarScope.Queryable<Account>().Where(x => x.LoginName == loginName).First();
            return result;
        }

        public LoginResponse Login(string loginName,string password)
        {
            var result = new LoginResponse();
            var account = DbScoped.SugarScope.Queryable<Account>().Where(x => x.LoginName == loginName).Where(x=>x.IsDeleted==false).First();
            if (account == null)
            {
                throw new BusinessException(ErrorCode.AccountNotExist);
            }
            var isMatch = BCrypt.Net.BCrypt.EnhancedVerify(password, account.Password);
            if (isMatch == false)
            {
                throw new BusinessException(ErrorCode.AccountLoginError);
            }
            if (account.status == false)
            {
                throw new BusinessException(ErrorCode.AccountStatusError);
            }
            result.UserId = account.Id;
            result.LoginName = account.LoginName;
            result.UserNo = account.UserNo;
            result.Tel = account.Tel;
            result.UserName = account.UserName;
            result.IsFirstLogin = account.IsFirstLogin;
            var relations = DbScoped.SugarScope.Queryable<AccountRoleRelation>().Where(x => x.Uid == account.Id)
               .LeftJoin<AccountRole>((x, y) => x.Rid == y.Id)
               .LeftJoin<RBACGroup>((x, y, z) => y.Id == z.RoleId)
               .Select((x, y, z) => new { AccountId = x.Uid, RoleId = y.Id, RoleName = y.RoleName, Description = y.Description, MenuJson = z.RBACMenuJson }).ToList();
            var payLoad = new Dictionary<string, object>();
            var roles = new List<JwtTokenUserRole>();
            var menus = new List<string>();
            foreach (var item in relations)
            {
                JwtTokenUserRole role = new JwtTokenUserRole();
                role.roleDesc = item.Description;
                role.id = item.RoleId;
                role.roleName = item.RoleName;
                roles.Add(role);
                if (!string.IsNullOrEmpty(item.MenuJson))
                {
                    var tempMenus = JsonConvert.DeserializeObject<List<string>>(item.MenuJson);
                    menus.AddRange(tempMenus);
                }
            }
            payLoad.Add("UserId", account.Id);
            payLoad.Add("UserName", account.UserName);
            payLoad.Add("LoginName", account.LoginName);
            payLoad.Add("Roles", roles);
            payLoad.Add("Status", account.status);
            var exp = DateTimeOffset.UtcNow.AddHours(12).ToUnixTimeSeconds();
            var token = JwtUtils.Encode(payLoad, exp);
            result.RBACMenu = menus.Distinct().ToList();
            result.Token = token;
            result.Exp = exp;
            FreshAccountLoginTime(account);
            int timeoutSeconds = 60 * 60 * 12;
            FreeRedisHelper.DefaultInstance.Set("AccountToken:"+account.LoginName,token, timeoutSeconds);
            return result;
        }

        public LoginResponse FreshToken(JwtTokenUserInfo userInfo)
        {
            LoginResponse result = new LoginResponse();
            var account = DbScoped.SugarScope.Queryable<Account>().Where(x => x.Id == long.Parse(userInfo.UserId)).Where(x => x.IsDeleted == false).First();
            result.UserId = account.Id;
            result.LoginName = account.LoginName;
            result.UserNo = account.UserNo;
            result.Tel = account.Tel;
            result.UserName = account.UserName;
            result.IsFirstLogin = account.IsFirstLogin;
            var relations = DbScoped.SugarScope.Queryable<AccountRoleRelation>().Where(x => x.Uid == account.Id)
               .LeftJoin<AccountRole>((x, y) => x.Rid == y.Id)
               .LeftJoin<RBACGroup>((x, y, z) => y.Id == z.RoleId)
               .Select((x, y, z) => new { AccountId = x.Uid,RoleId = y.Id, RoleName = y.RoleName,Description = y.Description, MenuJson = z.RBACMenuJson }).ToList();
            var payLoad = new Dictionary<string, object>();
            var roles = new List<JwtTokenUserRole>();
            var menus = new List<string>();
            foreach (var item in relations)
            {
                JwtTokenUserRole role = new JwtTokenUserRole();
                role.roleDesc = item.Description;
                role.id = item.RoleId;
                role.roleName = item.RoleName;
                roles.Add(role);
                if (!string.IsNullOrEmpty(item.MenuJson))
                {
                    var tempMenus = JsonConvert.DeserializeObject<List<string>>(item.MenuJson);
                    menus.AddRange(tempMenus);
                }
            }
            payLoad.Add("UserId", account.Id);
            payLoad.Add("UserName", account.UserName);
            payLoad.Add("LoginName", account.LoginName);
            payLoad.Add("Roles", roles);
            payLoad.Add("Status", account.status);
            var exp = DateTimeOffset.UtcNow.AddHours(12).ToUnixTimeSeconds();
            var token = JwtUtils.Encode(payLoad, exp);
            result.RBACMenu = menus.Distinct().ToList();
            result.Token = token;
            result.Exp = exp;
            int timeoutSeconds = 60 * 60 * 12;
            FreeRedisHelper.DefaultInstance.Set("AccountToken:" + account.LoginName, token, timeoutSeconds);
            return result;
        }

        private string GetUserNo()
        {
            string result = "bsan";
            var value = FreeRedisHelper.DefaultInstance.Get<string>("UserNoDecrement");
            if (value == null)
            {
                var account = DbScoped.SugarScope.Queryable<Account>().OrderByDescending(x => x.Id).First();
                long id = 0l;
                if (account != null)
                {
                    id = account.Id;
                }
                FreeRedisHelper.DefaultInstance.Set("UserNoDecrement", id);
            }
            long count = FreeRedisHelper.DefaultInstance.Incr("UserNoDecrement");
            string countStr = count.ToString().PadLeft(4, '0');
            result = result + countStr;
            return result;
        }

        public void LogOut(JwtTokenUserInfo currentUser)
        {
            FreeRedisHelper.DefaultInstance.Del("AccountToken:" + currentUser.LoginName);
        }
    }
}
