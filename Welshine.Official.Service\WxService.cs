﻿using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Polly;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core;
using Welshine.Official.Core.Config;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class WxService: IWxService
    {
        IOptionsMonitor<AppConfig> _appConfig;
        IHttpClientFactory _httpClientFactory;

        public WxService(IOptionsMonitor<AppConfig> appConfig, IHttpClientFactory httpClientFactory)
        {
            _appConfig = appConfig;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 获取token
        /// </summary>
        /// <returns></returns>
        private async Task<string> GetToken()
        {


            try
            {
                string key = "wxToken:" + _appConfig.CurrentValue.AppId;
                string token = await FreeRedisHelper.DefaultInstance.GetAsync(key);
                if (!string.IsNullOrWhiteSpace(token)) return token;
                string strUrl =
                    $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={_appConfig.CurrentValue.AppId}&secret={_appConfig.CurrentValue.AppSecret}";
                using (var _httpClient = _httpClientFactory.CreateClient())
                {
                    string str = await _httpClient.GetStringAsync(strUrl);
                    var dto = JsonConvert.DeserializeAnonymousType(str, new
                    {
                        access_token = "",
                        expires_in = 0
                    });
                    if (!string.IsNullOrWhiteSpace(dto.access_token))
                    {
                        await FreeRedisHelper.DefaultInstance.SetAsync(key, dto.access_token, dto.expires_in - 5);
                        token = dto.access_token;
                    }
                }
                return token;
            }
            catch (System.Exception ex)
            {
                Log.Error($"WxApp GetToken Ex:{ex.StackTrace + ex.Message}");
                return null;
            }

        }
        /// <summary>
        /// 获取会话信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<string> GetOpenId(string code)
        {
            try
            {
                string strUrl =
                    $"https://api.weixin.qq.com/sns/jscode2session?appId={_appConfig.CurrentValue.AppId}&secret={_appConfig.CurrentValue.AppSecret}&js_code={code}&grant_type=authorization_code";
                string str = "";
                using (var httpClient = _httpClientFactory.CreateClient())
                {
                    str = await httpClient.GetStringAsync(strUrl);
                }
                var dto = JsonConvert.DeserializeAnonymousType(str, new { OpenId = ""});
                return dto.OpenId;
            }
            catch (System.Exception ex)
            {
                Log.Error($"WxServices GetOpenId ex:{ex.Message + ex.InnerException}");
                return null;
            }

        }
        
                /// <summary>
        /// 获取会话信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<string> GetPrinterOpenId(string code)
        {
            try
            {
                string strUrl =
                    $"https://api.weixin.qq.com/sns/jscode2session?appId={_appConfig.CurrentValue.PrinterAppId}&secret={_appConfig.CurrentValue.PrinterAppSecret}&js_code={code}&grant_type=authorization_code";
                string str = "";
                using (var httpClient = _httpClientFactory.CreateClient())
                {
                    str = await httpClient.GetStringAsync(strUrl);
                }
                var dto = JsonConvert.DeserializeAnonymousType(str, new { OpenId = ""});
                return dto.OpenId;
            }
            catch (System.Exception ex)
            {
                Log.Error($"WxServices GetOpenId ex:{ex.Message + ex.InnerException}");
                return null;
            }

        }

        /// <summary>
        /// 删除token
        /// </summary>
        /// <returns></returns>
        private async Task ClearToken()
        {
            string key = "wxToken:" + _appConfig.CurrentValue.AppId;
            await FreeRedisHelper.DefaultInstance.DelAsync(key);
        }
        /// <summary>
        /// 获取用户手机
        /// </summary>
        /// <param name="code">验证码</param>
        /// <returns></returns>
        public async Task<string> GetWxPhone(string code)
        {
            try
            {
                string phone = null;
                //token过期重试一次
                var retryTwoTimesPolicy =
                     Policy
                        .HandleResult<string>(task => task == "40001")
                        .RetryAsync(retryCount: 1,
                        (ex, count) =>
                        {
                        });
                await retryTwoTimesPolicy.ExecuteAsync(async () =>
                {
                    string token = await GetToken();
                    if (string.IsNullOrWhiteSpace(token)) return null;
                    string strUrl = $"https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={token}";
                    using (var _httpClient = _httpClientFactory.CreateClient())
                    {
                        string str = await _httpClient.PostObjAsync(strUrl, new { code }, "application/json");
                        var data = JsonConvert.DeserializeAnonymousType(str,new { errcode = "", phone_info = new { phoneNumber="" } });
                        if (data.errcode != "0" && !string.IsNullOrWhiteSpace(data.errcode))
                        {
                            Log.Warning($"获取用户手机失败 回复是:{str}");
                        }
                        if (data.errcode == "40001")
                        {
                            await ClearToken();
                        }
                        phone = data.phone_info?.phoneNumber;
                        return data.errcode;
                    }
                });
                return phone;

            }
            catch (System.Exception ex)
            {
                Log.Error($"WxApp SendMessage Ex:{ex.StackTrace + ex.Message}");
            }
            return null;
        }
    }
}
