﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加精选产品条目
    /// </summary>
    public class AddProductRequest
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        [Required(ErrorMessage = "产品名称是必填项")]
        [StringLength(10, ErrorMessage = "产品名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "产品名称格式不正确")]
        public string ProductName { get; set; }

        /// <summary>
        /// 产品略缩图
        /// </summary>
        [Required(ErrorMessage = "产品略缩图是必填项")]
        public string Thumbnail { get; set; }

        /// <summary>
        /// 详情图
        /// </summary>
        [Required(ErrorMessage = "产品详情图是必填项")]
        [ArrayRequired(ErrorMessage = "产品详情图是必填项")]
        public List<string> DetailPicture { get; set; }
    }
}
