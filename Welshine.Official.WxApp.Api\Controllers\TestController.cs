﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 测试
    /// </summary>
    [AllowAnonymous]
    public class TestController : BaseApiController
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "Hot", "Sweltering", "Scorching"
        };

        private readonly ILogger<TestController> _logger;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        public TestController(ILogger<TestController> logger)
        {
            _logger = logger;
        }
        /// <summary>
        /// 获取
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<WeatherForecast> Get()
        {
            var rng = new Random();
            return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            {
                Date = DateTime.Now.AddDays(index),
                TemperatureC = rng.Next(-20, 55),
                Summary = Summaries[rng.Next(Summaries.Length)]
            })
            .ToArray();
        }
        /// <summary>
        /// 
        /// </summary>
        public class WeatherForecast
        {
            /// <summary>
            /// 时间
            /// </summary>
            public DateTime Date { get; set; }
            /// <summary>
            /// a
            /// </summary>

            public int TemperatureC { get; set; }
            /// <summary>
            /// a
            /// </summary>
            public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
            /// <summary>
            /// a
            /// </summary>
            public string Summary { get; set; }
        }
    }
}
