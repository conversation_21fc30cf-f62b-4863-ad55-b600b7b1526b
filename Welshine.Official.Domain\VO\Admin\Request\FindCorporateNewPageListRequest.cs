﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class FindCorporateNewPageListRequest
    {
        /// <summary>
        /// 中文企业资讯标题
        /// </summary>
        [StringLength(100,ErrorMessage = "中文企业资讯标题最多为100字")]
        public string WelshineTitle { get; set; }
        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [Range(0, 1, ErrorMessage = "内容状态参数只能为 0->未发布; 1->已发布")]
        public int? ReleaseStatus { get; set; }
        /// <summary>
        /// 创建时间段
        /// </summary>
        public TimeHorizon CreatedTimeScope { get; set; }
        /// <summary>
        /// 文章栏目: 0->企业快讯;1->新品上市;
        /// </summary>
        [Range(0, 1, ErrorMessage = "文章栏目参数只能为 0->企业快讯; 1->新品上市")]
        public int? NewColumn { get; set; }

        /// <summary>
        /// 热点推荐: 0->不推荐;1->推荐;
        /// </summary>
        [Range(0, 1, ErrorMessage = "热点推荐参数只能为 0->不推荐; 1->推荐")]
        public int? NewRecommend { get; set; }
    }
}
