﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class SidebarVersionsApproveRequest : GetVersionsIdRequest
    {
        /// <summary>
        /// 审核状态: 2->审核通过; 3->审核驳回
        /// </summary>
        [Required(ErrorMessage = "审核状态必填,请完善")]
        [Range(2, 3, ErrorMessage = "审核状态参数错误")]
        public int? ApproverStatus { get; set; }

        /// <summary>
        /// 审批意见
        /// </summary>
        [Required(ErrorMessage = "审批意见是必填项")]
        [StringLength(200, ErrorMessage = "审批意见长度不符", MinimumLength = 1)]
        public string ApprovalOpinion { get; set; }
    }
}
