﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;
using Welshine.Official.Core.Filters;
using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加门店
    /// </summary>
    public class AddStoreRequest
    {

        /// <summary>
        /// 门店名
        /// </summary>
        [Required(ErrorMessage = "门店名必填,请完善")]
        [StringLength(40, ErrorMessage = "门店名长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "门店名格式不正确!")]
        public string StoreName { get; set; }
        /// <summary>
        /// 门店地址
        /// </summary>
        [Required(ErrorMessage = "门店地址必填,请完善")]
        [StringLength(200, ErrorMessage = "地址长度限制,请完善",MinimumLength =1)]
        public string StoreAddress { get; set; }

        /// <summary>
        /// 门店开始营业时间
        /// </summary>
        [Required(ErrorMessage = "每天开业时间必填,请完善")]
        [StringLength(5, ErrorMessage = "每天开业时间长度受限", MinimumLength = 1)]
        [RegularExpression(@"^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$", ErrorMessage = "开业时间格式错误")]
        public string StoreStartTime { get; set; }

        /// <summary>
        /// 门店结束营业时间
        /// </summary>
        [Required(ErrorMessage = "每天停业时间必填,请完善")]
        [StringLength(5, ErrorMessage = "每天停业时间长度受限", MinimumLength = 1)]
        [RegularExpression(@"^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$", ErrorMessage = "停业时间格式错误")]
        public string StoreEndTime { get; set; }

        /// <summary>
        /// 门店状态
        /// </summary>
        [Required(ErrorMessage = "每天停业时间必填,请完善")]
        [EnumDataType(typeof(EnumStoreStatus),ErrorMessage = "营业状态参数错误")]
        public EnumStoreStatus? StoreStatus { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        //[Required(ErrorMessage = "手机必填,请完善")]
        //[StringLength(60, ErrorMessage = "手机长度不符", MinimumLength = 1)]
        //[RegularExpression("^([0-9`~!%@#$^*()\\-=+\\[\\][{}]\\\\|:;\"',.?/]+)$", ErrorMessage = "手机格式错误")]
        [SwaggerExclude]
        public string StorePhone{ 
            get { 
                return StorePhoneList == null ? null : string.Join("|", StorePhoneList); 
            } 
        }
        /// <summary>
        /// 手机
        /// </summary>

        [Required(ErrorMessage = "手机必填,请完善")]
        [ArrayRequired(ErrorMessage = "手机必填,请完善")]
        [StringArray("^[0-9_\\-@&=`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", 13, ErrorMessage = "手机格式错误")]
        [ArrayMaxLength(3,ErrorMessage = "手机最多三个")]
        public List<string> StorePhoneList { get; set; }
        /// <summary>
        /// 传真
        /// </summary>
        [StringLength(20, ErrorMessage = "传真长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "传真格式错误")]
        public string StoreFax { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(30, ErrorMessage = "邮箱长度不符", MinimumLength = 1)]
        [RegularExpression("^[A-Za-z0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "邮箱格式错误")]
        public string StoreEmail { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        [Required(ErrorMessage = "经度是必填项,请填写后再提交")]
        [Range(-180.0d, 180.0d, ErrorMessage = "经度错误")]
        public decimal StoreLongitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        [Required(ErrorMessage = "纬度是必填项,请填写后再提交")]
        [Range(-90.0d, 90.0d, ErrorMessage = "纬度错误")]
        public decimal StoreLatitude { get; set; }

        /// <summary>
        /// 略缩图文件Id
        /// </summary>
        [Required(ErrorMessage = "略缩图文件Id必填,请完善")]
        [StringLength(128, ErrorMessage = "略缩图文件Id长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9a-zA-Z-_]+$", ErrorMessage = "略缩图文件Id格式错误")]
        public string StoreThumbnail { get; set; }

        /// <summary>
        /// 联系图
        /// </summary>
        //[Required(ErrorMessage = "联系图文件Id必填,请完善")]
        [StringLength(128, ErrorMessage = "联系图文件Id长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9a-zA-Z-_]+$", ErrorMessage = "联系图文件Id格式错误")]
        public string StoreRelation { get; set; }
        /// <summary>
        /// 详情图
        /// </summary>
        [Required(ErrorMessage = "详情图Id必填,请完善")]
        [ArrayRequired(ErrorMessage = "详情图Id必填,请完善")]
        [StringArray("^[0-9a-zA-Z-_]+$", 128,ErrorMessage = "详情图文件Id格式错误")]
        [ArrayMaxLength(5, ErrorMessage = "详情图最多5个")]
        public List<string> StoreUrlList { get; set; }
        /// <summary>
        /// 是否首页
        /// </summary>
        public bool StoreShowHomepage { get; set; }
    }
}
