﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    public class ArrayRequiredAttribute : ValidationAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return new ValidationResult(ErrorMessage);
            }
            else
            {
                if (value is Array array && array.Length == 0)
                {
                    return new ValidationResult(ErrorMessage);
                }
                var type = value.GetType();
                if (type.IsGenericType && value is System.Collections.IEnumerable list)
                {
                    var a = list.GetEnumerator();
                    if (!a.MoveNext())
                    {
                        return new ValidationResult(ErrorMessage);
                    }

                }
            }
            return ValidationResult.Success;
        }
    }
}
