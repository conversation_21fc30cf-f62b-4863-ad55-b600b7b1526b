﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 银行卡校验
    /// </summary>
    public class BankCardAttribute : ValidationAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                var valueAsString = value.ToString();
                if (!(valueAsString.Length > 15 && valueAsString.Length < 20))
                {
                    string errorMessage = "银行卡号长度不正确";
                    return new ValidationResult(errorMessage);
                }
                //校验格式
                if (!CheckBankCard(valueAsString))
                {
                    string errorMessage = "银行卡号格式不正确";
                    return new ValidationResult(errorMessage);
                }
                
               

            }
            return ValidationResult.Success;
        }
        /// <summary>
        /// 是正整数
        /// </summary>
        /// <param name="valueAsString"></param>
        /// <returns></returns>
        private bool CheckBankCard(string valueAsString)
        {
            return Regex.IsMatch(valueAsString, @"^[0-9]*$");
        }
    }
}
