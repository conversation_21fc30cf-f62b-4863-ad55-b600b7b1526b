﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 版本类型条目
    /// </summary>
    public class GetVersionsTypeRequest
    {
        /// <summary>
        /// 版本类型: 0->首页Banner;1->首页中部广告;2->关于惠而信;3->联系方式;4->中文产品介绍;
        /// </summary>
        [Required(ErrorMessage = "版本类型必填,请完善")]
        [Range(0, 4, ErrorMessage = "版本类型参数错误")]
        public int? VersionType { get; set; }
    }
}
