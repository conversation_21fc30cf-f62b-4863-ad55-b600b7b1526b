﻿using DTHY.Core.Repository;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 分类管理仓储接口
    /// </summary>
    public interface ICategoryRepository : IBaseRepository<Category>
    {
        /// <summary>
        /// 判断分类名称是否存在
        /// </summary>
        /// <param name="nameType">类型: en->英文; cn->中文;</param>
        /// <param name="categoryName">分类名称</param>
        /// <param name="level">级别</param>
        /// <returns></returns>
        Task<bool> ExistsCategoryName(string nameType, string categoryName, int level);

        /// <summary>
        /// 判断分类名称是否存在
        /// </summary>
        /// <param name="nameType">类型: en->英文; cn->中文;</param>
        /// <param name="categoryName">分类名称</param>
        /// <param name="categoryId">分类Id</param>
        /// <param name="level">级别</param>
        /// <returns></returns>
        Task<bool> ExistsCategoryName(string nameType, string categoryName, long categoryId, int level);

        /// <summary>
        /// 获取分类
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        Task<Category> GetCategoryById(long categoryId);

        /// <summary>
        /// 获取分类排序
        /// </summary>
        /// <param name="fatherId">父级Id</param>
        /// <returns></returns>
        Task<int> GetCategorySort(int fatherId);

        /// <summary>
        /// 判断是否有下级
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        Task<bool> ExistsChild(long categoryId);

        /// <summary>
        /// 获取分类列表
        /// </summary>
        /// <returns></returns>
        Task<List<Category>> GetAllCategoryList();
    }
}
