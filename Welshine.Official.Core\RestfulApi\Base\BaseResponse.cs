﻿using System.Collections.Generic;

namespace Welshine.Official.Core.RestfulApi.Base
{
    /// <summary>
    /// 默认返回
    /// </summary>
    public class BaseResponse
    {
        /// <summary>
        /// API接口 返回结果头部
        /// </summary>
        public ResponseHead Head { get; set; } = new ResponseHead();
    }

    /// <summary>
    /// 默认返回
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BaseResponse<T> : BaseResponse
    {
        /// <summary>
        /// API接口 返回结果
        /// </summary>
        public T Result { get; set; }
    }

    /// <summary>
    /// 默认返回 Head
    /// </summary>
    public class ResponseHead
    {
        /// <summary>
        /// 返回业务信息，如果是成功调用直接填写成功，如果出现错误，由各接口根据实际情况填写
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// ApiStatus，200：成功 ，401：无权限访问，404：地址不存在， 500：为错误，详细的错误代码由各业务扩展，扩展的方式例如：500.1：表示参数错误
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 接口调用时间，格式 yyyy-MM-dd HH:mm:ss，例如 2107-10-13 23:12:15
        /// </summary>
        public string CallTime { get; set; }
    }
    /// <summary>
    /// 分页返回
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PageRows<T>
    {
        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
        /// <summary>
        /// 列表
        /// </summary>
        public List<T> Data { set; get; }
    }

    /// <summary>
    /// ResponseHead Code
    /// </summary>
    public enum ApiStatus
    {
        /// <summary>
        /// 成功
        /// </summary>
        OK = 200,

        /// <summary>
        /// 无权限访问
        /// </summary>
        Unauthorized = 401,
        /// <summary>
        /// 无角色权限访问
        /// </summary>
        RoleLimit = 403,
        /// <summary>
        /// 地址不存在
        /// </summary>
        NotFound = 40410,

        /// <summary>
        /// 操作失败
        /// </summary>
        InternalServerError = 500,

        /// <summary>
        /// 模块无权限访问
        /// </summary>
        ModularUnauthorized = 5001,
    }
}
