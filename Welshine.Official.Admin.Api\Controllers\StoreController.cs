﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Request;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 门店管理
    /// </summary>
    public class StoreController : BaseApiController
    {
        IStoreService _storeService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="storeService"></param>
        public StoreController(IStoreService storeService)
        {
            _storeService = storeService;
        }

        /// <summary>
        /// 门店列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<GetAdminStoreDetailReponse>>> GetStorePageList([FromBody] RequestPageModel<GetStorePageListRequest> request)
        {
            PageRows<GetAdminStoreDetailReponse> pageRows = new PageRows<GetAdminStoreDetailReponse>() { Data = new System.Collections.Generic.List<GetAdminStoreDetailReponse>() };
            try
            {

               var pageRowList = await _storeService.GetStorePageList(request.PageIndex, request.PageSize, request.OrderBy, request.RequestParams.StoreName);
                pageRows.Total = pageRowList.Total;
                pageRows.Data = _mapper.Map<List<GetAdminStoreDetailReponse>>(pageRowList.Data);

                return Success("查询成功",pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetStorePageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
        /// <summary>
        /// 门店详情
        /// </summary>
        /// <response code="40410">找不到门店</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetStoreDetailReponse>> GetStoreDetail([FromBody] BaseRequest<GetStoreDetailRequet> request)
        {
            GetStoreDetailReponse result = null;
            try
            {

                result = await _storeService.Get(request.Body.StoreId);
                if (result == null)
                {
                    return NoFind("找不到门店", result);
                }
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetStoreDetail " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
        /// <summary>
        /// 删除门店
        /// </summary>
        /// <response code="40410">找不到门店</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteStore([FromBody] BaseRequest<GetStoreDetailRequet> request)
        {
            bool result = false;
            try
            {

                bool existStore = await _storeService.Any(request.Body.StoreId);
                if (existStore == false)
                {
                    return NoFind("找不到门店", result);
                }
                var userInfo = GetUserInfo();
                var userId = userInfo.UserId;
                var userName = userInfo.UserName;
                result = await _storeService.Delete(request.Body.StoreId, userName, userId);
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteStore " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
        /// <summary>
        /// 添加门店
        /// </summary>
        /// <response code="1016">文件Id不存在</response>
        /// <response code="1017">门店名称已经存在</response>
        /// <response code="2201">门店首页显示只能一个</response>
        /// <response code="2202">文件格式错误</response>
        /// <response code="2203">首页门店必须有联系图片</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetStoreDetailReponse>> AddStore([FromBody] BaseRequest<AddStoreRequest> request)
        {
            GetStoreDetailReponse result = null;
            try
            {
                if (request.Body.StoreShowHomepage&&string.IsNullOrWhiteSpace(request.Body.StoreRelation))
                {
                    return Failure(ErrorCode.StoreRelateFileIdRequiredError, result);
                }
                var userInfo = GetUserInfo();
                var userId = userInfo.UserId;
                var userName = userInfo.UserName;
                //if (!string.IsNullOrWhiteSpace(request.Body.StoreStartTime) && !string.IsNullOrWhiteSpace(request.Body.StoreEndTime))
                //{
                //    var start = request.Body.StoreStartTime.Split(':'); var end = request.Body.StoreEndTime.Split(':');
                //    if (!(start.Length > 1 && end.Length > 1 && (start[1].ToInt() + start[0].ToInt() * 100 < end[1].ToInt() + end[0].ToInt() * 100)))
                //    {
                //        return Failure(-1, "停业时间必须大于开业时间", result);
                //    }
                //}
                result = await _storeService.AddStore(request.Body, userName, userId);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddStore " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
        /// <summary>
        /// 编辑门店
        /// </summary>

        /// <response code="1016">文件Id不存在</response>
        /// <response code="1017">门店名称已经存在</response>
        /// <response code="2201">门店首页显示只能一个</response>
        /// <response code="2202">文件格式错误</response>
        /// <response code="2203">首页门店必须有联系图片</response>
        /// <response code="2203">门店不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetStoreDetailReponse>> EditStore([FromBody] BaseRequest<EditStoreRequest> request)
        {
            GetStoreDetailReponse result = null;
            try
            {
                if (request.Body.StoreShowHomepage && string.IsNullOrWhiteSpace(request.Body.StoreRelation))
                {
                    return Failure(ErrorCode.StoreRelateFileIdRequiredError, result);
                }
                //if (!string.IsNullOrWhiteSpace(request.Body.StoreStartTime) && !string.IsNullOrWhiteSpace(request.Body.StoreEndTime))
                //{
                //    var start = request.Body.StoreStartTime.Split(':'); var end = request.Body.StoreEndTime.Split(':');
                //    if (!(start.Length > 1 && end.Length > 1 && (start[1].ToInt() + start[0].ToInt() * 100 < end[1].ToInt() + end[0].ToInt() * 100)))
                //    {
                //        return Failure(-1, "停业时间必须大于开业时间", result);
                //    }
                //}
                var userInfo = GetUserInfo();
                var userId = userInfo.UserId;
                var userName = userInfo.UserName;
                result = await _storeService.EditStore(request.Body, userName, userId);
                if (result == null)
                {
                    return Failure(ErrorCode.StoreNoFoundError, result);
                }
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditStore " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
    }
}
