﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Core.Config
{


    public class JwtTokenInfo
    {
        /// <summary>
        /// 用户信息
        /// </summary>
        public string user { get; set; }
        /// <summary>
        /// 签名
        /// </summary>
        public string jti { get; set; }
        /// <summary>
        /// 过期
        /// </summary>
        public int exp { get; set; }
    }

    public class JwtTokenUserInfo
    {
        [JsonProperty("id")]
        public string UserId { get; set; }
        [JsonProperty("username")]
        public string UserName { get; set; }
        [JsonProperty("status")]
        public int Status { get; set; }
        [JsonProperty("roles")]
        public JwtTokenUserRole[] Roles { get; set; }
        public string LoginName { get; set; }
    }

    public class JwtTokenUserRole
    {
        public long id { get; set; }
        public string roleName { get; set; }
        public string roleDesc { get; set; }
    }

}
