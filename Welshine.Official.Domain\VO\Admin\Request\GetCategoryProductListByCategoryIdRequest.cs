﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class GetCategoryProductListByCategoryIdRequest
    {
        /// <summary>
        /// 分类Id
        /// </summary>
        [Range(1, long.MaxValue, ErrorMessage = "分类Id参数错误")]
        public long? CategoryId { get; set; }

        /// <summary>
        /// 语言: 0->英文;1->中文;
        /// </summary>
        [Range(0, 1, ErrorMessage = "语言参数错误")]
        public int? ProductLanguage { get; set; }
    }
}
