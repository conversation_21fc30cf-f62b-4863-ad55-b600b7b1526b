﻿using System;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 展会活动表
    /// </summary>
    [SqlSugar.SugarTable("cms_exhibition_activity")]
    public class ExhibitionActivity : BaseBigEntity
    {
        /// <summary>
        /// 展会活动名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_activity_name")]
        public string ExhibitionActivityName { get; set; }

        /// <summary>
        /// 活动编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_activity_code")]
        public string ExhibitionActivityCode { get; set; }

        /// <summary>
        /// 活动开始时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_activity_start_time")]
        public DateTime ExhibitionActivityStartTime { get; set; }

        /// <summary>
        /// 活动结束时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_activity_end_time")]
        public DateTime ExhibitionActivityEndTime { get; set; }

        /// <summary>
        /// 活动地点
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_activity_address")]
        public string ExhibitionActivityAddress { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "address_longitude")]
        public decimal AddressLongitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "address_latitude")]
        public decimal AddressLatitude { get; set; }

        /// <summary>
        /// 券码前缀
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "code_prefix")]
        public string CodePrefix { get; set; }

        /// <summary>
        /// 活动入口图
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "entry_picture")]
        public string EntryPicture { get; set; }

        /// <summary>
        /// 活动参与图
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "participation_picture")]
        public string ParticipationPicture { get; set; }

        /// <summary>
        /// 活动券码图
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "code_picture")]
        public string CodePicture { get; set; }

        /// <summary>
        /// 活动状态:0->停用;1->启用;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "status")]
        public int Status { get; set; }

    }
}
