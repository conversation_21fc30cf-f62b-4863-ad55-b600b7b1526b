﻿using DocumentFormat.OpenXml.Bibliography;
using IdentityModel.Client;
using Newtonsoft.Json;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using Welshine.Official.Core;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Service
{
    public class RBACGRoupService
    {
        public PageRows<RBACGroupResponse> GetRBACGroupPageList(int pageIndex,int pageSize)
        {
            PageRows<RBACGroupResponse> result = new PageRows<RBACGroupResponse>();
            List<RBACGroupResponse> resultData = new List<RBACGroupResponse>();
            var totalNumber = 0;
            var data = DbScoped.SugarScope.Queryable<RBACGroup>().ToPageList(pageIndex, pageSize,ref totalNumber);
            foreach (var item in data)
            {
                RBACGroupResponse re = new RBACGroupResponse();
                re.Id = item.Id;
                re.UserNames= item.UserNames;
                re.GroupName= item.GroupName;
                re.GroupNo = item.GroupNo;
                re.Description = item.Description;
                if (!string.IsNullOrEmpty(item.RBACUrlJson))
                {
                    re.RBACUrlList = JsonConvert.DeserializeObject<List<string>>(item.RBACUrlJson);
                }
                if (!string.IsNullOrEmpty(item.RBACMenuJson))
                {
                    re.RBACMenuList = JsonConvert.DeserializeObject<List<string>>(item.RBACMenuJson);
                }
                resultData.Add(re);
            }
            result.Data = resultData;
            result.Total= totalNumber;
            return result;
        }

        public void AddRBACGroup(string groupName,string description,List<string> rbacMenuList,List<string> rbacUrlList,JwtTokenUserInfo userInfo)
        {
            var count = DbScoped.SugarScope.Queryable<RBACGroup>().Count();
            if(count>=99)
            {
                throw new BusinessException(ErrorCode.GroupCountLimitError);
            }
            var anyRBAC = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => x.GroupName == groupName).Any();
            if(anyRBAC)
            {
                throw new BusinessException(ErrorCode.GroupNameExist);
            }
            var max = 1;
            var groupNoList = DbScoped.SugarScope.Queryable<RBACGroup>().Select(x => x.GroupNo).ToList();
            if(groupNoList.Count>0)
            {
                max += groupNoList.Select(x => int.Parse(x)).Max();
            }
            RBACGroup group = new RBACGroup();
            group.GroupName = groupName;
            group.Description = description;
            group.GroupNo = max.ToString();
            if (rbacMenuList.Count>0)
            {
                group.RBACMenuJson = JsonConvert.SerializeObject(rbacMenuList);
            }
            else
            {
                group.RBACMenuJson = JsonConvert.SerializeObject(new List<string>());
            }
            if (rbacUrlList.Count>0)
            {
                group.RBACUrlJson = JsonConvert.SerializeObject(rbacUrlList);
            }
            else
            {
                group.RBACUrlJson = JsonConvert.SerializeObject(new List<string>());
            }
            group.CreatedTime = DateTime.Now;
            group.UpdatedTime = DateTime.Now;
            group.UpdatedBy = userInfo.UserName;
            group.CreatedBy = userInfo.UserName;
            group.ModifierId = userInfo.UserId;
            group.CreatorId = userInfo.UserId;
            AccountRole role = new AccountRole();
            role.ModifierId = userInfo.UserId;
            role.CreatorId = userInfo.UserId;
            role.CreatedBy = userInfo.UserName;
            role.UpdatedBy = userInfo.UserName;
            role.CreatedTime = DateTime.Now;
            role.UpdatedTime = DateTime.Now;
            role.RoleName = group.GroupName;
            role.Description = group.Description;
            try
            {
                DbScoped.SugarScope.BeginTran();
                var roleId = DbScoped.SugarScope.Insertable(role).ExecuteReturnBigIdentity();
                group.RoleId = roleId;
                DbScoped.SugarScope.Insertable(group).ExecuteCommand();
                DbScoped.SugarScope.CommitTran();
            }
            catch(Exception ex)
            {
                DbScoped.SugarScope.RollbackTran();
                throw ex;
            }
        }

        public RBACGroupResponse FindRBACGroupDetail(long groupId)
        {
            RBACGroupResponse result = new RBACGroupResponse();
            var group = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => x.Id == groupId).First();
            if (group == null)
            {
                throw new BusinessException(ErrorCode.GroupNotExist);
            }
            if(!string.IsNullOrEmpty(group.UserNames))
            {
                var userIds = group.UserIds.Split(',').Select(x=>long.Parse(x)).ToList();
                var accountList = DbScoped.SugarScope.Queryable<Account>().Where(x => userIds.Contains(x.Id)).ToList();
                result.AccountList = accountList;
            }
            result.RBACMenuList = JsonConvert.DeserializeObject<List<string>>(group.RBACMenuJson);
            result.RBACUrlList = JsonConvert.DeserializeObject<List<string>>(group.RBACUrlJson);
            result.GroupNo = group.GroupNo;
            result.GroupName = group.GroupName;
            result.Description = group.Description;
            result.Id = group.Id;
            result.UserNames = group.UserNames;
            return result;
        }

        public void EditGroup(long groupId,string groupName,string description,List<string> rbacMenuList,List<string> rbacUrlList)
        {
            var group = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => x.Id == groupId).First();
            if(group == null)
            {
                throw new BusinessException(ErrorCode.GroupNotExist);
            }

            if (group.GroupName != groupName)
            {
                if (!string.IsNullOrEmpty(group.UserNames))
                {
                    throw new BusinessException(ErrorCode.GroupUserNotEmpty);
                }
                var anyGroup = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => x.GroupName == groupName).Any();
                if (anyGroup)
                {
                    throw new BusinessException(ErrorCode.GroupNameExist);
                }
            }
            group.GroupName = groupName;
            group.Description = description;
            group.RBACUrlJson = JsonConvert.SerializeObject(rbacUrlList);
            group.RBACMenuJson = JsonConvert.SerializeObject(rbacMenuList); 
            DbScoped.SugarScope.Updateable(group).ExecuteCommand();
            FreeRedisHelper.DefaultInstance.Del("RBAC:" + group.RoleId);
        }

        public void AddGroupUsers(long groupId,List<long> userIds)
        {
            var group = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => x.Id == groupId).First();
            if (group == null)
            {
                throw new BusinessException(ErrorCode.GroupNotExist);
            }
            var accountList = DbScoped.SugarScope.Queryable<Account>().Where(x => userIds.Contains(x.Id)).ToList();
            if (accountList.Count!=userIds.Count)
            {
                throw new BusinessException(ErrorCode.GroupUserError);
            }
            List<long> currentUserIds = new List<long>();
            if (!string.IsNullOrEmpty(group.UserIds))
            {
                currentUserIds = group.UserIds.Split(',').Select(x=>long.Parse(x)).ToList();
            }
            foreach (long userId in userIds)
            {
                if (currentUserIds.Contains(userId))
                {
                    throw new BusinessException(ErrorCode.GroupUserAddError);
                }
            }
            var arList= new List<AccountRoleRelation>();
            foreach (var item in userIds)
            {
                AccountRoleRelation ar = new AccountRoleRelation();
                ar.Rid = group.RoleId;
                ar.Uid = item;
                arList.Add(ar);
            }
            currentUserIds.AddRange(userIds);
            var userNames = accountList.Select(x=>x.UserName).ToList();
            var userNameStr = string.Join(",", userNames);
            if (string.IsNullOrEmpty(group.UserNames))
            {
                group.UserNames = userNameStr;
            }
            else
            {
                group.UserNames = group.UserNames + "," + userNameStr;
            }
            group.UserIds = string.Join(",", currentUserIds);
            try
            {
                DbScoped.SugarScope.BeginTran();
                DbScoped.SugarScope.Insertable<AccountRoleRelation>(arList).ExecuteCommand();
                DbScoped.SugarScope.Updateable(group).ExecuteCommand();
                DbScoped.SugarScope.CommitTran();
            }
            catch(Exception ex)
            {
                DbScoped.SugarScope.RollbackTran();
                throw ex;
            }
        }

        public void RemoveGroupUsers(long groupId,List<long> userIds)
        {
            var group = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => x.Id == groupId).First();
            if (group == null)
            {
                throw new BusinessException(ErrorCode.GroupNotExist);
            }
            var accountList = DbScoped.SugarScope.Queryable<Account>().Where(x => userIds.Contains(x.Id)).ToList();
            if (accountList.Count != userIds.Count)
            {
                throw new BusinessException(ErrorCode.GroupUserError);
            }
            List<long> currentUserIds = new List<long>();
            if (!string.IsNullOrEmpty(group.UserIds))
            {
                currentUserIds = group.UserIds.Split(',').Select(x => long.Parse(x)).ToList();
            }
            foreach (long userId in userIds)
            {
                if (!currentUserIds.Contains(userId))
                {
                    throw new BusinessException(ErrorCode.GroupUserRemoveError);
                }
                else
                {
                    currentUserIds.Remove(userId);
                }
            }
            accountList = DbScoped.SugarScope.Queryable<Account>().Where(x => currentUserIds.Contains(x.Id)).ToList();
            var userNames = accountList.Select(x => x.UserName).ToList();
            group.UserNames = string.Join(",", userNames);
            group.UserIds = string.Join(",", currentUserIds);

            try
            {
                DbScoped.SugarScope.BeginTran();
                DbScoped.SugarScope.Deleteable<AccountRoleRelation>().Where(x => x.Rid == group.RoleId).Where(x => !currentUserIds.Contains(x.Uid)).ExecuteCommand();
                DbScoped.SugarScope.Updateable(group).ExecuteCommand();
                DbScoped.SugarScope.CommitTran();
            }
            catch (Exception ex)
            {
                DbScoped.SugarScope.RollbackTran();
                throw ex;
            }

        }

        public bool RBACValidate(List<JwtTokenUserRole> roles,string url)
        {
            bool result = false;
            foreach (JwtTokenUserRole role in roles)
            {
                List<string> rbacUrls = new List<string>();
                var redisUrls = FreeRedisHelper.DefaultInstance.Get<string>("RBAC:" + role.id);
                if (redisUrls != null)
                {
                    rbacUrls = JsonConvert.DeserializeObject<List<string>>(redisUrls);
                }
                if (rbacUrls == null || rbacUrls.Count == 0) 
                {
                    var group = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => x.RoleId == role.id).First();
                    if (group!=null)
                    {
                        rbacUrls = JsonConvert.DeserializeObject<List<string>>(group.RBACUrlJson);
                        FreeRedisHelper.DefaultInstance.Set("RBAC:" + role.id, group.RBACUrlJson);
                    }
                }
                if (rbacUrls.Contains(url))
                {
                    result = true; 
                    break;
                }
            }
            return result;
        }

        public List<string> GetRBACMenu(List<JwtTokenUserRole> sysRoles)
        {
            List<string> result = new List<string>();
            var roleIds = sysRoles.Select(x=>x.id).ToList();
            var groups = DbScoped.SugarScope.Queryable<RBACGroup>().Where(x => roleIds.Contains(x.RoleId)).ToList();
            foreach (var item in groups)
            {
                var menus = JsonConvert.DeserializeObject<List<string>>(item.RBACMenuJson);
                result.AddRange(menus);
            }
            return result;
        }
    }
}
