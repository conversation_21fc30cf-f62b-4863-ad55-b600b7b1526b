﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.SaveData
{
    /// <summary>
    /// 关于我们
    /// </summary>
    public class WelshineData
    {
        /// <summary>
        /// 中文
        /// </summary>
        [Required(ErrorMessage = "中文是必填项")]
        public WelshineVersionsItem CNInfo { get; set; }

        /// <summary>
        /// 英文
        /// </summary>
        [Required(ErrorMessage = "英文是必填项")]
        public WelshineVersionsItem ENInfo { get; set; }
    }

    /// <summary>
    /// 关于我们信息条目
    /// </summary>
    public class WelshineVersionsItem
    {
        /// <summary>
        /// 公司介绍
        /// </summary>
        [Required(ErrorMessage = "公司介绍是必填项")]
        [StringLength(2000, ErrorMessage = "公司介绍长度不符", MinimumLength = 1)]
        public string CompanyIntroduction { get; set; }

        /// <summary>
        /// PC端公司图片
        /// </summary>
        [Required(ErrorMessage = "PC端公司图片是必填项")]
        [ArrayRequired(ErrorMessage = "PC端公司图片是必填项")]
        [ArrayMaxLength(5, ErrorMessage = "PC端公司图片限制最多5张")]
        public List<SlideShow> PcCompanyPicture { get; set; }

        /// <summary>
        /// 移动端公司图片
        /// </summary>
        [Required(ErrorMessage = "移动端公司图片是必填项")]
        [ArrayRequired(ErrorMessage = "移动端公司图片是必填项")]
        [ArrayMaxLength(5, ErrorMessage = "移动端公司图片限制最多5张")]
        public List<SlideShow> MobileCompanyPicture { get; set; }

        /// <summary>
        /// 公司视频
        /// </summary>
        public string CompanyVideo { get; set; }
    }
}
