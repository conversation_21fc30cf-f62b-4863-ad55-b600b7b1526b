using DTHY.Core.Repository;
using SqlSugar.IOC;
using System;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 打印机设备仓储实现
    /// </summary>
    public class PrinterDeviceRepository : BaseRepository<PrinterDevice>, IPrinterDeviceRepository
    {
        /// <summary>
        /// 根据型号和SN码查询设备
        /// </summary>
        /// <param name="model">打印机型号</param>
        /// <param name="sn">设备SN码</param>
        /// <returns></returns>
        public async Task<PrinterDevice> GetByModelAndSN(string model, string sn)
        {
            return await DbScoped.SugarScope.Queryable<PrinterDevice>()
                .FirstAsync(x => x.Model == model && x.SN == sn && !x.IsDeleted);
        }

        /// <summary>
        /// 更新查询记录
        /// </summary>
        /// <param name="device">设备信息</param>
        /// <returns></returns>
        public async Task<bool> UpdateQueryRecord(PrinterDevice device)
        {
            var now = DateTime.Now;
            
            // 如果是首次查询，设置首次查询时间
            if (device.FirstQueryTime == null)
            {
                device.FirstQueryTime = now;
            }
            
            // 更新最后查询时间和查询次数
            device.LastQueryTime = now;
            device.QueryCount += 1;
            device.UpdatedTime = now;

            return await DbScoped.SugarScope.Updateable(device)
                .UpdateColumns(x => new { x.FirstQueryTime, x.LastQueryTime, x.QueryCount, x.UpdatedTime })
                .ExecuteCommandAsync() > 0;
        }
    }
}
