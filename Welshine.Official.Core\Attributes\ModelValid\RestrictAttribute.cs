﻿using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 限定只能是哪些字符
    /// </summary>
    public class RestrictAttribute : ValidationAttribute
    {
        /// <summary>
        /// 限定字符
        /// </summary>
        public string RestrictString { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public RestrictAttribute(string restrictString)
        {
            RestrictString = restrictString;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null && !string.IsNullOrWhiteSpace(RestrictString))
            {
                var valueAsString = value.ToString();
                if (!string.IsNullOrWhiteSpace(valueAsString))
                {
                    string errorMessage = "";
                    if (!RestrictString.Split(',').Any(x => x == valueAsString))
                    {
                        errorMessage = $"字符只能是{RestrictString}，请重新填写.";
                        return new ValidationResult(errorMessage);
                    }

                }
            }
            return ValidationResult.Success;
        }
    }
}
