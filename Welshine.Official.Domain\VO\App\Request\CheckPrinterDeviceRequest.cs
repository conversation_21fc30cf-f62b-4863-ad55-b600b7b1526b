using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.App.Request
{
    /// <summary>
    /// 检查打印机设备请求
    /// </summary>
    public class CheckPrinterDeviceRequest
    {
        /// <summary>
        /// 打印机型号
        /// </summary>
        [Required(ErrorMessage = "打印机型号是必填项", AllowEmptyStrings = false)]
        [StringLength(100, ErrorMessage = "打印机型号长度不能超过100个字符", MinimumLength = 1)]
        public string Model { get; set; }

        /// <summary>
        /// 设备SN码
        /// </summary>
        [Required(ErrorMessage = "设备SN码是必填项", AllowEmptyStrings = false)]
        [StringLength(100, ErrorMessage = "设备SN码长度不能超过100个字符", MinimumLength = 1)]
        public string SN { get; set; }
    }
}
