﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 图册详情条目
    /// </summary>
    public class AtlasDetailResponse
    {
        /// <summary>
        /// 图册Id
        /// </summary>
        public long AtlasId { get; set; }

        /// <summary>
        /// 图册编码
        /// </summary>
        public string AtlasCode { get; set; }

        /// <summary>
        /// 图册名称
        /// </summary>
        public string AtlasName { get; set; }

        /// <summary>
        /// 图册首页图
        /// </summary>
        public FileResponse Thumbnail { get; set; }

        /// <summary>
        /// 图册文件
        /// </summary>
        public FileResponse AtlasFile { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 下载次数
        /// </summary>
        public int UploadCount { get; set; } = 0;
    }
}
