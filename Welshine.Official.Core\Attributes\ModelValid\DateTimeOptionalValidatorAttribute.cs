﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 开始时间，结束时间 要么都不填，要么都填
    /// </summary>
    public class DateTimeOptionalValidatorAttribute : ValidationAttribute
    {
        private string _dateToCompare;
        private const string _errorMessage = "结束时间和开始时间要么都不填，要么都填";
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dateToCompare"></param>
        public DateTimeOptionalValidatorAttribute(string dateToCompare)
        {
            _dateToCompare = dateToCompare;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var dateToCompare = validationContext.ObjectType.GetProperty(_dateToCompare);
            var dateToCompareValue = dateToCompare.GetValue(validationContext.ObjectInstance, null);
            if (dateToCompareValue != null && value == null)
            {
                return new ValidationResult("结束时间为必填项，不能为空");
            }
            if (dateToCompareValue == null && value != null)
            {
                return new ValidationResult("开始时间为必填项，不能为空");
            }

            return null;
        }
    }
}
