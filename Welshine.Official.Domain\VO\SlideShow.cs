﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO
{
    /// <summary>
    /// 轮播图属性
    /// </summary>
    public class SlideShow
    {
        /// <summary>
        /// 图片地址
        /// </summary>
        [Required(ErrorMessage = "图片地址是必填项")]
        //[RegularExpression(@"^[\s\S]*\.(jpeg|jpg|png|gif)$", ErrorMessage = "图片仅支持jpg，jpeg，png，gif格式")]
        public string ImgUrl { get; set; }
    }

    /// <summary>
    /// 轮播图属性
    /// </summary>
    public class SlideShowImage
    {
        /// <summary>
        /// 图片地址
        /// </summary>
        [Required(ErrorMessage = "图片地址是必填项")]
        //[RegularExpression(@"^[\s\S]*\.(jpeg|jpg|png)$", ErrorMessage = "图片仅支持jpg，jpeg，png格式")]
        public string ImgUrl { get; set; }
    }
}
