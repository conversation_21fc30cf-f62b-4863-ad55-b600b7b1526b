﻿
using System;
using System.Collections.Generic;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain
{
    /// <summary>
    /// 创建时间
    /// </summary>
    public class CreatedTimeScope
    {
        /// <summary>
        /// Desc:审核时间From
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? From { get; set; }
        /// <summary>
        /// Desc:审核时间TO
        /// Default:
        /// Nullable:True
        /// </summary>
        [CompareDatesValidator("From", ErrorMessage = "时间区间数据错误")]
        public DateTime? To { get; set; }
    }
}
