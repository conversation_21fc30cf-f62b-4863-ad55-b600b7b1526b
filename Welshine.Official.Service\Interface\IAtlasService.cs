﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 图册服务接口
    /// </summary>
    public interface IAtlasService
    {
        /// <summary>
        /// 添加图册
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        Task<bool> AddAtlas(Atlas atlas, string thumbnail, string fileId);

        /// <summary>
        /// 修改图册
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        Task<bool> EditAtlas(Atlas atlas, string thumbnail, string fileId);

        /// <summary>
        /// 获取图册
        /// </summary>
        /// <param name="atlasId">图册Id</param>
        /// <returns></returns>
        Task<AtlasDetailResponse> GetAtlasById(long atlasId);

        /// <summary>
        /// 删除图册
        /// </summary>
        /// <param name="atlas">图册信息</param>
        /// <returns></returns>
        Task<bool> DeleteAtlas(Atlas atlas);

        /// <summary>
        /// 获取图册列表
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="times">时间筛选</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<AtlasListReponse>> GetAtlasPageList(string atlasName, TimeHorizon times, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取图册
        /// </summary>
        /// <param name="openId">用户openId</param>
        /// <returns></returns>
        Task<AtlasDetailResponse> WX_GetAtlas(string openId);
    }
}
