﻿using System;

namespace Welshine.Official.Core.Extensions
{
    /// <summary>
    /// 时间扩展
    /// </summary>
    public static class DateTimeExtensions
    {
        /// <summary>
        /// 获取时间戳(秒)
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public static long ToTimestamp(this DateTime dateTime)
        {
            var ts = dateTime - new DateTime(1970, 1, 1);
            return Convert.ToInt64(ts.TotalSeconds);
        }

        /// <summary>
        /// 生成十位的 Unix 时间戳
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static int GetUnixTimeStamp(this DateTime dt)
        {
            DateTime dateStart = new DateTime(1970, 1, 1).ToLocalTime();
            int timeStamp = Convert.ToInt32((dt - dateStart).TotalSeconds);

            return timeStamp;
        }

        /// <summary>
        /// 日期格式转 Unix 时间戳
        /// </summary>
        /// <param name="dtStr"></param>
        /// <param name="timeStamp"></param>
        /// <returns></returns>
        public static bool GetUnixTimeStampByStr(string dtStr, out int timeStamp)
        {
            DateTime dt = DateTime.Now;
            if (DateTime.TryParse(dtStr, out dt))
            {
                timeStamp = GetUnixTimeStamp(dt);
                return true;
            }
            else
            {
                timeStamp = 0;
                return false;
            }
        }

        /// <summary>
        /// 十位的 Unix 时间戳 转 DateTime
        /// </summary>
        /// <param name="timeStamp"></param>
        /// <returns></returns>
        public static DateTime GetDateTimeFormUnixTimeStamp(this int timeStamp)
        {
            DateTime startTime = new DateTime(1970, 1, 1);
            DateTime dt = startTime.AddSeconds(timeStamp);
            return dt.ToLocalTime();
        }
        /// <summary>
        ///  获取时间戳 十三位
        /// </summary>
        /// <returns></returns>
        public static string GetMilliTimeStamp(this System.DateTime time)
        {
            long ts = ConvertDateTimeToLong(time);
            return ts.ToString();
        }
        /// <summary>  
        /// 将c# DateTime时间格式转换为Unix时间戳格式  十三位
        /// </summary>  
        /// <param name="time">时间</param>  
        /// <returns>long</returns>  
        public static long ConvertDateTimeToLong(this System.DateTime time)
        {
            System.DateTime startTime = new System.DateTime(1970, 1, 1, 0, 0, 0, 0);
            long t = (time.Ticks - startTime.Ticks) / 10000;   //除10000调整为13位      
            return t;
        }
        /// <summary>
        /// 时间戳转为C#格式时间     十三位
        /// </summary>
        /// <param name="timeStamp"></param>
        /// <returns></returns>
        public static DateTime ConvertStringToDateTime(string timeStamp)
        {
            DateTime dtStart = new System.DateTime(1970, 1, 1, 0, 0, 0, 0);
            long lTime = long.Parse(timeStamp + "0000");
            TimeSpan toNow = new TimeSpan(lTime);
            return dtStart.Add(toNow);
        }

        /// <summary>
        /// 时间戳转为DateTime
        /// </summary>
        /// <param name="timeStamp"></param>
        /// <returns></returns>
        public static DateTime ToDateTime(this long timeStamp)
        {
            var s = timeStamp.ToString();
            if (s.Length == 10)
            {
                return GetDateTimeFormUnixTimeStamp((int)timeStamp);
            }

            if (s.Length == 13)
            {
                return ConvertStringToDateTime(s);
            }

            throw new System.Exception("时间戳格式不正确");
        }
    }
}
