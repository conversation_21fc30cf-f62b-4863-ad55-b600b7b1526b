﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 前端英文商品条目
    /// </summary>
    public class GetWebCategoryProductListResponse
    {
        /// <summary>
        /// 商品Id
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        /// 分类Id
        /// </summary>
        public long CategoryId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        ///// <summary>
        ///// 材质
        ///// </summary>
        //public string ProductTexture { get; set; }

        ///// <summary>
        ///// 颜色
        ///// </summary>
        //public string ProductColor { get; set; }

        ///// <summary>
        ///// 规格
        ///// </summary>
        //public string ProductSpecification { get; set; }

        /// <summary>
        /// 商品介绍
        /// </summary>
        public string ProductIntroduce { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public string ProductImg { get; set; }
    }
}
