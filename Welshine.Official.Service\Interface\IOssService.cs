﻿using Aliyun.OSS;
using System.IO;
using Welshine.Official.Domain.VO.Admin.Response;
using static AlibabaCloud.SDK.Sts20150401.Models.AssumeRoleResponseBody;

namespace Welshine.Official.Service
{
    public interface IOssService
    {
        string BaseUrl { get; }
        /// <summary>
        /// 设置成加密模式
        /// </summary>
        /// <param name="keyName"></param>
        /// <param name="stream"></param>
        /// <returns></returns>
        void SetEncrypted();
        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="keyName"></param>
        /// <param name="stream"></param>
        /// <returns></returns>
        bool AsyncAppendObject(string keyName, Stream stream);

        /// <summary>
        /// 获取文件流
        /// </summary>
        /// <param name="keyName"></param>
        /// <param name="isPrivate"></param>
        /// <returns></returns>
        Stream GetObject(string keyName);
        bool AsyncAppendObject(string bucketName, string keyName, Stream stream);
        void DeleteObject(string bucketName, string keyName);
        void DeleteObject(string keyName);
        void DeleteObjectList(string bucketName, string[] objectnames);
        void GetBucketAcl(string bucketName, string keyName);
        object SyncAppendObject(string bucketName, string keyName, Stream stream);

        object SyncAppendObject(string keyName, Stream stream);

        void SyncAppendObjectWithPartSize(string bucketName, string keyName, Stream stream, long? partSize);
        object PutObject(string bucketName, string keyName, Stream stream);
        object PutObject(string keyName, Stream stream);

        object PutObject(string keyName, Stream stream, ObjectMetadata metaData);

        AliOssCredentialsResponse GetCredentials(string name);
    }
}
