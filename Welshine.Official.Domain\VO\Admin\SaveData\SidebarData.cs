﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Domain.VO.Admin.Request;

namespace Welshine.Official.Domain.VO.Admin.SaveData
{
    public class SidebarData
    {
        /// <summary>
        /// 中文
        /// </summary>
        [Required(ErrorMessage = "中文是必填项")]
        public List<SidebarItem> CNInfo { get; set; }

        /// <summary>
        /// 英文
        /// </summary>
        [Required(ErrorMessage = "英文是必填项")]
        public List<SidebarItem> ENInfo { get; set; }
    }
}
