﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 修改产品排序
    /// </summary>
    public class EditCategoryProductSortRequest
    {
        /// <summary>
        /// 分类Id
        /// </summary>
        [Required(ErrorMessage = "分类Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "分类Id参数错误")]
        public long? CategoryId { get; set; }

        /// <summary>
        /// 语言: 0->英文; 1->中文
        /// </summary>
        [Required(ErrorMessage = "语言必填,请完善")]
        [Range(0, 1, ErrorMessage = "语言参数错误")]
        public int? ProductLanguage { get; set; }

        /// <summary>
        /// 商品序号列表
        /// </summary>
        [Required(ErrorMessage = "商品序号列表是必填项")]
        [ArrayRequired(ErrorMessage = "商品序号列表是必填项")]
        public List<CategoryProductSort> CategoryProductSortList { get; set; }

    }

    /// <summary>
    /// 商品序号
    /// </summary>
    public class CategoryProductSort
    {
        /// <summary>
        /// 分类产品Id
        /// </summary>
        [Required(ErrorMessage = "分类产品Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "分类产品Id参数错误")]
        public long? CategoryProductId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required(ErrorMessage = "序号必填,请完善")]
        [Range(1, int.MaxValue, ErrorMessage = "序号参数错误")]
        public int? Sort { get; set; }
    }
}
