﻿using System;
using System.Collections.Specialized;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Welshine.Official.Core
{
   public static class HttpClientExtend
    {
        /// <summary>
        /// 分析 url 字符串中的参数信息
        /// </summary>
        /// <param name="url">输入的 URL</param>
        /// <param name="baseUrl">输出 URL 的基础部分</param>
        /// <param name="nvc">输出分析后得到的 (参数名,参数值) 的集合</param>
        public static void ParseUrl(string url, out string baseUrl, out NameValueCollection nvc)
        {
            if (url == null)
                throw new ArgumentNullException("url");
            nvc = new NameValueCollection();
            baseUrl = "";
            if (url == "")
                return;
            int questionMarkIndex = url.IndexOf('?');
            if (questionMarkIndex == -1)
            {
                baseUrl = url;
                return;
            }
            baseUrl = url.Substring(0, questionMarkIndex);
            if (questionMarkIndex == url.Length - 1)
                return;
            string ps = url.Substring(questionMarkIndex + 1);
            // 开始分析参数对  
            Regex re = new Regex(@"(^|&)?(\w+)=([^&]+)(&|$)?", RegexOptions.Compiled);
            MatchCollection mc = re.Matches(ps);
            foreach (Match m in mc)
            {
                nvc.Add(m.Result("$2").ToLower(), m.Result("$3"));
            }
        }
        /// <summary>
        /// 实体参数
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        static string getQueryString(object req)
        {
            StringBuilder query = new StringBuilder(""); PropertyInfo[] propertys = req.GetType().GetProperties();
            foreach (PropertyInfo pi in propertys)
            {
                if (pi.CanRead)
                {
                    query.Append($@"{pi.Name}={pi.GetValue(req)}&");
                }
            }
            return query.ToString();
        }
        /// <summary>
        /// 微信支付Post
        /// </summary>
        /// <param name="xml">xml</param>
        /// <param name="url">url</param>
        /// <param name="isUseCert">撤销订单和退款需要证书</param>
        /// <param name="timeout">超时时间</param>
        /// <returns></returns>
        public static Task<string> WxXmlPost(this HttpClient _httpClient, string xml, string url, bool isUseCert, int timeout)
        {
            string contentType = "text/xml";
            _httpClient.Timeout = new TimeSpan(0, 0, timeout);
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(contentType));
           // 是否使用证书
            //    if (isUseCert)
            //{
            //    string path = HttpContext.Current.Request.PhysicalApplicationPath;
            //    X509Certificate2 cert = new X509Certificate2(path + WxPayConfig.GetConfig().GetSSlCertPath(), WxPayConfig.GetConfig().GetSSlCertPassword());
            //    _httpClient.ClientCertificates.Add(cert);

            //}
            var httpContent = new StringContent(xml, Encoding.UTF8, contentType);

            var response = _httpClient.PostAsync(url, httpContent).Result;
            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsStringAsync();
            }
            return null;
        }

        /// <summary>
        /// 获取字符串
        /// </summary>
        /// <param name="_httpClient"></param>
        /// <param name="url"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        public static Task<string> GetStrAsync(this HttpClient _httpClient,string url, object param = null,string contentType ="")
        {
            string baseUrl = ""; NameValueCollection nvc;
            ParseUrl(url, out baseUrl, out nvc);
            if (param != null)
            {
                if (nvc.Count > 0)
                {
                    url += $"&{getQueryString(param)}";
                }
                else
                {
                    url += $"?{getQueryString(param)}";
                }
            }
            if (!string.IsNullOrWhiteSpace(contentType))
            {
                _httpClient.DefaultRequestHeaders.Accept.Clear();
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(contentType));
            }
            var response = _httpClient.GetAsync(url).Result;
            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsStringAsync();
            }
            return null;

        }
        /// <summary>
        /// 获取流
        /// </summary>
        /// <param name="_httpClient"></param>
        /// <param name="url"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        public static Task<Stream> GetStreamAsync(this HttpClient _httpClient, string url, object param = null)
        {
            string baseUrl = ""; NameValueCollection nvc;
            ParseUrl(url, out baseUrl, out nvc);
            if (param != null)
            {
                if (nvc.Count > 0)
                {
                    url += $"&{getQueryString(param)}";
                }
                else
                {
                    url += $"?{getQueryString(param)}";
                }
            }
            var response = _httpClient.GetAsync(url).Result;
            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsStreamAsync();
            }
            return null;
        }
        /// <summary>
        /// 异步post
        /// </summary>
        /// <param name="url"></param>
        /// <param name="obj"></param>
        /// <param name="contentType"></param>
        /// <param name="charset"></param>
        /// <returns></returns>
        public static Task<string> PostObjAsync(this HttpClient _httpClient, string url, object obj, string contentType = "application/x-www-form-urlencoded", string charset = "UTF-8")
        {
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(contentType));
            string content = JsonConvert.SerializeObject(obj);
            var httpContent = new StringContent(content, Encoding.GetEncoding(charset), contentType);
            var response = _httpClient.PostAsync(url, httpContent).Result;

            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsStringAsync();
            }
            return null;
        }


        /// <summary>
        /// 异步post
        /// </summary>
        /// <param name="client"></param>
        /// <param name="url"></param>
        /// <param name="obj"></param>
        /// <param name="contentType"></param>
        /// <param name="charset"></param>
        /// <returns></returns>
        public static async Task<HttpResponseMessage> PostAsync(this HttpClient client, string url, object obj,string contentType = "application/json", string charset = "UTF-8")
        {
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(contentType));
            if (obj != null)
            {
                string content = JsonConvert.SerializeObject(obj);
                var httpContent = new StringContent(content, Encoding.GetEncoding(charset), contentType);

                return await client.PostAsync(url, httpContent);
            }
            var httpContent2 = new StringContent("", Encoding.GetEncoding(charset), contentType);
            return await client.PostAsync(url, httpContent2);
        }

        /// <summary>
        /// 异步post
        /// </summary>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="client"></param>
        /// <param name="url"></param>
        /// <param name="obj"></param>
        /// <param name="contentType"></param>
        /// <param name="charset"></param>
        /// <returns></returns>
        public static async Task<TResult> PostAsync<TResult>(this HttpClient client, string url, object obj, string contentType = "application/json", string charset = "UTF-8")
        {
            var response =await client.PostAsync(url, obj, contentType, charset);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<TResult>(content);
            }
            return default;
        }

        /// <summary>
        /// 异步Put
        /// </summary>
        /// <param name="client"></param>
        /// <param name="url"></param>
        /// <param name="obj"></param>
        /// <param name="contentType"></param>
        /// <param name="charset"></param>
        /// <returns></returns>
        public static async Task<HttpResponseMessage> PutAsync(this HttpClient client, string url, object obj, string contentType = "application/x-www-form-urlencoded", string charset = "UTF-8")
        {
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(contentType));

            string content = JsonConvert.SerializeObject(obj);
            var httpContent = new StringContent(content, Encoding.GetEncoding(charset), contentType);

            return await client.PutAsync(url, httpContent);
        }
    }
}
