﻿

using AutoMapper;
using CommonServiceLocator;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using Welshine.Official.Admin.Api;
using Welshine.Official.Core.Attributes;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.JwtToken;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.RestfulApi.Helper;

namespace Welshine.Official.Admin.Api.Core
{/// <summary>
 /// BaseApiController
 /// </summary>
    [Route("api/[controller]/[action]")]
    [Route(Program.AppName + "/api/[controller]/[action]")]
    [Produces("application/json")]
    [WebApiException]
    [Authorzation]
    [ModelValidate]
   
    public class BaseApiController : Controller
    {
        /// <summary>
        /// 映射
        /// </summary>
       public IMapper _mapper { get; set; }
        #region 返回方法封装
        /// <summary>
        /// 成功返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Success<T>(string msg, T data = default(T))
        {
            return WebApiResponseHelp.Success(msg, data);
        }

        /// <summary>
        /// 失败返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="errorCode">错误编码</param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Failure<T>(ErrorCode errorCode, T data = default(T))
        {
            return WebApiResponseHelp.Result<T>(errorCode.GetHashCode(), errorCode.ToDescriptionName(), data);
        }
        /// <summary>
        /// 失败返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Failure<T>(string msg, T data = default(T))
        {
            return WebApiResponseHelp.Failure(msg, data);
        }
        /// <summary>
        /// 失败返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="code">错误码</param>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Failure<T>(int code, string msg, T data = default(T))
        {
            return WebApiResponseHelp.Result<T>(code, msg, data);
        }
        /// <summary>
        /// 找不到返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> NoFind<T>(string msg, T data = default(T))
        {
            return WebApiResponseHelp.NoFind(msg, data);
        }
        /// <summary>
        /// 返回结果
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="code"></param>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Result<T>(ApiStatus code, string msg, T data = default(T))
        {
            return WebApiResponseHelp.Result(code, msg, data);
        }
        /// <summary>
        /// 成功放回结果
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse Success(string msg, dynamic data = null)
        {
            return WebApiResponseHelp.Success(msg, data);
        }
        /// <summary>
        /// 失败返回结果
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse Failure(string msg, dynamic data = null)
        {
            return WebApiResponseHelp.Failure(msg, data);
        }
        /// <summary>
        /// 找不到返回结果
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse NoFind(string msg, dynamic data = null)
        {
            return WebApiResponseHelp.NoFind(msg, data);
        }
        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="code"></param>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse Result(ApiStatus code, string msg, dynamic data = null)
        {
            return WebApiResponseHelp.Result(code, msg, data);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="isSuccess"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        protected BaseResponse Result(bool isSuccess, string msg)
        {
            return isSuccess ? Success(msg) : Failure(msg);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        protected BaseResponse NotAuthority()
        {
            return Result(ApiStatus.ModularUnauthorized, "权限不足");
        }
        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        protected BaseResponse<T> NotAuthority<T>()
        {
            return Result<T>(ApiStatus.ModularUnauthorized, "权限不足");
        }

        #endregion 返回方法封装
        /// <summary>
        /// 获取当前用户Id
        /// </summary>
        /// <returns></returns>
        protected JwtTokenUserInfo GetUserInfo()
        {
            var httpContextAccessor = ServiceLocator.Current.GetInstance<IHttpContextAccessor>();
            HttpRequest request = httpContextAccessor.HttpContext.Request;
            if (request.Headers.ContainsKey("Authorization") && request.Headers.TryGetValue("Authorization", out var value))
            {
                string token = value.ToString().Split(' ')[1];
                var payLoad = JwtUtils.Decode(token);
                var result = new JwtTokenUserInfo();
                var roles = JsonConvert.DeserializeObject<List<JwtTokenUserRole>>(payLoad["Roles"].ToString());
                var status = (bool)payLoad["Status"];
                var userId = (long)payLoad["UserId"];
                var userName = (string)payLoad["UserName"];
                var loginName = (string)payLoad["LoginName"];
                result.UserName = userName;
                result.LoginName = loginName;
                result.UserId = userId.ToString();
                result.Roles = roles.ToArray();
                if (status)
                {
                    result.Status = 1;
                }
                else
                {
                    result.Status = 0;
                }
                return result;
                /*var tokenHandler = new JwtSecurityTokenHandler();
                var userTokenInfo = tokenHandler.ReadToken(token) as JwtSecurityToken;
                var userInfo = userTokenInfo.Claims.FirstOrDefault(x => x.Type == "user")?.Value;
                if (!string.IsNullOrEmpty(userInfo))
                {
                    return JsonConvert.DeserializeObject<JwtTokenUserInfo>(userInfo);
                }*/
            }
            return new JwtTokenUserInfo() { UserId="0",UserName="未登录用户"};
        }
    }
}
