﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 展会列表查询入参
    /// </summary>
    public class GetExhibitionListRequest
    {
        /// <summary>
        /// 展会名称
        /// </summary>
        [StringLength(60, ErrorMessage = "展会名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "展会名称格式不正确")]
        public string ExhibitionName { get; set; }
    }
}
