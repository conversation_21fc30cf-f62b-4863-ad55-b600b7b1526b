using System.Collections.Generic;

namespace Welshine.Official.Domain.VO.App.Response
{
    /// <summary>
    /// 标签模版分类响应
    /// </summary>
    public class LabelTemplateCategoryResponse
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public long CategoryId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string CategoryName { get; set; }
    }

    /// <summary>
    /// 系统标签模版响应
    /// </summary>
    public class SystemLabelTemplateResponse
    {
        /// <summary>
        /// 模版ID
        /// </summary>
        public long TemplateId { get; set; }

        /// <summary>
        /// 模版名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 模版内容（JSON格式）
        /// </summary>
        public string TemplateContent { get; set; }

        /// <summary>
        /// 模版缩略图（Base64）
        /// </summary>
        public string TemplateThumbnail { get; set; }

        /// <summary>
        /// 模版描述
        /// </summary>
        public string TemplateDescription { get; set; }

        /// <summary>
        /// 标签尺寸
        /// </summary>
        public string LabelSize { get; set; }

        /// <summary>
        /// 适用场景标签
        /// </summary>
        public string SceneTags { get; set; }
    }
}
