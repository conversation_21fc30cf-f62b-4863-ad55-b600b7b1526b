using System.Collections.Generic;

namespace Welshine.Official.Domain.VO.App.Response
{
    /// <summary>
    /// 标签模版分类响应
    /// </summary>
    public class LabelTemplateCategoryResponse
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public long CategoryId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string CategoryName { get; set; }
    }

    /// <summary>
    /// 标签模版响应
    /// </summary>
    public class LabelTemplateResponse
    {
        /// <summary>
        /// 模版ID
        /// </summary>
        public long TemplateId { get; set; }

        /// <summary>
        /// 模版名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 模版内容（JSON格式）
        /// </summary>
        public string TemplateContent { get; set; }
    }
}
