﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 展会活动列表条目
    /// </summary>
    public class GetExhibitionActivityPageListRequest
    {
        /// <summary>
        /// 活动名称
        /// </summary>
        [StringLength(20, ErrorMessage = "活动名称长度不符", MinimumLength = 1)]
        public string ExhibitionActivityName { get; set; }

        /// <summary>
        /// 活动时间
        /// </summary>
        public TimeHorizon ActivityTimeScope { get; set; }

        /// <summary>
        /// 活动状态:0->停用;1->启用;
        /// </summary>
        [Range(0, 1, ErrorMessage = "活动状态错误")]
        public int? Status { get; set; }
    }
}
