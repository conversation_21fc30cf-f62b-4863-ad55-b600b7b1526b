﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class GetProductCNVersionsResponse
    {
        /// <summary>
        /// 版本标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 版本内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 详情图
        /// </summary>
        public List<SlideShow> Img { get; set; }
    }
}
