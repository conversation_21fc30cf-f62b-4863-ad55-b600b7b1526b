﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class GetCorporateNewListRequest
    {
        /// <summary>
        /// 文章栏目: 0->企业快讯;1->新品上市;
        /// </summary>
        [Required(ErrorMessage = "文章栏目为必填")]
        [Range(0, 1, ErrorMessage = "文章栏目参数只能为 0->企业快讯; 1->新品上市")]
        public int? NewColumn { get; set; }

        /// <summary>
        /// 热点推荐: 0->不推荐;1->推荐;
        /// </summary>
        [Range(0, 1, ErrorMessage = "热点推荐参数只能为 0->不推荐;1->推荐;")]
        public int? NewRecommend { get; set; }
    }
}
