﻿using Newtonsoft.Json;
using System;

namespace Welshine.Official.Core
{
    /// <summary>
    /// 枚举转换器
    /// </summary>
    public class JsonConverterEnum : JsonConverter
    {
        /// 
        /// 是否可以转换
        /// 
        /// 
        /// 
        public override bool CanConvert(Type objectType)
        {
            return true;
        }

        /// 
        /// 读json
        /// 
        /// 
        /// 
        /// 
        /// 
        /// 
        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            var type = Nullable.GetUnderlyingType(objectType);
            if ((reader.ValueType == null || type != null) && reader.Value == null)
            {
                return null;
            }
            else
            {
                if (type != null)
                {
                    return Enum.ToObject(type, Convert.ToInt32(reader.Value != null ? reader.Value.ToString() : ""));
                }
                return Enum.ToObject(objectType, Convert.ToInt32(reader.Value != null ? reader.Value.ToString() : ""));
            }
        }

        /// 
        /// 写json
        /// 
        /// 
        /// 
        /// 
        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            writer.WriteValue(value);
        }
    }
}
