﻿using CsvHelper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 展会活动参与人员
    /// </summary>
    public class ExhibitionActivityParticipantController : BaseApiController
    {
        IExhibitionActivityParticipantService _exhibitionActivityParticipantService;

        public ExhibitionActivityParticipantController(IExhibitionActivityParticipantService exhibitionActivityParticipantService)
        {
            _exhibitionActivityParticipantService = exhibitionActivityParticipantService;
        }

        /// <summary>
        /// 展会活动参与人员列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<ExhibitionActivityParticipantResponse>>> GetExhibitionActivityParticipantPageList([FromBody] RequestPageModel<GetExhibitionActivityParticipantPageListRequest> request)
        {
            PageRows<ExhibitionActivityParticipantResponse> pageRows = new PageRows<ExhibitionActivityParticipantResponse>() { Data = new System.Collections.Generic.List<ExhibitionActivityParticipantResponse>() };
            try
            {
                DateTime? dt1 = request.RequestParams.CreatedTimeScope == null ? null : request.RequestParams.CreatedTimeScope.From;
                DateTime? dt2 = request.RequestParams.CreatedTimeScope == null ? null : request.RequestParams.CreatedTimeScope.To;
                var pageRowList = await _exhibitionActivityParticipantService.GetExhibitionActivityParticipantPageList(request.PageIndex, request.PageSize, request.OrderBy, request.RequestParams.ExhibitionActivityId, request.RequestParams.Name, request.RequestParams.Mobile, request.RequestParams.Code, dt1, dt2, request.RequestParams.Type, request.RequestParams.IsScene);
                pageRows.Total = pageRowList.Total;
                pageRows.Data = _mapper.Map<List<ExhibitionActivityParticipantResponse>>(pageRowList.Data);

                return Success("查询成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionActivityParticipantPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        [HttpPost]
        public async Task<IActionResult> ExhibitionActivityParticipantExport([FromBody] BaseRequest<GetExhibitionActivityParticipantPageListRequest> request)
        {
            List<ExhibitionActivityParticipantResponse> list = new List<ExhibitionActivityParticipantResponse>();
            var baseDirectory = AppContext.BaseDirectory;
            var basePath = "";
            try
            {

                basePath = baseDirectory + $"/{System.Guid.NewGuid().ToString("N")}".Replace('/', Path.DirectorySeparatorChar);//用于生产数据
                var basePathCopy = baseDirectory + $"/活动参与人员_{DateTime.Now.ToString("yyyyMMdd")}".Replace('/', Path.DirectorySeparatorChar);//用于压缩

                DateTime? dt1 = request.Body.CreatedTimeScope == null ? null : request.Body.CreatedTimeScope.From;
                DateTime? dt2 = request.Body.CreatedTimeScope == null ? null : request.Body.CreatedTimeScope.To;
                var count = await _exhibitionActivityParticipantService.GetExhibitionActivityParticipantCount(request.Body.ExhibitionActivityId, request.Body.Name, request.Body.Mobile, request.Body.Code, dt1, dt2, request.Body.Type, request.Body.IsScene);
                if (count == 0)
                {
                    return Json(Failure(ErrorCode.NoDataWarn, "没有数据，请调整查询条件后重新导出"));
                }

                //查询和导出
                if (!Directory.Exists(basePath))
                {
                    Directory.CreateDirectory(basePath);
                }

                List<Task<string>> tasks = new List<Task<string>>();
                int pageIndex = 0; //页码 0也就是第一条 
                int pageSize = 5000; //每页条数    

                while (pageIndex * pageSize < count)
                {
                    var task = GetExportPath(basePath, pageIndex + 1, request);
                    var path = await task;
                    if (string.IsNullOrWhiteSpace(path)) break;

                    pageIndex++;
                }

                string zipName = $"活动参与人员_{DateTime.Now.ToString("yyyyMMdd")}.zip";
                string zipPath = baseDirectory + $"/{zipName}".Replace('/', Path.DirectorySeparatorChar);
                if (Directory.Exists(basePathCopy))
                {
                    Directory.Delete(basePathCopy, true);
                }
                Directory.Move(basePath, basePathCopy);
                ZipUtility zip = new ZipUtility();
                zip.ZipFileFromDirectory(basePathCopy, zipPath, 5);
                Directory.Delete(basePathCopy, true);
                if (Directory.Exists(basePath))
                {
                    Directory.Delete(basePath, true);
                }
                //文件流
                var filepath = zipPath;
                var provider = new FileExtensionContentTypeProvider();
                var fileInfo = new FileInfo(filepath);
                var ext = fileInfo.Extension;
                new FileExtensionContentTypeProvider().Mappings.TryGetValue(ext, out var contentment);
                return File(await System.IO.File.ReadAllBytesAsync(filepath), contentment ?? "application/octet-stream", fileInfo.Name);
            }
            catch (BusinessException ex)
            {
                return Json(Failure(ex.Code, ex.Message, list));
            }
            catch (System.Exception ex)
            {

                Log.Error("ExhibitionActivityParticipantExport ex:" + ex.Message + ex.StackTrace);
                return Json(Failure(ErrorCode.SystemError, list));
            }
            finally
            {
                if (!string.IsNullOrWhiteSpace(basePath) && Directory.Exists(basePath))
                {
                    Directory.Delete(basePath, true);
                }
            }
        }

        /// <summary>
        /// 获取导出csv地址
        /// </summary>
        /// <param name="basePath"></param>
        /// <param name="pageIndex"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<string> GetExportPath(string basePath, int pageIndex, BaseRequest<GetExhibitionActivityParticipantPageListRequest> request)
        {
            DateTime? dt1 = request.Body.CreatedTimeScope == null ? null : request.Body.CreatedTimeScope.From;
            DateTime? dt2 = request.Body.CreatedTimeScope == null ? null : request.Body.CreatedTimeScope.To;
            var pageRows = (await _exhibitionActivityParticipantService.GetExhibitionActivityParticipantPageList(pageIndex, pageSize: 5000, "id desc", request.Body.ExhibitionActivityId, request.Body.Name,request.Body.Mobile, request.Body.Code, dt1, dt2, request.Body.Type, request.Body.IsScene)).Data;
            if (!pageRows.Any()) return null;

            foreach (var item in pageRows)
            {
                item.CreatedTime = item.CreatedTime.AddHours(8);
            }

            var csvFilePath = basePath + $"/活动参与人员_{DateTime.Now.ToString("yyyyMMdd")}_{pageIndex}.csv".Replace('/', Path.DirectorySeparatorChar);

            await using (var writer = new StreamWriter(csvFilePath, false, Encoding.UTF8))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    csv.WriteHeader<ExhibitionActivityParticipantResponse>();
                    await csv.NextRecordAsync();
                    await csv.WriteRecordsAsync(pageRows);
                }
            }

            return csvFilePath;
        }

    }
}
