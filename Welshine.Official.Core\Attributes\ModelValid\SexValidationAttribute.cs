﻿using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 性别校验
    /// </summary>
    public class SexValidationAttribute : ValidationAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                var valueAsString = value.ToString().ToLower();
                if (!string.IsNullOrWhiteSpace(valueAsString))
                {
                    if (valueAsString != "男" && valueAsString != "女")
                    {
                        string errorMessage = "性别格式不对.";
                        return new ValidationResult(ErrorMessage ?? errorMessage);
                    }
                }
            }
            return ValidationResult.Success;
        }
    }
}
