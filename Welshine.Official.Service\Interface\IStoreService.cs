﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Request;

namespace Welshine.Official.Service.Interface
{
    public interface IStoreService
    {
        Task<PageRows<GetStorePageListReponse>> GetStorePageList(int pageIndex, int pageSize, string orderBy, string storeName);
        Task<GetStoreDetailReponse> Get(long storeId);
        Task<bool> Any(long storeId);
        Task<bool> Delete(long storeId, string userName, string userId);
        Task<GetStoreDetailReponse> AddStore(AddStoreRequest body, string userName, string userId);
        Task<GetStoreDetailReponse> EditStore(EditStoreRequest body, string userName, string userId);
        Task<GetStoreDetailReponse> GetDefaultStoreDetail();
        Task<PageRows<GetStorePageListReponse>> GetWXStorePageList(int pageIndex, int pageSize, string orderBy, string storeName);
    }
}
