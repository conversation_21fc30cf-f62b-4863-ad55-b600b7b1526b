﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加展会条目
    /// </summary>
    public class AddExhibitionRequest
    {
        /// <summary>
        /// 展会名称
        /// </summary>
        [Required(ErrorMessage = "展会名称是必填项")]
        [StringLength(60, ErrorMessage = "展会名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "展会名称格式不正确")]
        public string ExhibitionName { get; set; }

        /// <summary>
        /// 展会时间
        /// </summary>
        [Required(ErrorMessage = "展会时间是必填项")]
        [StringLength(60, ErrorMessage = "展会时间长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "展会时间格式不正确")]
        public string ExhibitionTime { get; set; }

        /// <summary>
        /// 展会地点
        /// </summary>
        [Required(ErrorMessage = "展会地点是必填项")]
        [StringLength(100, ErrorMessage = "展会地点长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "展会地点格式不正确")]
        public string ExhibitionAddress { get; set; }

        /// <summary>
        /// 展会略缩图
        /// </summary>
        [Required(ErrorMessage = "展会略缩图是必填项")]
        public string Thumbnail { get; set; }

        /// <summary>
        /// 详情图
        /// </summary>
        [Required(ErrorMessage = "展会详情图是必填项")]
        [ArrayRequired(ErrorMessage = "展会详情图是必填项")]
        public List<string> DetailPicture { get; set; }
    }
}
