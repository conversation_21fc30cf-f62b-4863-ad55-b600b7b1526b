﻿using DocumentFormat.OpenXml.Bibliography;
using DTHY.Core.Repository;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;

namespace Welshine.Official.Repository.Interface
{
    public interface ICorporateNewRepository : IBaseRepository<CorporateNew>
    {
        Task<CorporateNew> InsertAsync(CorporateNew corporateNew);
    }
}
