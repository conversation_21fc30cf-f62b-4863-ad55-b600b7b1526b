﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class SavePostListRequest
    {
        /// <summary>
        /// 部门id
        /// </summary>
        [Required(ErrorMessage = "部门id为必填")]
        public long? DepartmentId { get; set; }
        /// <summary>
        /// 岗位名称列表
        /// </summary>
        [Required(ErrorMessage = "岗位名称列表为必填")]
        [ArrayRequired(ErrorMessage = "岗位名称列表为必填")]
        [StringArray("^[\u4e00-\u9fa5]{2,10}$",10, ErrorMessage = "岗位名称应为2-10个中文字")]
        public List<string> PostNameList { get; set; }
    }
}
