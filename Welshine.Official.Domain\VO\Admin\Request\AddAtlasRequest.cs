﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加图册条目
    /// </summary>
    public class AddAtlasRequest
    {
        /// <summary>
        /// 图册名称
        /// </summary>
        [Required(ErrorMessage = "图册名称是必填项")]
        [StringLength(60, ErrorMessage = "图册名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "图册名称格式不正确")]
        public string AtlasName { get; set; }

        /// <summary>
        /// 图册封面图
        /// </summary>
        [Required(ErrorMessage = "图册封面图是必填项")]
        public string Thumbnail { get; set; }

        /// <summary>
        /// 图册文件
        /// </summary>
        [Required(ErrorMessage = "图册文件是必填项")]
        public string FileId { get; set; }

    }
}
