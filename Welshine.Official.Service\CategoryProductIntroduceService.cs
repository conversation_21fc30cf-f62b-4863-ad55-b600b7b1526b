﻿using System;
using System.Collections.Generic;
using System.Text;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class CategoryProductIntroduceService : ICategoryProductIntroduceService
    {
        private readonly IFileRepository _fileRepository;
        private readonly ICategoryProductRepository _categoryProductRepository;


    }
}
