﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Service.Interface
{
    public interface ICorporateNewService
    {


        CorporateNewResponse AddNew(string cnTitle, string cnContent, string cnImage, string enTitle, string enContent, string enImage, DateTime? cnPublishDate, DateTime? enPublishDate, JwtTokenUserInfo userInfo, int newColumn, int newRecommend, string enDescription, string cnDescription);

        PageRows<CorporateNewResponse> FindCorporateNewPageList(string? title,int? releaseStatus,DateTime? startDate,DateTime? endDate, int? newColumn, int? newRecommend, int pageIndex,int pageSize,string orderField,OrderByType order);

        void EditNew(long newId, string cnTitle, string cnContent, string cnImage, string enTitle, string enContent, string enImage, DateTime? cnPublishDate, DateTime? enPublishDate, JwtTokenUserInfo userInfo, int newColumn, int newRecommend, string enDescription, string cnDescription);

        CorporateNewDetailResponse FindNewDetail(long id);

        void DeleteNew(long id, JwtTokenUserInfo userInfo);

        void SwitchNewStatus(long id,int releaseStatus, JwtTokenUserInfo userInfo);
        List<CorporateNewDetailResponse> GetNewDetailPageList();

        int GetRecommendCountByNewColumn(int NewColumn);
        PageRows<CorporateNewDetailResponse> GetCorporateNewList(int newColumn, int? newRecommend, int pageIndex, int pageSize, string orderField, OrderByType order);
    }
}
