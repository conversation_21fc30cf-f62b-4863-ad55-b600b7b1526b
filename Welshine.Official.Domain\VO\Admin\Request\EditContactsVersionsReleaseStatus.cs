﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 联系方式发布/下架
    /// </summary>
    public class EditContactsVersionsReleaseStatus : GetVersionsIdRequest
    {
        /// <summary>
        /// 发布状态: 0->未发布; 1->已发布;
        /// </summary>
        [Required(ErrorMessage = "发布状态必填,请完善")]
        [Range(0, 1, ErrorMessage = "发布状态参数错误")]
        public int? ReleaseStatus { get; set; }
    }
}
