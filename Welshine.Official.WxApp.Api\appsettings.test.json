{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "sqlsuger": {"DbType": 0, "InitKeyType": 1, "IsAutoCloseConnection": true, "ConnectionString": "Data Source=************;port=43306; Initial Catalog=official_db;uid=root; pwd=Root@123456.;Convert Zero Datetime=True;Allow Zero Datetime=True"}, "ConnectionStrings": {"RedisStr": "************,password=Redis@123456.,defaultDatabase=10,poolsize=10,ssl=false,writeBuffer=10240,prefix="}, "AppConfig": {"AppId": "wx0fe9df0a15f8223e", "AppSecret": "2d6933e4fb4b586619d9f852761a7c80"}, "Serilog": {"WriteTo": [{"Name": "RollingFile", "Args": {"pathFormat": "logs\\{Date}.txt", "RestrictedToMinimumLevel": "Warning"}}, {"Name": "<PERSON><PERSON><PERSON>"}], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Information"}}}, "AliOss": {"endpoint": "oss-cn-guangzhou.aliyuncs.com", "bucketName": "welshine-official", "accessKeyId": "LTAI5t6xCiGVY4DP1DWthyHr", "accessKeySecret": "******************************", "allowType": ".png,.jpg,.txt,.pdf,.jpeg,.image", "mLength": 5}}