﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 图册查询条目
    /// </summary>
    public class GetAtlasListRequest
    {
        /// <summary>
        /// 图册名称
        /// </summary>
        [StringLength(60, ErrorMessage = "图册名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "图册名称格式不正确")]
        public string AtlasName { get; set; }

        /// <summary>
        /// 创建时间筛选
        /// </summary>
        public TimeHorizon CreatedTimeScope { get; set; }
    }

    /// <summary>
    /// 时间范围
    /// </summary>
    public class TimeHorizon
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? From { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [CompareDatesValidator("From", ErrorMessage = "结束时间不能小于开始有效时间")]
        public DateTime? To { get; set; }
    }
}
