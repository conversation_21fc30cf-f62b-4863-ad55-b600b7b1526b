﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Domain.VO.Request;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 门店
    /// </summary>
    public class StoreController : BaseApiController
    {
        IStoreService _storeService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="storeService"></param>
        public StoreController(IStoreService storeService)
        {
            _storeService = storeService;
        }

        /// <summary>
        /// 门店列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<GetAppStorePageListReponse>>> GetStorePageList([FromBody] RequestPageModel<GetStorePageListRequest> request)
        {
            PageRows<GetAppStorePageListReponse> pageRows = new PageRows<GetAppStorePageListReponse>() { Data = new System.Collections.Generic.List<GetAppStorePageListReponse>() };
            try
            {

                var pageRowList = await _storeService.GetWXStorePageList(request.PageIndex, request.PageSize, request.OrderBy, request.RequestParams.StoreName);
                pageRows.Total = pageRowList.Total;
                pageRows.Data = _mapper.Map<List<GetAppStorePageListReponse>>(pageRowList.Data);

                return Success("查询成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetStorePageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
        /// <summary>
        /// 门店详情
        /// </summary>
        /// <response code="40410">找不到门店</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetAppStoreDetailReponse>> GetStoreDetail([FromBody] BaseRequest<GetStoreDetailRequet> request)
        {
            GetAppStoreDetailReponse result = null;
            try
            {

                var dto = await _storeService.Get(request.Body.StoreId);
                if (dto == null)
                {
                    return NoFind("找不到门店", result);
                }
                result= _mapper.Map<GetAppStoreDetailReponse>(dto);
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetStoreDetail " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
        /// <summary>
        /// 首页门店详情
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetAppStoreDetailReponse>> GetDefaultStoreDetail()
        {
            GetAppStoreDetailReponse result = null;
            try
            {

                var dto = await _storeService.GetDefaultStoreDetail();
                if (dto != null)
                {
                    result = _mapper.Map<GetAppStoreDetailReponse>(dto);
                }
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetDefaultStoreDetail " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
    }
}
