﻿using System.Reflection;

namespace Welshine.Official.Core.Extensions
{
    /// <summary>
    /// Assembly 扩展
    /// </summary>
    public static class AssemblyExtensions
    {
        /// <summary>
        /// 获取运行的DLL的服务名
        /// 如：DTHY.CRM.Api.dll取出 crm
        /// </summary>
        /// <param name="assembly">Assembly.GetExecutingAssembly()</param>
        /// <returns></returns>
        public static string GetServiceName(this Assembly assembly)
        {
            string file = assembly.GetName().Name.ToLower();
            if (file.Contains("."))
            {
                var fiels = file.Split('.');
                return fiels[fiels.Length - 2];
            }
            return file;
        }
    }
}
