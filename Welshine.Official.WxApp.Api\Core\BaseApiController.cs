﻿

using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Welshine.Official.Core.Attributes;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.RestfulApi.Helper;

namespace Welshine.Official.WxApp.Api.Core
{/// <summary>
 /// BaseApiController
 /// </summary>
    [Route("api/[controller]/[action]")]
    [Route(Program.AppName + "/api/[controller]/[action]")]
    [Produces("application/json")]
    [WebApiException]
    [ModelValidate]
    public class BaseApiController : Controller
    {
        /// <summary>
        /// 映射
        /// </summary>
        public IMapper _mapper { get; set; }
        #region 返回方法封装
        /// <summary>
        /// 成功返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Success<T>(string msg, T data = default(T))
        {
            return WebApiResponseHelp.Success(msg, data);
        }

        /// <summary>
        /// 失败返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="errorCode">错误编码</param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Failure<T>(ErrorCode errorCode, T data = default(T))
        {
            return WebApiResponseHelp.Result<T>(errorCode.GetHashCode(), errorCode.ToDescriptionName(), data);
        }
        /// <summary>
        /// 失败返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Failure<T>(string msg, T data = default(T))
        {
            return WebApiResponseHelp.Failure(msg, data);
        }
        /// <summary>
        /// 失败返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="code">错误码</param>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Failure<T>(int code, string msg, T data = default(T))
        {
            return WebApiResponseHelp.Result<T>(code, msg, data);
        }
        /// <summary>
        /// 找不到返回
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> NoFind<T>(string msg, T data = default(T))
        {
            return WebApiResponseHelp.NoFind(msg, data);
        }
        /// <summary>
        /// 返回结果
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="code"></param>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse<T> Result<T>(ApiStatus code, string msg, T data = default(T))
        {
            return WebApiResponseHelp.Result(code, msg, data);
        }
        /// <summary>
        /// 成功放回结果
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse Success(string msg, dynamic data = null)
        {
            return WebApiResponseHelp.Success(msg, data);
        }
        /// <summary>
        /// 失败返回结果
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse Failure(string msg, dynamic data = null)
        {
            return WebApiResponseHelp.Failure(msg, data);
        }
        /// <summary>
        /// 找不到返回结果
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse NoFind(string msg, dynamic data = null)
        {
            return WebApiResponseHelp.NoFind(msg, data);
        }
        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="code"></param>
        /// <param name="msg"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected BaseResponse Result(ApiStatus code, string msg, dynamic data = null)
        {
            return WebApiResponseHelp.Result(code, msg, data);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="isSuccess"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        protected BaseResponse Result(bool isSuccess, string msg)
        {
            return isSuccess ? Success(msg) : Failure(msg);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        protected BaseResponse NotAuthority()
        {
            return Result(ApiStatus.ModularUnauthorized, "权限不足");
        }
        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        protected BaseResponse<T> NotAuthority<T>()
        {
            return Result<T>(ApiStatus.ModularUnauthorized, "权限不足");
        }

        #endregion 返回方法封装
    }
}
