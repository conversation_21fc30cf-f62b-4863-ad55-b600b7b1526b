﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Core.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static void AddFreeRedis(this IServiceCollection services, IConfiguration configuration)
        {
            FreeRedisHelper.Init(new FreeRedisConnectConfig() {
                DefaultConnectStr= configuration.GetSection("ConnectionStrings:RedisStr").Value
            });
        }
        public static void AddMySql(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSqlSugar(new IocConfig()
            {
                //ConfigId="db01"  多租户用到
                ConnectionString = configuration.GetSection("sqlsuger:ConnectionString").Value,
                DbType = IocDbType.MySql,
                IsAutoCloseConnection = true//自动释放
            }); //多个库就传List<IocConfig>

            //配置参数
            services.ConfigurationSugar(db =>
            {
                db.Aop.OnLogExecuting = (sql, p) =>
                {
                    Console.WriteLine(sql);
                };
                //设置更多连接参数
                //db.CurrentConnectionConfig.XXXX=XXXX
                //db.CurrentConnectionConfig.MoreSettings=new ConnMoreSettings(){}
                //二级缓存设置
                //db.CurrentConnectionConfig.ConfigureExternalServices = new ConfigureExternalServices()
                //{
                // DataInfoCacheService = myCache //配置我们创建的缓存类
                //}
                //读写分离设置
                //laveConnectionConfigs = new List<SlaveConnectionConfig>(){...}

                /*多租户注意*/
                //单库是db.CurrentConnectionConfig 
                //多租户需要db.GetConnection(configId).CurrentConnectionConfig 
            });
        }
    }
}
