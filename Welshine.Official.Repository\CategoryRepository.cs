﻿using DTHY.Core.Repository;
using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 分类管理仓储实现
    /// </summary>
    public class CategoryRepository : BaseRepository<Category>, ICategoryRepository
    {
        /// <summary>
        /// 判断分类名称是否存在
        /// </summary>
        /// <param name="nameType">类型: en->英文; cn->中文;</param>
        /// <param name="categoryName">分类名称</param>
        /// <param name="level">级别</param>
        /// <returns></returns>
        public async Task<bool> ExistsCategoryName(string nameType, string categoryName, int level)
        {
            if (nameType == "en")
            {
                return await DbScoped.SugarScope.Queryable<Category>().AnyAsync(x => x.NameEN == categoryName && x.Level == level && !x.IsDeleted);
            }
            else
            {
                return await DbScoped.SugarScope.Queryable<Category>().AnyAsync(x => x.NameCN == categoryName && x.Level == level && !x.IsDeleted);
            }
        }

        /// <summary>
        /// 判断分类名称是否存在
        /// </summary>
        /// <param name="nameType">类型: en->英文; cn->中文;</param>
        /// <param name="categoryName">分类名称</param>
        /// <param name="categoryId">分类Id</param>
        /// <param name="level">级别</param>
        /// <returns></returns>
        public async Task<bool> ExistsCategoryName(string nameType, string categoryName, long categoryId, int level)
        {
            if (nameType == "en")
            {
                return await DbScoped.SugarScope.Queryable<Category>().AnyAsync(x => x.NameEN == categoryName && x.Id != categoryId && x.Level == level && !x.IsDeleted);
            }
            else
            {
                return await DbScoped.SugarScope.Queryable<Category>().AnyAsync(x => x.NameCN == categoryName && x.Id != categoryId && x.Level == level && !x.IsDeleted);
            }
        }

        /// <summary>
        /// 获取分类
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        public async Task<Category> GetCategoryById(long categoryId)
        {
            return await DbScoped.SugarScope.Queryable<Category>().FirstAsync(x => x.Id == categoryId && !x.IsDeleted);
        }

        /// <summary>
        /// 获取分类排序
        /// </summary>
        /// <param name="fatherId">父级Id</param>
        /// <returns></returns>
        public async Task<int> GetCategorySort(int fatherId)
        {
            return await DbScoped.SugarScope.Queryable<Category>().Where(x => x.FatherId == fatherId).MaxAsync(x => x.Sort) + 1;
        }

        /// <summary>
        /// 判断是否有下级
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        public async Task<bool> ExistsChild(long categoryId)
        {
            return await DbScoped.SugarScope.Queryable<Category>().AnyAsync(x => x.FatherId == categoryId && !x.IsDeleted);
        }

        /// <summary>
        /// 获取分类列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<Category>> GetAllCategoryList()
        {
            List<OrderByModel> orderby = new List<OrderByModel>()
            {
                new OrderByModel(){ FieldName = "level", OrderByType = OrderByType.Asc },
                new OrderByModel(){ FieldName = "sort", OrderByType = OrderByType.Asc }
            };
            return await DbScoped.SugarScope.Queryable<Category>().Where(x=>!x.IsDeleted).OrderBy(orderby).ToListAsync();
        }
    }
}
