﻿using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 
    /// </summary>
    public class MobileValidationAttribute : ValidationAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                var valueAsString = value.ToString().Trim();
                if (valueAsString == "") return new ValidationResult("手机号码长度错误.");
                if (!string.IsNullOrWhiteSpace(valueAsString))
                {
                    string errorMessage = "";

                    if (!System.Text.RegularExpressions.Regex.IsMatch(valueAsString, @"^\d*$"))
                    {
                        errorMessage = "手机号码格式不正确，只能输入数字，请重新填写.";
                        return new ValidationResult(errorMessage);
                    }
                    if (valueAsString.Length != 11)
                    {
                        errorMessage = "手机号码长度不符，请重新填写.";
                        return new ValidationResult(errorMessage);
                    }
                }
            }
            return ValidationResult.Success;
        }
    }
}
