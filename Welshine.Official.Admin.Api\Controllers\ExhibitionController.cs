﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 展会管理
    /// </summary>
    public class ExhibitionController : BaseApiController
    {
        private readonly IExhibitionService _exhibitionService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="exhibitionService"></param>
        public ExhibitionController(IExhibitionService exhibitionService)
        {
            _exhibitionService = exhibitionService;
        }

        /// <summary>
        /// 添加展会分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2007">展会名称已存在</response>
        /// <response code="2008">展会略缩图不存在</response>
        /// <response code="2009">展会详情图，最多可上传8张图片</response>
        /// <response code="2010">展会详情图不存在</response>
        /// <response code="2202">文件格式错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddExhibition([FromBody] BaseRequest<AddExhibitionRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Exhibition exhibition = new Exhibition()
                {
                    ExhibitionName = request.Body.ExhibitionName,
                    ExhibitionTime = request.Body.ExhibitionTime,
                    ExhibitionAddress = request.Body.ExhibitionAddress,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _exhibitionService.AddExhibition(exhibition, request.Body.Thumbnail, request.Body.DetailPicture);
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddExhibition Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 修改展会分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2007">展会名称已存在</response>
        /// <response code="2008">展会略缩图不存在</response>
        /// <response code="2009">展会详情图，最多可上传8张图片</response>
        /// <response code="2010">展会详情图不存在</response>
        /// <response code="2011">展会Id不存在</response>
        /// <response code="2202">文件格式错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditExhibition([FromBody] BaseRequest<EditExhibitionRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Exhibition exhibition = new Exhibition()
                {
                    Id = request.Body.ExhibitionId,
                    ExhibitionName = request.Body.ExhibitionName,
                    ExhibitionTime = request.Body.ExhibitionTime,
                    ExhibitionAddress = request.Body.ExhibitionAddress,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _exhibitionService.EditExhibition(exhibition, request.Body.Thumbnail, request.Body.DetailPicture);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditExhibition Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取展会详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2011">展会Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ExhibitionDetailResponse>> GetExhibitionById([FromBody] BaseRequest<ExhibitionIdRequest> request)
        {
            ExhibitionDetailResponse result = null;
            try
            {
                result = await _exhibitionService.GetExhibitionById(request.Body.ExhibitionId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionById Error {u}", ex.Message);
                return Failure<ExhibitionDetailResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 删除展会分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2011">展会Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteExhibition([FromBody] BaseRequest<ExhibitionIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Exhibition exhibition = new Exhibition()
                {
                    Id = request.Body.ExhibitionId,
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _exhibitionService.DeleteExhibition(exhibition);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteExhibition Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<ExhibitionListReponse>>> GetExhibitionPageList([FromBody] RequestPageModel<GetExhibitionListRequest> request)
        {
            PageRows<ExhibitionListReponse> pageRows = null;
            try
            {
                pageRows = await _exhibitionService.GetExhibitionPageList(request.RequestParams.ExhibitionName, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
    }
}
