﻿using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 商户表
    /// </summary>
    [SqlSugar.SugarTable("cms_stores")]
    public class Stores: BaseBigEntity
    {
        /// <summary>
        /// 门店编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_code")]
        public string StoreCode { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_name")]
        public string StoreName { get; set; }

        /// <summary>
        /// 门店地址
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_address")]
        public string StoreAddress { get; set; }

        /// <summary>
        /// 门店开始营业时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_start_time")]
        public string StoreStartTime { get; set; }

        /// <summary>
        /// 门店结束营业时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_end_time")]
        public string StoreEndTime { get; set; }

        /// <summary>
        /// 门店状态
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_status")]
        public EnumStoreStatus StoreStatus { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_phone")]
        public string StorePhone { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_fax")]
        public string StoreFax { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_email")]
        public string StoreEmail { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_longitude")]
        public decimal StoreLongitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_latitude")]
        public decimal StoreLatitude { get; set; }

        /// <summary>
        /// 略缩图
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_thumbnail")]
        public string StoreThumbnail { get; set; }

        /// <summary>
        /// 联系图
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_relation")]
        public string StoreRelation { get; set; }
        /// <summary>
        /// 是否首页
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "store_show_homepage")]
        public bool StoreShowHomepage { get; set; }
    }
}
