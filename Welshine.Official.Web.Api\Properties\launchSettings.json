{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "weatherforecast", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Welshine.Official.Web.Api": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:5000"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/weatherforecast", "publishAllPorts": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:61436", "sslPort": 0}}}