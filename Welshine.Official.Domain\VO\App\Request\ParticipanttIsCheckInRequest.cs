﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.App.Request
{
    public class ParticipanttIsCheckInRequest
    {
        /// <summary>
        /// 展会活动Id
        /// </summary>
        [Required(ErrorMessage = "展会活动Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "展会活动Id参数错误")]
        public long ExhibitionActivityId { get; set; }

        /// <summary>
        /// 动态令牌
        /// </summary>
        [Required(ErrorMessage = "动态令牌是必填项,请完善", AllowEmptyStrings = false)]
        public string Code { get; set; }
    }
}
