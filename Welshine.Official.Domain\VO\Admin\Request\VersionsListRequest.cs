﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 获取版本列表条目
    /// </summary>
    public class VersionsListRequest
    {
        ///// <summary>
        ///// 版本类型: 0->首页Banner;1->首页中部广告;2->关于惠而信;3->联系方式;
        ///// </summary>
        //[Required(ErrorMessage = "版本类型必填,请完善")]
        //[Range(0, 1, ErrorMessage = "版本类型参数错误")]
        //public int VersionType { get; set; }

        /// <summary>
        /// 版本标题
        /// </summary>
        [StringLength(30, ErrorMessage = "版本标题长度不符", MinimumLength = 1)]
        public string VersionsTitle { get; set; }

        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [Range(0, 1, ErrorMessage = "内容状态参数错误")]
        public int? ReleaseStatus { get; set; }

        /// <summary>
        /// 审核状态: 0->待提交; 1->待审核; 2->审核通过; 3->审核驳回
        /// </summary>
        [Range(0, 3, ErrorMessage = "审核状态参数错误")]
        public int? ApproverStatus { get; set; }

        /// <summary>
        /// 发布时间筛选
        /// </summary>
        public TimeHorizon ReleaseTimeScope { get; set; }
    }
}
