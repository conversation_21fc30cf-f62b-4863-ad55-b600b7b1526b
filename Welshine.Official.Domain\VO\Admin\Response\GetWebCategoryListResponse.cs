﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class GetWebCategoryListResponse
    {
        /// <summary>
        /// 分类Id
        /// </summary>
        public long CategoryId { get; set; }

        /// <summary>
        /// 分类名称(中文)
        /// </summary>
        public string CategoryNameCN { get; set; }

        /// <summary>
        /// 分类名称(英文)
        /// </summary>
        public string CategoryNameEN { get; set; }

        /// <summary>
        /// 未激活logo图片
        /// </summary>
        public string logoImgUrl { get; set; }

        /// <summary>
        /// 激活logo图片
        /// </summary>
        public string logoImgUrlActivate { get; set; }

        /// <summary>
        /// 手机版激活logo图片
        /// </summary>
        public string mobileImgUrlActivate { get; set; }

        /// <summary>
        /// 父类Id
        /// </summary>
        public long parentCategoryId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int sort { get; set; }

        /// <summary>
        /// 是否显示
        /// </summary>
        public bool isDelete { get; set; }

        /// <summary>
        /// 英文是否显示
        /// </summary>
        public bool ENShow { get; set; }

        /// <summary>
        /// 中文是否显示
        /// </summary>
        public bool CNShow { get; set; }

        /// <summary>
        /// 子级
        /// </summary>
        public List<GetWebCategoryListResponse> Children { get; set; }
    }
}
