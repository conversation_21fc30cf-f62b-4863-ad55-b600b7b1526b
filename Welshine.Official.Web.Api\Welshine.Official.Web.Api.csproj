<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <StartupObject>Welshine.Official.Web.Api.Program</StartupObject>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile>./Welshine.Official.Web.Api.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Welshine.Official.Repository\Welshine.Official.Repository.csproj" />
    <ProjectReference Include="..\Welshine.Official.Service\Welshine.Official.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile_jenkins">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Welshine.Official.Web.Api.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>


</Project>
