﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 图册服务实现
    /// </summary>
    public class AtlasService : IAtlasService
    {
        private readonly IAtlasRepository _atlasRepository;
        private readonly IFileRepository _fileRepository;

        public AtlasService(IAtlasRepository atlasRepository, IFileRepository fileRepository)
        {
            _atlasRepository = atlasRepository;
            _fileRepository = fileRepository;
        }

        /// <summary>
        /// 添加图册
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        public async Task<bool> AddAtlas(Atlas atlas, string thumbnail, string fileId)
        {
            await AddAtlasVaild(atlas.AtlasName, thumbnail, fileId);

            var result = await _atlasRepository.AddAtlas(atlas, thumbnail, fileId);

            return result;
        }

        /// <summary>
        /// 添加图册校验
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        public async Task AddAtlasVaild(string atlasName, string thumbnail, string fileId)
        {
            //图册名称校验
            var result = await _atlasRepository.ExistsAtlasName(atlasName);
            if (result)
            {
                throw new BusinessException(ErrorCode.AtlasNameError.ToDescriptionName(), ErrorCode.AtlasNameError.GetHashCode());
            }

            //封面图校验
            var fileList = await _fileRepository.GetList(new List<string>() { thumbnail }.ToList());
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //图册文件校验
            fileList = await _fileRepository.GetList(new List<string>() { fileId }.ToList());
            fileFormat = new List<string>() { "pdf" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //封面图校验
            result = await _fileRepository.Exists(thumbnail);
            if (!result)
            {
                throw new BusinessException(ErrorCode.AtlasThumbnailNotFind.ToDescriptionName(), ErrorCode.AtlasThumbnailNotFind.GetHashCode());
            }

            //图册文件校验
            result = await _fileRepository.Exists(fileId);
            if (!result)
            {
                throw new BusinessException(ErrorCode.AtlasFileNotFind.ToDescriptionName(), ErrorCode.AtlasFileNotFind.GetHashCode());
            }
        }

        /// <summary>
        /// 修改图册
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        public async Task<bool> EditAtlas(Atlas atlas, string thumbnail, string fileId)
        {
            var result = false;

            await EditAtlasVaild(atlas.AtlasName, atlas.Id, thumbnail, fileId);

            var entity = await _atlasRepository.GetAtlas(atlas.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.AtlasNotFind.ToDescriptionName(), ErrorCode.AtlasNotFind.GetHashCode());
            }

            entity.AtlasName = atlas.AtlasName;
            entity.ModifierId = atlas.CreatorId;
            entity.UpdatedBy = atlas.UpdatedBy;
            entity.UpdatedTime = DateTime.Now;

            await _atlasRepository.EditAtlas(entity, thumbnail, fileId);

            return result;
        }

        /// <summary>
        /// 修改图册校验
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="atlasId">图册Id</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        public async Task EditAtlasVaild(string atlasName, long atlasId, string thumbnail, string fileId)
        {
            //图册名称校验
            var result = await _atlasRepository.ExistsAtlasName(atlasName, atlasId);
            if (result)
            {
                throw new BusinessException(ErrorCode.AtlasNameError.ToDescriptionName(), ErrorCode.AtlasNameError.GetHashCode());
            }

            //封面图校验
            var fileList = await _fileRepository.GetList(new List<string>() { thumbnail }.ToList());
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //图册文件校验
            fileList = await _fileRepository.GetList(new List<string>() { fileId }.ToList());
            fileFormat = new List<string>() { "pdf" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //封面图校验
            result = await _fileRepository.Exists(thumbnail);
            if (!result)
            {
                throw new BusinessException(ErrorCode.AtlasThumbnailNotFind.ToDescriptionName(), ErrorCode.AtlasThumbnailNotFind.GetHashCode());
            }

            //图册文件校验
            result = await _fileRepository.Exists(fileId);
            if (!result)
            {
                throw new BusinessException(ErrorCode.AtlasFileNotFind.ToDescriptionName(), ErrorCode.AtlasFileNotFind.GetHashCode());
            }
        }

        /// <summary>
        /// 获取图册
        /// </summary>
        /// <param name="atlasId">图册Id</param>
        /// <returns></returns>
        public async Task<AtlasDetailResponse> GetAtlasById(long atlasId)
        {
            var entity = await _atlasRepository.GetAtlas(atlasId);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.AtlasNotFind.ToDescriptionName(), ErrorCode.AtlasNotFind.GetHashCode());
            }

            AtlasDetailResponse result = new AtlasDetailResponse()
            {
                AtlasId = entity.Id,
                AtlasCode = entity.AtlasCode,
                AtlasName = entity.AtlasName,
                UpdatedTime = entity.UpdatedTime
            };

            var files = await _fileRepository.GetFileList(new List<long> { atlasId });

            var thumbnail = files.Where(x => x.TableEnum == EnumRelationType.AtlasThumbnail).FirstOrDefault();
            if (thumbnail != null)
            {
                result.Thumbnail = new Domain.VO.FileResponse() { FileId = thumbnail.FileId, FileName = thumbnail.Name, Url = thumbnail.Url };
            }

            var atlasFile = files.Where(x => x.TableEnum == EnumRelationType.AtlasFile).FirstOrDefault();
            if (atlasFile != null)
            {
                result.AtlasFile = new Domain.VO.FileResponse() { FileId = atlasFile.FileId, FileName = atlasFile.Name, Url = atlasFile.Url };
            }

            return result;
        }

        /// <summary>
        /// 删除图册
        /// </summary>
        /// <param name="atlas">图册信息</param>
        /// <returns></returns>
        public async Task<bool> DeleteAtlas(Atlas atlas)
        {
            var entity = await _atlasRepository.GetAtlas(atlas.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.AtlasNotFind.ToDescriptionName(), ErrorCode.AtlasNotFind.GetHashCode());
            }

            return await _atlasRepository.DeleteAtlas(atlas);
        }

        /// <summary>
        /// 获取图册列表
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="times">时间筛选</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<AtlasListReponse>> GetAtlasPageList(string atlasName, TimeHorizon times, int pageIndex = 1, int pageSize = 10)
        {
            return await _atlasRepository.GetAtlasPageList(atlasName, times, pageIndex, pageSize);
        }

        /// <summary>
        /// 获取最新图册
        /// </summary>
        /// <param name="openId">用户openId</param>
        /// <returns></returns>
        public async Task<AtlasDetailResponse> WX_GetAtlas(string openId)
        {
            return await _atlasRepository.WX_GetAtlas(openId);
        }
    }
}
