﻿namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 展会表
    /// </summary>
    [SqlSugar.SugarTable("cms_exhibition")]
    public class Exhibition: BaseBigEntity
    {
        /// <summary>
        /// 展会编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_code")]
        public string ExhibitionCode { get; set; }

        /// <summary>
        /// 展会名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_name")]
        public string ExhibitionName { get; set; }

        /// <summary>
        /// 展会时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_time")]
        public string ExhibitionTime { get; set; }

        /// <summary>
        /// 展会地址
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_address")]
        public string ExhibitionAddress { get; set; }
    }
}
