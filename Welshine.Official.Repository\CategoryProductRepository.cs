﻿using DocumentFormat.OpenXml.Bibliography;
using DTHY.Core.Repository;
using Org.BouncyCastle.Utilities;
using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 分类产品仓储实现
    /// </summary>
    public class CategoryProductRepository : BaseRepository<CategoryProduct>, ICategoryProductRepository
    {
        private readonly IFileRepository _fileRepository;

        public CategoryProductRepository(IFileRepository fileRepository)
        {
            _fileRepository = fileRepository;
        }

        /// <summary>
        /// 获取分类产品
        /// </summary>
        /// <param name="categoryProductId"></param>
        /// <returns></returns>
        public async Task<CategoryProduct> GetCategoryProductById(long categoryProductId)
        { 
            return await DbScoped.SugarScope.Queryable<CategoryProduct>().FirstAsync(x => x.Id == categoryProductId && !x.IsDeleted);
        }

        /// <summary>
        /// 获取当前分类商品最大排序
        /// </summary>
        /// <param name="categoryId">分类Id</param>
        /// <param name="productLanguage">语言: 0->英文; 1->中文</param>
        /// <returns></returns>
        public async Task<int> GetMaxSort(long categoryId, int productLanguage)
        {
            return await DbScoped.SugarScope.Queryable<CategoryProduct>().Where(x => x.CategoryId == categoryId && x.ProductLanguage == productLanguage && x.ReleaseStatus == 1).MaxAsync(x => x.Sort.Value) + 1;
        }

        /// <summary>
        /// 获取分类产品列表
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="productName">商品名称</param>
        /// <param name="ReleaseStatus">内容状态</param>
        /// <param name="times">创建时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageRows<CategoryProductListResponse>> GetCategoryProductPageList(int? productLanguage, string productName, int? ReleaseStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<CategoryProductListResponse> result = new PageRows<CategoryProductListResponse>();

            result.Data = await DbScoped.SugarScope.Queryable<CategoryProduct>()
                .Where(x => !x.IsDeleted)
                .WhereIF(!string.IsNullOrWhiteSpace(productName), x => x.ProductName.Contains(productName))
                .WhereIF(productLanguage != null, x => x.ProductLanguage == productLanguage)
                .WhereIF(ReleaseStatus != null, x => x.ReleaseStatus == ReleaseStatus)
                .WhereIF(times != null && times.From != null, x => x.CreatedTime >= times.From)
                .WhereIF(times != null && times.To != null, x => x.CreatedTime <= times.To)
                .OrderByIF(orderFile == "CategoryProductId" || orderFile == "id", x => x.Id, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "CreatedTime", x => x.CreatedTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "UpdatedTime", x => x.UpdatedTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .Select(x => new CategoryProductListResponse
                {
                    CategoryProductId = x.Id,
                    ProductName = x.ProductName,
                    ReleaseStatus = x.ReleaseStatus,
                    CreatedTime = x.CreatedTime,
                    UpdatedUser = x.UpdatedBy,
                    UpdatedTime = x.UpdatedTime

                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            var ids = result.Data.Select(x=>x.CategoryProductId).ToList();
            var files = await _fileRepository.GetFileList(ids, EnumRelationType.CategoryProductPicture);

            foreach (var item in result.Data)
            {
                var file = files.Where(x => x.ObjectId == item.CategoryProductId).OrderBy(x => x.CreatedTime).FirstOrDefault();
                if (file != null)
                {
                    item.ProductImg = file.Url;
                }
            }

            result.Total = totalNumber;

            return result;
        }

        /// <summary>
        /// 根据分类Id获取已发布的分类商品
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="categoryId">分类id</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PageRows<GetWebCategoryProductListResponse>> GetCategoryProductListByCategoryId(long? categoryId, int? productLanguage, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<GetWebCategoryProductListResponse> result = new PageRows<GetWebCategoryProductListResponse>();

            result.Data = await DbScoped.SugarScope.Queryable<CategoryProduct>()
            .Where(x => !x.IsDeleted && x.CategoryId == categoryId && x.ReleaseStatus == 1)
            .WhereIF(productLanguage != null, x => x.ProductLanguage == productLanguage)
            .WhereIF(categoryId != null, x=>x.CategoryId == categoryId)
            .OrderBy(x => x.Sort, OrderByType.Asc)
                .Select(x => new GetWebCategoryProductListResponse
                {
                    ProductId = x.Id,
                    CategoryId = x.CategoryId,
                    ProductName = x.ProductName,
                    //ProductTexture = x.ProductTexture,
                    //ProductColor = x.ProductColor,
                    //ProductSpecification = x.ProductSpecification,
                    ProductIntroduce = x.ProductIntroduce 

                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            var ids = result.Data.Select(x => x.ProductId).ToList();
            var files = await _fileRepository.GetFileList(ids, EnumRelationType.CategoryProductPicture);

            foreach (var item in result.Data)
            {
                var file = files.Where(x => x.ObjectId == item.ProductId).OrderBy(x => x.CreatedTime).FirstOrDefault();
                item.ProductImg = file.Url;
            }

            result.Total = totalNumber;

            return result;
        }

        /// <summary>
        /// 分类是否有产品
        /// </summary>
        /// <param name="categoryId">分类Id</param>
        /// <returns></returns>
        public async Task<bool> ExistsCategoryProduct(long categoryId)
        {
            return await DbScoped.SugarScope.Queryable<CategoryProduct>().AnyAsync(x => x.CategoryId == categoryId && !x.IsDeleted);
        }

        /// <summary>
        /// 根据分类Id获取已发布的分类商品(并按sort排序)
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        public async Task<GetCategoryProductListOrderSortResponse> GetCategoryProductListByCategoryId(long categoryId)
        {
            List<CategoryProductResponse> list = await DbScoped.SugarScope.Queryable<CategoryProduct>()
            .Where(x => !x.IsDeleted && x.CategoryId == categoryId && x.ReleaseStatus == 1)
            .OrderBy(x => x.Sort, OrderByType.Asc)
                .Select(x => new CategoryProductResponse
                {
                    ProductId = x.Id,
                    CategoryId = x.CategoryId,
                    Sort = x.Sort,
                    ProductName = x.ProductName,
                    ProductLanguage = x.ProductLanguage,

                })
                .ToListAsync();

            var ids = list.Select(x => x.ProductId).ToList();
            var files = await _fileRepository.GetFileList(ids, EnumRelationType.CategoryProductPicture);

            foreach (var item in list)
            {
                var file = files.Where(x => x.ObjectId == item.ProductId).OrderBy(x => x.CreatedTime).FirstOrDefault();
                item.ProductImg = file.Url;
            }

            GetCategoryProductListOrderSortResponse result = new GetCategoryProductListOrderSortResponse();
            result.EnProductList = list.Where(x => x.ProductLanguage == 0).OrderBy(x => x.Sort).ToList();
            result.CnProductList = list.Where(x => x.ProductLanguage == 1).OrderBy(x => x.Sort).ToList();

            return result;
        }
    }
}
