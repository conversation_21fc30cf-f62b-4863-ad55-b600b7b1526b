﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MySqlX.XDevAPI.Common;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;

namespace Welshine.Official.Admin.Api.Controllers
{
    public class AccountController :BaseApiController
    {
        private readonly AccountService _accountService = new AccountService();
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public BaseResponse<LoginResponse> Login([FromBody]BaseRequest<LoginRequest> request)
        {
            var result = _accountService.Login(request.Body.LoginName,request.Body.Password);
            return Success("请求成功",result);
        }
        /// <summary>
        /// 刷新token
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<LoginResponse> FreshToken()
        {
            var currentUser = GetUserInfo();
            var result = _accountService.FreshToken(currentUser);
            return Success("请求成功", result);
        }
        /// <summary>
        /// 登出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse LogOut()
        {
            var currentUser = GetUserInfo();
            _accountService.LogOut(currentUser);
            return Success("请求成功");
        }
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse AlterPassword([FromBody] BaseRequest<AlterPasswordRequest> request)
        {
            var currentUser = GetUserInfo() ;
            _accountService.AlterPassword(currentUser.LoginName, request.Body.OldPassword, request.Body.NewPassword);
            return Success("请求成功");
        }
    }
}
