﻿using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 图册服务实现
    /// </summary>
    public class VersionsService : IVersionsService
    {
        private readonly IVersionsRepository _versionsRepository;

        public VersionsService(IMapper mapper, IVersionsRepository versionsRepository)
        {
            _versionsRepository = versionsRepository;
        }

        /// <summary>
        /// 添加版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> AddVersions(Versions versions)
        {
            if (versions.VersionType != 3)
            {
                var resultTitle = await _versionsRepository.ExistsTitle(versions.VersionType, versions.Title);
                if (resultTitle)
                {
                    throw new BusinessException(ErrorCode.VersionsTitleError.ToDescriptionName(), ErrorCode.VersionsTitleError.GetHashCode());
                }
            }

            return await _versionsRepository.AddVersions(versions);
        }

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <param name="versionsId">版本Id</param>
        /// <returns></returns>
        public async Task<GetVersionsResponse<T>> GetVersions<T>(int versionType, long versionsId)
        {
            Versions versions = await _versionsRepository.GetVersions(versionType, versionsId);
            if (versions == null)
            {
                throw new BusinessException(ErrorCode.VersionsIdNoFoundError.ToDescriptionName(), ErrorCode.VersionsIdNoFoundError.GetHashCode());
            }

            GetVersionsResponse<T> result = new GetVersionsResponse<T>
            {
                VersionsId = versions.Id,
                Title = versions.Title,
                Content = versions.Content,
                Approver = versions.Approver,
                ApprovalTime = versions.ApprovalTime,
                ApprovalOpinion = versions.ApprovalOpinion,
                ReleaseTime = versions.ReleaseTime,
                Status = versions.ReleaseStatus,
                ApproverStatus = versions.ApproverStatus,
                Data = JsonConvert.DeserializeObject<T>(versions.RData)
            };

            return result;
        }

        /// <summary>
        /// 修改版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> EditVersions(Versions versions)
        {
            var entity = await _versionsRepository.GetVersions(versions.VersionType, versions.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.VersionsIdNoFoundError.ToDescriptionName(), ErrorCode.VersionsIdNoFoundError.GetHashCode());
            }

            //if (entity.CreatorId == versions.ModifierId)
            //{
            //    throw new BusinessException(ErrorCode.VersionsEditUserError.ToDescriptionName(), ErrorCode.VersionsEditUserError.GetHashCode());
            //}

            if (entity.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.VersionsEditReleaseStatusrError.ToDescriptionName(), ErrorCode.VersionsEditReleaseStatusrError.GetHashCode());
            }

            if (entity.ApproverStatus == 1 || entity.ApproverStatus == 2)
            {
                throw new BusinessException(ErrorCode.VersionsEditApproverStatusError.ToDescriptionName(), ErrorCode.VersionsEditApproverStatusError.GetHashCode());
            }

            if (versions.VersionType != 3)
            {
                var resultTitle = await _versionsRepository.ExistsTitle(versions.VersionType, versions.Title, versions.Id);
                if (resultTitle)
                {
                    throw new BusinessException(ErrorCode.VersionsTitleError.ToDescriptionName(), ErrorCode.VersionsTitleError.GetHashCode());
                }
            }

            if (versions.ApproverStatus == 1)
            {
                entity.ApproverStatus = 1;
            }
            entity.Title = versions.Title;
            entity.Content = versions.Content;
            entity.RData = versions.RData;
            entity.ApproverStatus = versions.ApproverStatus;
            entity.SubmitApprover = versions.SubmitApprover;
            entity.SubmitApproverId = versions.SubmitApproverId;
            entity.SubmitApprovalTime = versions.SubmitApprovalTime;
            entity.UpdatedBy = versions.UpdatedBy;
            entity.ModifierId = versions.ModifierId;
            entity.UpdatedTime = versions.UpdatedTime;

            return await _versionsRepository.EditVersions(entity);
        }

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <returns></returns>
        public async Task<bool> ExistsRelease(int versionType)
        { 
            return await _versionsRepository.ExistsRelease(versionType);
        }

        /// <summary>
        /// 删除版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> DeleteVersions(Versions versions)
        {
            var entity = await _versionsRepository.GetVersions(versions.VersionType, versions.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.VersionsIdNoFoundError.ToDescriptionName(), ErrorCode.VersionsIdNoFoundError.GetHashCode());
            }

            if (entity.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.VersionsDeleteReleaseStatusError.ToDescriptionName(), ErrorCode.VersionsDeleteReleaseStatusError.GetHashCode());
            }

            if (entity.ApproverStatus == 1)
            {
                throw new BusinessException(ErrorCode.VersionsDeleteApproverStatusError.ToDescriptionName(), ErrorCode.VersionsDeleteApproverStatusError.GetHashCode());
            }

            entity.IsDeleted = versions.IsDeleted;
            entity.UpdatedBy = versions.UpdatedBy;
            entity.ModifierId = versions.ModifierId;
            entity.UpdatedTime = versions.UpdatedTime;

            return await _versionsRepository.EditVersions(entity); ;
        }

        /// <summary>
        /// 提交版本信息审核
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> SubmitVersionsApprove(Versions versions)
        {
            var entity = await _versionsRepository.GetVersions(versions.VersionType, versions.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.VersionsIdNoFoundError.ToDescriptionName(), ErrorCode.VersionsIdNoFoundError.GetHashCode());
            }

            if (entity.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.VersionsSubmitReleaseStatusError.ToDescriptionName(), ErrorCode.VersionsSubmitReleaseStatusError.GetHashCode());
            }

            if (entity.ApproverStatus == 1 || entity.ApproverStatus == 2)
            {
                throw new BusinessException(ErrorCode.VersionsSubmitApproverStatusError.ToDescriptionName(), ErrorCode.VersionsSubmitApproverStatusError.GetHashCode());
            }

            entity.ApproverStatus = versions.ApproverStatus;
            entity.UpdatedBy = versions.UpdatedBy;
            entity.ModifierId = versions.ModifierId;
            entity.UpdatedTime = versions.UpdatedTime;
            entity.SubmitApprover = versions.SubmitApprover;
            entity.SubmitApproverId = versions.SubmitApproverId;
            entity.SubmitApprovalTime = versions.SubmitApprovalTime;

            return await _versionsRepository.EditVersions(entity);
        }

        /// <summary>
        /// 版本信息审核
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> VersionsApprove(Versions versions)
        {
            var entity = await _versionsRepository.GetVersions(versions.VersionType, versions.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.VersionsIdNoFoundError.ToDescriptionName(), ErrorCode.VersionsIdNoFoundError.GetHashCode());
            }

            if (entity.ApproverStatus != 1)
            {
                throw new BusinessException(ErrorCode.BannerVersionsApproveError.ToDescriptionName(), ErrorCode.BannerVersionsApproveError.GetHashCode());
            }

            if (versions.ApproverStatus == 2)
            {
                if (versions.VersionType != EnumVersionType.Contacts.GetHashCode())
                {
                    await _versionsRepository.ResetReleaseStatus(entity.VersionType);
                }
                else
                {
                    int count = await _versionsRepository.GetReleaseCount(EnumVersionType.Contacts.GetHashCode());
                    if (count >= 20)
                    {
                        throw new BusinessException(ErrorCode.VersionsReleaseCountError.ToDescriptionName(), ErrorCode.VersionsReleaseCountError.GetHashCode());
                    }
                }

                entity.ReleaseStatus = 1;
                entity.ReleaseTime = versions.ApprovalTime;
            }
            entity.ApproverStatus = versions.ApproverStatus;
            entity.ApprovalOpinion = versions.ApprovalOpinion;
            entity.ApproverId = versions.ApproverId;
            entity.Approver = versions.Approver;
            entity.ApprovalTime = versions.ApprovalTime;
            entity.UpdatedBy = versions.UpdatedBy;
            entity.ModifierId = versions.ModifierId;
            entity.UpdatedTime = versions.UpdatedTime;

            return await _versionsRepository.EditVersions(entity);
        }

        /// <summary>
        /// 获取版本列表
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="versionType">版本类型</param>
        /// <param name="title">版本标题</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<VersionsListResponse>> GetVersionsPageList(string userId, int versionType, string title, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            return await _versionsRepository.GetVersionsPageList(userId, versionType, title, releaseStatus, approverStatus, times, orderFile, sortType, pageIndex, pageSize);
        }

        /// <summary>
        /// 版本发布
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> VersionsRelease(Versions versions)
        {
            var entity = await _versionsRepository.GetVersions(versions.VersionType, versions.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.VersionsIdNoFoundError.ToDescriptionName(), ErrorCode.VersionsIdNoFoundError.GetHashCode());
            }

            if (entity.ReleaseStatus == 1 && versions.ReleaseStatus == 1)
            {
                throw new BusinessException(ErrorCode.VersionsReleaseStatusError.ToDescriptionName(), ErrorCode.VersionsReleaseStatusError.GetHashCode());
            }

            if (entity.ReleaseStatus == 0 && versions.ReleaseStatus == 0)
            {
                throw new BusinessException(ErrorCode.VersionsReleaseStatusWError.ToDescriptionName(), ErrorCode.VersionsReleaseStatusWError.GetHashCode());
            }

            if (entity.ApproverStatus != 2)
            {
                throw new BusinessException(ErrorCode.VersionsApproverStatusError.ToDescriptionName(), ErrorCode.VersionsApproverStatusError.GetHashCode());
            }


            if (versions.VersionType != EnumVersionType.Contacts.GetHashCode())
            {
                await _versionsRepository.ResetReleaseStatus(entity.VersionType);
            }
            else
            {
                if (versions.ReleaseStatus == 1)
                {
                    int count = await _versionsRepository.GetReleaseCount(EnumVersionType.Contacts.GetHashCode());
                    if (count >= 20)
                    {
                        throw new BusinessException(ErrorCode.VersionsReleaseCountError.ToDescriptionName(), ErrorCode.VersionsReleaseCountError.GetHashCode());
                    }
                }
            }

            entity.ReleaseStatus = versions.ReleaseStatus;
            if (entity.ReleaseStatus == 1)
            {
                entity.ReleaseTime = DateTime.Now;
            }
            entity.UpdatedBy = versions.UpdatedBy;
            entity.ModifierId = versions.ModifierId;
            entity.UpdatedTime = versions.UpdatedTime;

            return await _versionsRepository.EditVersions(entity);
        }

        /// <summary>
        /// 获取所有已发布的版本
        /// </summary>
        /// <returns></returns>
        public async Task<List<VersionsListResponse>> GetAllReleaseVersions()
        {
            return await _versionsRepository.GetAllReleaseVersions();
        }

        /// <summary>
        /// 获取直营门店列表(管理端)
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="storeName">门店名称</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<GetContactsPageListResponse>> GetContactsPageList(string userId, string storeName, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            return await _versionsRepository.GetContactsPageList(userId, storeName, releaseStatus, approverStatus, times, orderFile, sortType, pageIndex, pageSize);
        }

        /// <summary>
        /// 获取直营门店列表(官网)
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<GetWebContactsPageListResponse> GetWebContactsPageList(int pageIndex = 1, int pageSize = 10)
        {
            return await _versionsRepository.GetWebContactsPageList(pageIndex, pageSize);
        }

        /// <summary>
        /// 首页
        /// </summary>
        /// <returns></returns>
        public async Task<GetHomePageInfoResponse> GetHomePageInfo() 
        {
            GetHomePageInfoResponse result = new GetHomePageInfoResponse();

            List<VersionsListResponse> list = await GetAllReleaseVersions();

            VersionsListResponse bannerData = list.Where(x => x.VersionType == 0).FirstOrDefault();
            if (bannerData != null)
            {
                result.BannerData = JsonConvert.DeserializeObject<BannerData>(bannerData.RData);
            }

            VersionsListResponse midData = list.Where(x => x.VersionType == 1).FirstOrDefault();
            if (midData != null)
            {
                result.MidData = JsonConvert.DeserializeObject<MidData>(midData.RData);
            }

            return result;
        }

        /// <summary>
        /// 获取中文产品图
        /// </summary>
        /// <returns></returns>
        public async Task<ProductCNData> GetProductCN()
        {
            Versions versions = await _versionsRepository.GetProductCN();
            if (versions == null)
            {
                throw new BusinessException(ErrorCode.VersionsIdNoFoundError.ToDescriptionName(), ErrorCode.VersionsIdNoFoundError.GetHashCode());
            }

            ProductCNData result = JsonConvert.DeserializeObject<ProductCNData>(versions.RData);

            return result;
        }

        /// <summary>
        /// 底部横幅
        /// </summary>
        /// <returns></returns>
        public async Task<BottomData> GetBottomData()
        {
            BottomData result = new BottomData();

            List<VersionsListResponse> list = await GetAllReleaseVersions();

            VersionsListResponse bottomData = list.Where(x => x.VersionType == 5).FirstOrDefault();
            if (bottomData != null)
            {
                result = JsonConvert.DeserializeObject<BottomData>(bottomData.RData);
            }

            return result;
        }

        /// <summary>
        /// 关于惠而信
        /// </summary>
        /// <returns></returns>
        public async Task<WelshineData> GetWelshineData()
        {
            WelshineData result = new WelshineData();

            List<VersionsListResponse> list = await GetAllReleaseVersions();

            VersionsListResponse welshineData = list.Where(x => x.VersionType == 2).FirstOrDefault();
            if (welshineData != null)
            {
                result = JsonConvert.DeserializeObject<WelshineData>(welshineData.RData);
            }

            return result;
        }

        /// <summary>
        /// 侧边横幅
        /// </summary>
        /// <returns></returns>
        public async Task<SidebarData> GetSidebarData()
        {
            SidebarData result = new SidebarData();

            List<VersionsListResponse> list = await GetAllReleaseVersions();

            VersionsListResponse sidebarData = list.Where(x => x.VersionType == 6).FirstOrDefault();
            if (sidebarData != null)
            {
                result = JsonConvert.DeserializeObject<SidebarData>(sidebarData.RData);
            }

            return result;
        }
    }
}
