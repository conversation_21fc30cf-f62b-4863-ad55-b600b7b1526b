﻿using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.RestfulApi.Base
{
    /// <summary>
    /// 基础请求类
    /// </summary>
    public class BaseRequest
    {

        /// <summary>
        /// 请求头
        /// </summary>
        public RequestHead Head { get; set; }
    }
    /// <summary>
    /// 请求实体
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BaseRequest<T> : BaseRequest where T : new()
    {
        /// <summary>
        /// 请求体
        /// </summary>
        [Required(ErrorMessage = "请求数据不能为空")]
        public T Body { get; set; }
    }

    /// <summary>
    /// RequestHead
    /// </summary>
    public class RequestHead
    {
        /// <summary>
        /// RequestId
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public string Uid { get; set; }

        /// <summary>
        /// 前端默认值，用于判断属于哪个应用
        /// </summary>
        public int AppId { get; set; }
    }
}
