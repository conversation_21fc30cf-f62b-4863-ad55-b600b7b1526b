﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;
using Welshine.Official.Web.Api.Core;

namespace Welshine.Official.Web.Api.Controllers
{
    /// <summary>
    /// 产品介绍
    /// </summary>
    public class ProductController : BaseApiController
    {
        private readonly ICategoryService _categoryService;
        private readonly ICategoryProductService _categoryProductService;
        private readonly IVersionsService _versionsService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="categoryService"></param>
        public ProductController(ICategoryService categoryService, ICategoryProductService categoryProductService, IVersionsService versionsService)
        {
            _categoryService = categoryService;
            _categoryProductService = categoryProductService;
            _versionsService = versionsService;
        }

        /// <summary>
        /// 获取分类列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<GetWebCategoryListResponse>>> GetCategoryList()
        {
            List<GetWebCategoryListResponse> result = null;
            try
            {
                result = await _categoryService.GetWebCategoryList();
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategoryList Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 根据分类Id获取已发布的分类产品
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<GetWebCategoryProductListResponse>>> GetCategoryProductListByCategoryId([FromBody] RequestPageModel<GetCategoryProductListByCategoryIdRequest> request)
        {
            PageRows<GetWebCategoryProductListResponse> pageRows = null;
            try
            {
                pageRows = await _categoryProductService.GetCategoryProductListByCategoryId(request.RequestParams.CategoryId, request.RequestParams.ProductLanguage, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategoryProductListByCategoryId " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 获取分类产品详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2228">分类产品不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetCategoryProductResponse>> GetCategoryProductDetail([FromBody] BaseRequest<CategoryProductIdRequest> request)
        {
            GetCategoryProductResponse result = null;
            try
            {
                result = await _categoryProductService.GetCategoryProductDetail(request.Body.CategoryProductId.Value);
                if (result.ReleaseStatus == 0)
                {
                    result = null;
                    throw new BusinessException(ErrorCode.CategoryProductNoFoundError.ToDescriptionName(), ErrorCode.CategoryProductNoFoundError.GetHashCode());
                }
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategoryProductDetail Error {u}", ex.Message);
                return Failure<GetCategoryProductResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 获取中文产品
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ProductCNData>> GetProductCNVersions()
        {
            ProductCNData result = null;
            try
            {
                result = await _versionsService.GetProductCN();
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductCNVersions Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

    }
}
