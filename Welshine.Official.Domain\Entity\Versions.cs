﻿using System;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 版本表
    /// </summary>
    [SqlSugar.SugarTable("cms_versions")]
    public class Versions : BaseBigEntity
    {
        /// <summary>
        /// 版本类型: 0->首页Banner;1->首页中部广告;2->关于惠而信;3->联系方式;4->中文产品介绍;5->首页底部横幅;6->侧边浮层;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "version_type")]
        public int VersionType { get; set; }

        /// <summary>
        /// 版本内容
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "content")]
        public string Content { get; set; }

        /// <summary>
        /// 版本标题
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "title")]
        public string Title { get; set; }

        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "release_status")]
        public int ReleaseStatus { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "release_time")]
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "r_data")]
        public string RData { get; set; }

        /// <summary>
        /// 提交审批人
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "submit_approver")]
        public string SubmitApprover { get; set; }

        /// <summary>
        /// 提交审批人id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "submit_approver_id")]
        public string SubmitApproverId { get; set; }

        /// <summary>
        /// 提交审批时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "submit_approval_time")]
        public DateTime? SubmitApprovalTime { get; set; }

        /// <summary>
        /// 审批人
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "approver")]
        public string Approver { get; set; }

        /// <summary>
        /// 审批人id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "approver_id")]
        public string ApproverId { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "approval_time")]
        public DateTime? ApprovalTime { get; set; }

        /// <summary>
        /// 审批意见
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "approval_opinion")]
        public string ApprovalOpinion { get; set; }

        /// <summary>
        /// 审核状态: 0->待提交; 1->待审核; 2->审核通过; 3->审核驳回
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "approver_status")]
        public int ApproverStatus { get; set; }

    }
}
