﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 展会详情条目
    /// </summary>
    public class ExhibitionDetailResponse
    {
        /// <summary>
        /// 展会Id
        /// </summary>
        public long ExhibitionId { get; set; }

        /// <summary>
        /// 展会编码
        /// </summary>
        public string ExhibitionCode { get; set; }

        /// <summary>
        /// 展会名称
        /// </summary>
        public string ExhibitionName { get; set; }

        /// <summary>
        /// 展会时间
        /// </summary>
        public string ExhibitionTime { get; set; }

        /// <summary>
        /// 展会地址
        /// </summary>
        public string ExhibitionAddress { get; set; }

        /// <summary>
        /// 图册首页图
        /// </summary>
        public FileResponse Thumbnail { get; set; }

        /// <summary>
        /// 图册首页图
        /// </summary>
        public List<FileResponse> DetailPicture { get; set; }
    }
}
