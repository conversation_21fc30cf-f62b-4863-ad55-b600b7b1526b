﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.SaveData
{
    /// <summary>
    /// Banner数据
    /// </summary>
    public class BannerData
    {
        /// <summary>
        /// 轮播时间间隔
        /// </summary>
        [Required(ErrorMessage = "轮播时间间隔必填,请完善")]
        [Range(1, 5, ErrorMessage = "轮播时间间隔参数错误")]
        public int? Interval { get; set; }

        /// <summary>
        /// 中文轮播图
        /// </summary>
        [Required(ErrorMessage = "中文轮播图是必填项")]
        public BannerItem CNImg { get; set; }

        /// <summary>
        /// 英文轮播图
        /// </summary>
        [Required(ErrorMessage = "英文轮播图是必填项")]
        [ArrayRequired(ErrorMessage = "英文轮播图是必填项")]
        [ArrayMaxLength(5, ErrorMessage = "英文轮播图限制最多5张")]
        public BannerItem ENImg { get; set; }
    }

    /// <summary>
    /// Banner条目
    /// </summary>
    public class BannerItem
    {
        /// <summary>
        /// PC端
        /// </summary>
        [Required(ErrorMessage = "PC端图片是必填项")]
        [ArrayRequired(ErrorMessage = "PC端图片是必填项")]
        [ArrayMaxLength(5, ErrorMessage = "PC端图片限制最多5张")]
        public List<BannerSlideShow> PC { get; set; }

        /// <summary>
        /// 手机端
        /// </summary>
        [Required(ErrorMessage = "手机端图片是必填项")]
        [ArrayRequired(ErrorMessage = "手机端图片是必填项")]
        [ArrayMaxLength(5, ErrorMessage = "手机端图片限制最多5张")]
        public List<BannerSlideShow> Mobile { get; set; }
    }

    /// <summary>
    /// 首页轮播图属性
    /// </summary>
    public class BannerSlideShow : SlideShow
    {
        /// <summary>
        /// 跳转地址
        /// </summary>
        [StringLength(200, ErrorMessage = "跳转地址长度不符", MinimumLength = 1)]
        public string LinkUrl { get; set; }
    }
}
