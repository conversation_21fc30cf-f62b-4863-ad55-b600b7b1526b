﻿using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class EditRBACGroupUserRequest
    {
        /// <summary>
        /// 权限分组id
        /// </summary>
        [Required(ErrorMessage ="权限分组id为必填")]
        public long? GroupId { get; set; }
        /// <summary>
        /// 用户id列表
        /// </summary>
        [Required(ErrorMessage = "用户id列表为必填")]
        public List<long> UserIds { get; set; }
    }
}
