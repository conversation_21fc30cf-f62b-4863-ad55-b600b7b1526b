﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class EditExhibitionActivityStatus
    {
        /// <summary>
        /// 展会活动Id
        /// </summary>
        [Required(ErrorMessage = "展会活动Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "展会活动Id参数错误")]
        public long ExhibitionActivityId { get; set; }

        /// <summary>
        /// 活动状态:0->停用;1->启用;
        /// </summary>
        [Required(ErrorMessage = "活动状态必填,请完善")]
        [Range(0, 1, ErrorMessage = "活动状态错误")]
        public int Status { get; set; }
    }
}
