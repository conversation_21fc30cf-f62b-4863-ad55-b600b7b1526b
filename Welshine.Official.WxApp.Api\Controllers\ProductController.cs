﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 精选产品
    /// </summary>
    public class ProductController : BaseApiController
    {
        private readonly IProductService _productService;

        /// <summary>
        /// 构造器注入
        /// </summary>
        /// <param name="productService"></param>
        public ProductController(IProductService productService)
        {
            _productService = productService;
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<WXProductListReponse>>> GetProductPageList()
        {
            List<WXProductListReponse> pageRows = null;
            try
            {
                pageRows = await _productService.WX_GetProductPageList();

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 获取产品详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2006">产品Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ProductDetailResponse>> GetProductById([FromBody] BaseRequest<ProductIdRequest> request)
        {
            ProductDetailResponse result = null;
            try
            {
                result = await _productService.GetProductById(request.Body.ProductId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductById Error {u}", ex.Message);
                return Failure<ProductDetailResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }
    }
}
