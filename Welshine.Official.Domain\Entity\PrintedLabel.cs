using System;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 已打印的标签内容（完整标签JSON内容）
    /// </summary>
    [SqlSugar.SugarTable("ext_printed_label")]
    public class PrintedLabel : BaseBigEntity
    {
        /// <summary>
        /// 微信 OpenId
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "open_id")]
        public string OpenId { get; set; }

        /// <summary>
        /// 已打印的标签完整内容（JSON 字符串） 
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "json_content", Length = 0)]
        public string JsonContent { get; set; }


        /// <summary>
        /// 模板缩略图（Base64）
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "label_thumbnail", ColumnDataType = "longtext")]
        public string LabelThumbnail { get; set; }
    }
}

