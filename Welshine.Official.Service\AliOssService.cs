﻿using Aliyun.OSS;
using <PERSON>yun.OSS.Common;
using Aliyun.OSS.Common.Internal;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using Tea;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Domain.VO.Admin.Response;
using static AlibabaCloud.SDK.Sts20150401.Models.AssumeRoleResponseBody;

namespace Welshine.Official.Service
{
    public class AliOssService : IOssService
    {
        public string Endpoint { get; set; }
        private string bucketName { get; set; } = "welshine-official";

        OssClient client;//= new OssClient(endpoint, accessKeyId, accessKeySecret);
        IOptionsMonitor<AliOssSettings> _aliOss;

        AutoResetEvent _event = new AutoResetEvent(false);

        public AliOssService(IOptionsMonitor<AliOssSettings> aliOss)
        {
            this._aliOss = aliOss;
            if (!string.IsNullOrWhiteSpace(_aliOss.CurrentValue?.bucketName))
            {

                bucketName = _aliOss.CurrentValue.bucketName;
            }
            Endpoint = aliOss.CurrentValue.endpoint;
            client = new OssClient(aliOss.CurrentValue.endpoint, aliOss.CurrentValue.accessKeyId, aliOss.CurrentValue.accessKeySecret);
        }

        /// <summary>
        /// 设置成加密模式
        /// </summary>
        /// <param name="keyName"></param>
        /// <param name="stream"></param>
        /// <returns></returns>
        public void SetEncrypted()
        {
            Endpoint = _aliOss.CurrentValue.privateEndpoint;
            bucketName = _aliOss.CurrentValue.bucketPrivateName;
            client = new OssClient(Endpoint, _aliOss.CurrentValue.accessKeyId, _aliOss.CurrentValue.accessKeySecret);
        }
        #region add object
        public object PutObject(string keyName, Stream stream)
        {
            return PutObject(bucketName, keyName, stream);
        }
        public object PutObject(string keyName, Stream stream, ObjectMetadata metaData)
        {
            return PutObject(bucketName, keyName, stream, metaData);
        }

        public object PutObject(string bucketName, string keyName, Stream stream, ObjectMetadata metaData)
        {
            try
            {
                var result = client.PutObject(bucketName, keyName, stream, metaData);
                return result;
            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                    ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }

        public object PutObject(string bucketName, string keyName, Stream stream)
        {


            try
            {
                var result = client.PutObject(bucketName, keyName, stream);
                return result;
            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                    ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }
        public object SyncAppendObject(string bucketName, string keyName, Stream stream)
        {
            long position = 0;
            ulong initCrc = 0;
            try
            {
                var metadata = client.GetObjectMetadata(bucketName, keyName);
                position = metadata.ContentLength;
                initCrc = ulong.Parse(metadata.Crc64);
            }
            catch (Exception) { }

            try
            {
                var request = new AppendObjectRequest(bucketName, keyName)
                {
                    ObjectMetadata = new ObjectMetadata(),
                    Content = stream,
                    Position = position,
                    InitCrc = initCrc
                };

                var result = client.AppendObject(request);
                position = result.NextAppendPosition;
                initCrc = result.HashCrc64Ecma;
                return result;


            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                    ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }
        public object SyncAppendObject(string keyName, Stream stream)
        {
            return SyncAppendObject(this.bucketName, keyName, stream);
        }

        public bool AsyncAppendObject(string bucketName, string keyName, Stream stream)
        {
            long position = 0;
            try
            {
                var metadata = client.GetObjectMetadata(bucketName, keyName);
                position = metadata.ContentLength;
            }
            catch (Exception) { }

            try
            {
                var request = new AppendObjectRequest(bucketName, keyName)
                {
                    ObjectMetadata = new ObjectMetadata(),
                    Content = stream,
                    Position = position
                };


                const string notice = "Append object succeeded";
                var task = client.BeginAppendObject(request, AppendObjectCallback, notice.Clone());

                _event.WaitOne();
                return task.IsCompleted;
            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                    ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }
        public bool AsyncAppendObject(string keyName, Stream stream)
        {
            return AsyncAppendObject(this.bucketName, keyName, stream);
        }

        public void SyncAppendObjectWithPartSize(string bucketName, string keyName, Stream stream, long? partSize)
        {
            partSize = partSize ?? 1 * 1024 * 1024;
            long position = 0;
            ulong initCrc = 0;
            try
            {
                var metadata = client.GetObjectMetadata(bucketName, keyName);
                position = metadata.ContentLength;
                initCrc = ulong.Parse(metadata.Crc64);
            }
            catch (Exception) { }

            try
            {
                do
                {
                    stream.Seek(position, SeekOrigin.Begin);
                    var request = new AppendObjectRequest(bucketName, keyName)
                    {
                        ObjectMetadata = new ObjectMetadata(),
                        Content = new PartialWrapperStream(stream, partSize.Value),
                        Position = position,
                        InitCrc = initCrc
                    };
                    var result = client.AppendObject(request);
                    position = result.NextAppendPosition;
                    initCrc = result.HashCrc64Ecma;
                    Console.WriteLine("Append object succeeded, next append position:{0}, crc64:{1}", position, initCrc);
                } while (position < stream.Length);
            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                    ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }
        private void AppendObjectCallback(IAsyncResult ar)
        {
            try
            {
                var result = client.EndAppendObject(ar);
                Console.WriteLine("Append object succeeded, next append position:{0}", result.NextAppendPosition);
                Console.WriteLine(ar.AsyncState as string);

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw ex;
            }
            finally
            {
                _event.Set();
            }
        }
        #endregion


        #region del object
        public void DeleteObject(string bucketName, string keyName)
        {
            try
            {
                client.DeleteObject(bucketName, keyName);

                Console.WriteLine("Delete object succeeded");
            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                    ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }

        public void DeleteObjectList(string bucketName, string[] objectnames)
        {
            try
            {
                var listResult = client.ListObjects(bucketName);
                var request = new DeleteObjectsRequest(bucketName, objectnames, false);
                client.DeleteObjects(request);

                Console.WriteLine("Delete objects succeeded");
            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                          ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }
        #endregion


        #region get object
        public string BaseUrl
        {
            get
            {
                return $"https://{bucketName}.{this.Endpoint}/";
            }
        }



        public Stream GetObject(string keyName)
        {
            var request = new GetObjectRequest(bucketName, keyName);

            var result = client.GetObject(request);
            //using (var requestStream = result.Content)
            //{
            //    using (var fs = File.Open("/sample2.png", FileMode.OpenOrCreate))
            //    {
            //        int length = 4 * 1024;
            //        var buf = new byte[length];
            //        do
            //        {
            //            length = requestStream.Read(buf, 0, length);
            //            fs.Write(buf, 0, length);
            //        } while (length != 0);
            //    }
            //}
            return result.Content;
        }
        public void GetBucketAcl(string bucketName, string keyName)
        {
            try
            {
                var result = client.GetObjectAcl(bucketName, keyName);
                Console.WriteLine("Get Object Acl succeeded,Id:{0} Acl:{1} succeeded",
                    result.Owner.Id, result.ACL.ToString());
            }
            catch (OssException ex)
            {
                var msg = string.Format("Failed with error code: {0}; Error info: {1}. \nRequestID:{2}\tHostID:{3}",
                          ex.ErrorCode, ex.Message, ex.RequestId, ex.HostId);

                Console.WriteLine(msg);
                throw new BusinessException(msg, ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Failed with error info: {0}", ex.Message);
                throw ex;
            }
        }

        public void DeleteObject(string keyName)
        {
            DeleteObject(this.bucketName, keyName);
        }






        #endregion

        public AliOssCredentialsResponse GetCredentials(string name)
        {
            var result = new AliOssCredentialsResponse();
            var key = _aliOss.CurrentValue.accessKeyId;
            var secret = _aliOss.CurrentValue.accessKeySecret;
            AlibabaCloud.SDK.Sts20150401.Client client = CreateClient(key,secret);
            AlibabaCloud.SDK.Sts20150401.Models.AssumeRoleRequest assumeRoleRequest = new AlibabaCloud.SDK.Sts20150401.Models.AssumeRoleRequest
            {
                DurationSeconds = _aliOss.CurrentValue.durationSeconds,
                RoleArn = _aliOss.CurrentValue.arn,
                RoleSessionName = name
            };
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime = new AlibabaCloud.TeaUtil.Models.RuntimeOptions();
            try
            {
                // 复制代码运行请自行打印 API 的返回值
                var data = client.AssumeRoleWithOptions(assumeRoleRequest, runtime);
                result.BucketName = _aliOss.CurrentValue.bucketName;
                result.AccessKeySecret = data.Body.Credentials.AccessKeySecret;
                result.AccessKeyId = data.Body.Credentials.AccessKeyId;
                result.SecurityToken = data.Body.Credentials.SecurityToken;
                result.Expiration = data.Body.Credentials.Expiration;
                result.EndPoint = _aliOss.CurrentValue.endpoint;
                return result;
            }
            catch (TeaException error)
            {
                // 如有需要，请打印 error
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
                throw error;
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                // 如有需要，请打印 error
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
                throw error;
            }
        }
        private static AlibabaCloud.SDK.Sts20150401.Client CreateClient(string accessKeyId, string accessKeySecret)
        {
            AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config
            {
                // 必填，您的 AccessKey ID
                AccessKeyId = accessKeyId,
                // 必填，您的 AccessKey Secret
                AccessKeySecret = accessKeySecret,
            };
            // 访问的域名
            config.Endpoint = "sts.cn-hangzhou.aliyuncs.com";
            return new AlibabaCloud.SDK.Sts20150401.Client(config);
        }
    }
}
