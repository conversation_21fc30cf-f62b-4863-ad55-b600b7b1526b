﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 分类产品列表条目
    /// </summary>
    public class CategoryProductListResponse
    {
        /// <summary>
        /// 分类商品Id
        /// </summary>
        public long CategoryProductId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public string ProductImg { get; set; }

        /// <summary>
        /// 发布状态: 0->未发布; 1->已发布;
        /// </summary>
        public int ReleaseStatus { get; set;}

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string UpdatedUser { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? UpdatedTime { get; set;}
    }
}
