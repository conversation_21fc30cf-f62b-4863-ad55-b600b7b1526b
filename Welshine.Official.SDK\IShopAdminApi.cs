﻿using Refit;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.SDK.Model;

namespace Welshine.Official.SDK
{
    [Headers(new string[] { "Content-Type: application/json" })]
    public interface IShopAdminApi
    {
        [Post("/user/login")]
        Task<JsonResult<LoginResponse>> Login([Body] LoginRequest request);
        [Post("/user/freshToken")]
        Task<JsonResult<LoginResponse>> FreshToken([Header("Authorization")] string authorization);
        [Post("/user/logOut")]
        Task<JsonResult<string>> LogOut([Header("Authorization")] string authorization);
        [Post("/user/alterPassword")]
        Task<JsonResult<string>> AlterPassword([Header("Authorization")] string authorization, [Body] AlterPasswordRequest request);

        [Post("/admin/rbac/rbacValidate")]
        Task<JsonResult<bool>> RBACValidate([Header("Authorization")] string authorization, [Body] RBACValidateRequest request);

    }
}
