﻿using Welshine.Official.Core.RestfulApi.Base;
using System;

namespace Welshine.Official.Core.RestfulApi.Helper
{
    /// <summary>
    /// WebAPI Response Help Class
    /// </summary>
    public class WebApiResponseHelp
    {
        public static BaseResponse<T> Success<T>(string msg, T data = default(T))
        {
            return Result<T>(ApiStatus.OK, msg, data);
        }

        public static BaseResponse<T> Failure<T>(string msg, T data = default(T))
        {
            return Result<T>(ApiStatus.InternalServerError, msg, data);
        }

        public static BaseResponse<T> NoFind<T>(string msg, T data = default(T))
        {
            return Result<T>(ApiStatus.NotFound, msg, data);
        }

        public static BaseResponse<T> Result<T>(ApiStatus code, string msg, T data = default(T))
        {
            return new BaseResponse<T>()
            {
                Head = { Code = ((int)code).ToString(), CallTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Message = msg },
                Result = data
            };
        }

        public static BaseResponse Success(string msg, dynamic data = null)
        {
            return Result(ApiStatus.OK, msg, data);
        }

        public static BaseResponse Failure(string msg, dynamic data = null)
        {
            return Result(ApiStatus.InternalServerError, msg, data);
        }

        public static BaseResponse NoFind(string msg, dynamic data = null)
        {
            return Result(ApiStatus.NotFound, msg, data);
        }

        public static BaseResponse Result(ApiStatus code, string msg, dynamic data = null)
        {
            return Result(code.GetHashCode(), msg, data);
        }

        public static BaseResponse Result(int code, string msg, dynamic data = null)
        {
            return new BaseResponse()
            {
                Head = { Code = (code).ToString(), CallTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Message = msg }
            };
        }

        public static BaseResponse<T> Result<T>(int code, string msg, dynamic data = null)
        {
            return new BaseResponse<T>()
            {
                Head = { Code = (code).ToString(), CallTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Message = msg },
                Result = data

            };
        }

        public static BaseResponse Result(bool isSuccess, string msg)
        {
            return isSuccess ? Success(msg) : Failure(msg);
        }
    }
}
