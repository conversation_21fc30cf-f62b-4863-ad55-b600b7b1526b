﻿using DTHY.Core.Repository;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 分类产品规格仓储实现
    /// </summary>
    public class CategoryProductIntroduceRepository : BaseRepository<CategoryProductIntroduce>, ICategoryProductIntroduceRepository
    {
        private readonly IFileRepository _fileRepository;

        public CategoryProductIntroduceRepository(IFileRepository fileRepository)
        {
            _fileRepository = fileRepository;
        }

        public async Task<List<ProductIntroduce>> GetList(long categoryProductId)
        {
            return await DbScoped.SugarScope.Queryable<CategoryProduct, CategoryProductIntroduce>
                ((f, fr) => f.Id == fr.CategoryProductId && f.IsDeleted == false && fr.IsDeleted == false)
                .Where((f, fr) => f.Id == categoryProductId)
                .Select<ProductIntroduce>((f, fr) => new ProductIntroduce()
                {
                    IntroduceModel = fr.IntroduceModel,
                    IntroduceSize = fr.IntroduceSize,
                    IntroduceCapacity = fr.IntroduceCapacity,
                    imgUrl = fr.ImgUrl
                })
                .ToListAsync();
        }

    }
}
