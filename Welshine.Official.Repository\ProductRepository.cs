﻿using DTHY.Core.Repository;
using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.SNGeneration;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 产品仓储实现
    /// </summary>
    public class ProductRepository : BaseRepository<Product>, IProductRepository
    {
        private readonly IFileRepository _fileRepository;

        public ProductRepository(IFileRepository fileRepository)
        {
            _fileRepository = fileRepository;
        }

        /// <summary>
        /// 判断产品名称是否存在
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <returns></returns>
        public async Task<bool> ExistsProductName(string productName)
        {
            return await DbScoped.SugarScope.Queryable<Product>().AnyAsync(x => x.ProductName == productName && !x.IsDeleted);
        }

        /// <summary>
        /// 判断产品名称是否存在
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="productId">产品Id</param>
        /// <returns></returns>
        public async Task<bool> ExistsProductName(string productName, long productId)
        {
            return await DbScoped.SugarScope.Queryable<Product>().AnyAsync(x => x.ProductName == productName && x.Id != productId && !x.IsDeleted);
        }

        /// <summary>
        /// 获取数据总数
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetCount()
        {
            return await DbScoped.SugarScope.Queryable<Product>().CountAsync(x => !x.IsDeleted);
        }

        /// <summary>
        /// 添加产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> AddProduct(Product product, string thumbnail, List<string> detailPicture)
        {
            product.ProductCode = await CodeHelper.GetCode("TCFL");

            var id = await DbScoped.SugarScope.Insertable(product).ExecuteReturnBigIdentityAsync();
            if (id > 0)
            {
                #region 略缩图
                if (!string.IsNullOrWhiteSpace(thumbnail))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = thumbnail,
                        ObjectId = id,
                        TableEnum = EnumRelationType.ProductThumbnail,
                        CreatedBy = product.UpdatedBy,
                        CreatorId = product.CreatorId,
                        UpdatedBy = product.UpdatedBy,
                        ModifierId = product.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                #region 详情图
                foreach (var item in detailPicture)
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = item,
                        ObjectId = id,
                        TableEnum = EnumRelationType.ProductDetailPicture,
                        CreatedBy = product.UpdatedBy,
                        CreatorId = product.CreatorId,
                        UpdatedBy = product.UpdatedBy,
                        ModifierId = product.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取产品信息
        /// </summary>
        /// <param name="ProductId">产品Id</param>
        /// <returns></returns>
        public async Task<Product> GetProduct(long ProductId)
        {
            return await DbScoped.SugarScope.Queryable<Product>().FirstAsync(x => x.Id == ProductId && !x.IsDeleted);
        }

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> EditProduct(Product product, string thumbnail, List<string> detailPicture)
        {
            var num = await DbScoped.SugarScope.Updateable(product).ExecuteCommandAsync();
            if (num > 0)
            {
                //清空关联文件
                await DbScoped.SugarScope.Updateable<FilesRelation>()
                    .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = product.ModifierId, UpdatedBy = product.UpdatedBy, UpdatedTime = product.UpdatedTime })
                    .Where(x => x.ObjectId == product.Id && (x.TableEnum == EnumRelationType.ProductDetailPicture || x.TableEnum == EnumRelationType.ProductThumbnail))
                    .ExecuteCommandAsync();

                #region 略缩图
                if (!string.IsNullOrWhiteSpace(thumbnail))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = thumbnail,
                        ObjectId = product.Id,
                        TableEnum = EnumRelationType.ProductThumbnail,
                        CreatorId = product.CreatorId,
                        CreatedBy = product.UpdatedBy,
                        ModifierId = product.ModifierId,
                        UpdatedBy = product.UpdatedBy
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                #region 详情图
                foreach (var item in detailPicture)
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = item,
                        ObjectId = product.Id,
                        TableEnum = EnumRelationType.ProductDetailPicture,
                        CreatorId = product.CreatorId,
                        CreatedBy = product.UpdatedBy,
                        ModifierId = product.ModifierId,
                        UpdatedBy = product.UpdatedBy
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                return true;
            }
            return false;
        }

        /// <summary>
        /// 删除产品分类
        /// </summary>
        /// <param name="productId">产品信息</param>
        /// <returns></returns>
        public async Task<bool> DeleteProduct(Product product)
        {
            //删除产品
            await DbScoped.SugarScope.Updateable<Product>()
                .SetColumns(x => new Product() { IsDeleted = true, ModifierId = product.ModifierId, UpdatedBy = product.UpdatedBy, UpdatedTime = product.UpdatedTime })
                .Where(x => x.Id == product.Id)
                .ExecuteCommandAsync();

            //清空产品关联文件
            await DbScoped.SugarScope.Updateable<FilesRelation>()
                .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = product.ModifierId, UpdatedBy = product.UpdatedBy, UpdatedTime = product.UpdatedTime })
                .Where(x => x.ObjectId == product.Id && (x.TableEnum == EnumRelationType.ProductDetailPicture || x.TableEnum == EnumRelationType.ProductThumbnail))
                .ExecuteCommandAsync();

            return true;
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <param name="orderBy">排序</param>
        /// <returns></returns>
        public async Task<PageRows<ProductListReponse>> GetProductPageList(string productName, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<ProductListReponse> result = new PageRows<ProductListReponse>();

            result.Data = await DbScoped.SugarScope.Queryable<Product>()
                .Where(x => !x.IsDeleted)
                .WhereIF(!string.IsNullOrWhiteSpace(productName), x => x.ProductName.Contains(productName))
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .Select(x => new ProductListReponse()
                {
                    ProductId = x.Id,
                    ProductCode = x.ProductCode,
                    ProductName = x.ProductName,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedTime = x.UpdatedTime
                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            result.Total = totalNumber;

            if (result.Data == null || result.Data.Count == 0)
            {
                return result;
            }

            var ids = result.Data.Select(x => x.ProductId).ToList();

            var files = await _fileRepository.GetFileList(ids, EnumRelationType.ProductThumbnail);

            foreach (var item in result.Data)
            {
                var file = files.Where(x => x.ObjectId == item.ProductId).FirstOrDefault();
                if (file != null)
                {
                    item.Thumbnail = file.Url;
                }
            }

            return result;
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<List<WXProductListReponse>> WX_GetProductPageList()
        {
            List<WXProductListReponse> result = new List<WXProductListReponse>();

            result = await DbScoped.SugarScope.Queryable<Product>()
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .Select(x => new WXProductListReponse()
                {
                    ProductId = x.Id,
                    ProductName = x.ProductName,
                })
                .ToListAsync();

            if (result == null || result.Count == 0)
            {
                return result;
            }

            var ids = result.Select(x => x.ProductId).ToList();

            var files = await _fileRepository.GetFileList(ids, EnumRelationType.ProductThumbnail);

            foreach (var item in result)
            {
                var file = files.Where(x => x.ObjectId == item.ProductId).FirstOrDefault();
                if (file != null)
                {
                    item.Thumbnail = file.Url;
                }
            }

            return result;
        }
    }
}
