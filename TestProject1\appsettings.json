{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "sqlsuger": {"DbType": 0, "InitKeyType": 1, "IsAutoCloseConnection": true, "ConnectionString": "Data Source=***********;port=3306; Initial Catalog=official_db;uid=root; pwd=*******;Convert Zero Datetime=True;Allow Zero Datetime=True"}, "ConnectionStrings": {"RedisStr": "***********,password=Red<PERSON>@123456.,poolsize=10,ssl=false,writeBuffer=10240,prefix=", "BaseIdentityApiUrl": "http://***********:9001", "ShopAdminApiUrl": "http://***********:9004"}, "Serilog": {"WriteTo": [{"Name": "RollingFile", "Args": {"pathFormat": "logs\\{Date}.txt", "RestrictedToMinimumLevel": "Warning"}}, {"Name": "<PERSON><PERSON><PERSON>"}], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Information"}}}, "AliOss": {"endpoint": "oss-cn-guangzhou.aliyuncs.com", "bucketName": "welshine-official", "accessKeyId": "LTAI5t6xCiGVY4DP1DWthyHr", "accessKeySecret": "******************************", "allowType": ".png,.jpg,.txt,.pdf,.jpeg,.image", "mLength": 200}}