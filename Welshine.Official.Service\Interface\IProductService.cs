﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 产品服务接口
    /// </summary>
    public interface IProductService
    {
        /// <summary>
        /// 添加产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        Task<bool> AddProduct(Product product, string thumbnail, List<string> detailPicture);

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        Task<bool> EditProduct(Product product, string thumbnail, List<string> detailPicture);

        /// <summary>
        /// 获取产品详情
        /// </summary>
        /// <param name="productId">产品Id</param>
        /// <returns></returns>
        Task<ProductDetailResponse> GetProductById(long productId);

        /// <summary>
        /// 删除产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <returns></returns>
        Task<bool> DeleteProduct(Product product);

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<ProductListReponse>> GetProductPageList(string productName, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<List<WXProductListReponse>> WX_GetProductPageList();
    }
}
