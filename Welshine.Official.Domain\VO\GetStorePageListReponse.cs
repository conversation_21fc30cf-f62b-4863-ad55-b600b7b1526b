﻿using System;
using System.Collections.Generic;
using System.Text;
using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.VO
{
    /// <summary>
    /// 门店列表
    /// </summary>
    public class GetStorePageListReponse
    {
        /// <summary>
        /// 主键
        /// </summary>
        public long StoreId { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; } = "system";

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdatedBy { get; set; } = "system";
        /// <summary>
        /// 门店编码
        /// </summary>
        public string StoreCode { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// 门店地址
        /// </summary>
        public string StoreAddress { get; set; }

        /// <summary>
        /// 门店开始营业时间
        /// </summary>
        public string StoreStartTime { get; set; }

        /// <summary>
        /// 门店结束营业时间
        /// </summary>
        public string StoreEndTime { get; set; }

        /// <summary>
        /// 门店状态
        /// </summary>
        public EnumStoreStatus StoreStatus { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        public string StorePhone { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        public string StoreFax { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string StoreEmail { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public decimal StoreLongitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        public decimal StoreLatitude { get; set; }

        /// <summary>
        /// 略缩图
        /// </summary>
        public string StoreThumbnail { get; set; }

        /// <summary>
        /// 联系图
        /// </summary>
        public string StoreRelation { get; set; }
        /// <summary>
        /// 是否首页
        /// </summary>
        public bool StoreShowHomepage { get; set; }
        /// <summary>
        /// 略缩图地址
        /// </summary>
        public string StoreThumbnailUrl { get; set; }

        /// <summary>
        /// 联系图地址
        /// </summary>
        public string StoreRelationUrl { get; set; }
      

    }
}
