﻿using System;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class ExhibitionActivityDetailResponse
    {
        /// <summary>
        /// 会展活动id
        /// </summary>
        public long ExhibitionActivityId { get; set; }

        /// <summary>
        /// 活动编号
        /// </summary>
        public string ExhibitionActivityCode { get; set; }

        /// <summary>
        /// 活动名称
        /// </summary>
        public string ExhibitionActivityName { get; set; }

        /// <summary>
        /// 活动开始时间
        /// </summary>
        public DateTime ExhibitionActivityStartTime { get; set; }

        /// <summary>
        /// 活动结束时间
        /// </summary>
        public DateTime ExhibitionActivityEndTime { get; set; }

        /// <summary>
        /// 活动地点
        /// </summary>
        public string ExhibitionActivityAddress { get; set; }

        /// <summary>
        /// 活动状态:0->停用;1->启用;
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string UpdatedBy { get; set; }

        /// <summary>
        /// 活动入口图
        /// </summary>
        public string EntryPicture { get; set; }

        /// <summary>
        /// 活动入口图地址
        /// </summary>
        public string EntryPictureUrl { get; set; }

        /// <summary>
        /// 活动参与图
        /// </summary>
        public string ParticipationPicture { get; set; }

        /// <summary>
        /// 活动参与图地址
        /// </summary>
        public string ParticipationPictureUrl { get; set; }

        /// <summary>
        /// 活动券码图
        /// </summary>
        public string CodePicture { get; set; }

        /// <summary>
        /// 活动券码图地址
        /// </summary>
        public string CodePictureUrl { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public decimal AddressLongitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        public decimal AddressLatitude { get; set; }

        /// <summary>
        /// 现场客户数
        /// </summary>
        public int LocaleCount { get; set; }

        /// <summary>
        /// 非现场客户数
        /// </summary>
        public int OffSiteLocaleCount { get; set; }
    }
}
