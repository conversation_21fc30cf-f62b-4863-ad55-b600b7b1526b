﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 展会服务实现
    /// </summary>
    public class ExhibitionService : IExhibitionService
    {
        private readonly IExhibitionRepository _exhibitionRepository;
        private readonly IFileRepository _fileRepository;

        public ExhibitionService(IExhibitionRepository exhibitionRepository, IFileRepository fileRepository)
        {
            _exhibitionRepository = exhibitionRepository;
            _fileRepository = fileRepository;
        }

        /// <summary>
        /// 添加展会
        /// </summary>
        /// <param name="exhibition">展会信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> AddExhibition(Exhibition exhibition, string thumbnail, List<string> detailPicture)
        {
            await AddExhibitionVaild(exhibition.ExhibitionName, thumbnail, detailPicture);

            var result = await _exhibitionRepository.AddExhibition(exhibition, thumbnail, detailPicture);

            return result;
        }

        /// <summary>
        /// 添加展会校验
        /// </summary>
        /// <param name="exhibitionName">展会名称</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task AddExhibitionVaild(string exhibitionName, string thumbnail, List<string> detailPicture)
        {
            //产品名称校验
            var result = await _exhibitionRepository.ExistsExhibitionName(exhibitionName);
            if (result)
            {
                throw new BusinessException(ErrorCode.ExhibitionNameError.ToDescriptionName(), ErrorCode.ExhibitionNameError.GetHashCode());
            }

            //图片校验
            var fileList = await _fileRepository.GetList(new List<string>() { thumbnail }.Union(detailPicture).ToList());
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //略缩图校验
            result = await _fileRepository.Exists(thumbnail);
            if (!result)
            {
                throw new BusinessException(ErrorCode.ExhibitionThumbnailNotFind.ToDescriptionName(), ErrorCode.ExhibitionThumbnailNotFind.GetHashCode());
            }

            //详情图校验
            if (detailPicture.Count > 8)
            {
                throw new BusinessException(ErrorCode.ExhibitionDetailPictureLimitError.ToDescriptionName(), ErrorCode.ExhibitionDetailPictureLimitError.GetHashCode());
            }
            foreach (var item in detailPicture)
            {
                result = await _fileRepository.Exists(item);
                if (!result)
                {
                    throw new BusinessException(ErrorCode.ExhibitionDetailPictureNotFind.ToDescriptionName(), ErrorCode.ExhibitionDetailPictureNotFind.GetHashCode());
                }
            }
        }

        /// <summary>
        /// 修改展会
        /// </summary>
        /// <param name="exhibition">展会信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> EditExhibition(Exhibition exhibition, string thumbnail, List<string> detailPicture)
        {
            var result = false;

            await EditExhibitionVaild(exhibition.ExhibitionName, exhibition.Id, thumbnail, detailPicture);

            var entity = await _exhibitionRepository.GetExhibition(exhibition.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionNotFind.ToDescriptionName(), ErrorCode.ExhibitionNotFind.GetHashCode());
            }

            entity.ExhibitionName = exhibition.ExhibitionName;
            entity.ExhibitionAddress = exhibition.ExhibitionAddress;
            entity.ExhibitionTime = exhibition.ExhibitionTime;
            entity.UpdatedBy = exhibition.UpdatedBy;
            entity.UpdatedTime = DateTime.Now;

            await _exhibitionRepository.EditExhibition(entity, thumbnail, detailPicture);

            return result;
        }

        /// <summary>
        /// 修改展会校验
        /// </summary>
        /// <param name="exhibitionName">展会名称</param>
        /// <param name="exhibitionId">展会Id</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task EditExhibitionVaild(string exhibitionName, long exhibitionId, string thumbnail, List<string> detailPicture)
        {
            //展会名称校验
            var result = await _exhibitionRepository.ExistsExhibitionName(exhibitionName, exhibitionId);
            if (result)
            {
                throw new BusinessException(ErrorCode.ExhibitionNameError.ToDescriptionName(), ErrorCode.ExhibitionNameError.GetHashCode());
            }

            //图片校验
            var fileList = await _fileRepository.GetList(new List<string>() { thumbnail }.Union(detailPicture).ToList());
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }

            //略缩图校验
            result = await _fileRepository.Exists(thumbnail);
            if (!result)
            {
                throw new BusinessException(ErrorCode.ExhibitionThumbnailNotFind.ToDescriptionName(), ErrorCode.ExhibitionThumbnailNotFind.GetHashCode());
            }

            //详情图校验
            if (detailPicture.Count > 8)
            {
                throw new BusinessException(ErrorCode.ExhibitionDetailPictureLimitError.ToDescriptionName(), ErrorCode.ExhibitionDetailPictureLimitError.GetHashCode());
            }
            foreach (var item in detailPicture)
            {
                result = await _fileRepository.Exists(item);
                if (!result)
                {
                    throw new BusinessException(ErrorCode.ExhibitionDetailPictureNotFind.ToDescriptionName(), ErrorCode.ExhibitionDetailPictureNotFind.GetHashCode());
                }
            }
        }

        /// <summary>
        /// 获取展会
        /// </summary>
        /// <param name="exhibitionId">展会Id</param>
        /// <returns></returns>
        public async Task<ExhibitionDetailResponse> GetExhibitionById(long exhibitionId)
        {
            var entity = await _exhibitionRepository.GetExhibition(exhibitionId);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionNotFind.ToDescriptionName(), ErrorCode.ExhibitionNotFind.GetHashCode());
            }

            ExhibitionDetailResponse result = new ExhibitionDetailResponse()
            {
                ExhibitionId = entity.Id,
                ExhibitionName = entity.ExhibitionName,
                ExhibitionCode = entity.ExhibitionCode,
                ExhibitionTime = entity.ExhibitionTime,
                ExhibitionAddress = entity.ExhibitionAddress
            };

            var files = await _fileRepository.GetFileList(new List<long> { exhibitionId });

            var thumbnail = files.Where(x => x.TableEnum == EnumRelationType.ExhibitionThumbnail).FirstOrDefault();
            if (thumbnail != null)
            {
                result.Thumbnail = new Domain.VO.FileResponse() { FileId = thumbnail.FileId, FileName = thumbnail.Name, Url = thumbnail.Url };
            }

            var dtetailPicture = files.Where(x => x.TableEnum == EnumRelationType.ExhibitionDetailPicture).OrderBy(x => x.CreatedTime).ToList();
            List<FileResponse> pictureList = new List<FileResponse>();
            foreach (var item in dtetailPicture)
            {
                FileResponse file = new FileResponse();
                file.FileId = item.FileId;
                file.FileName = item.Name;
                file.Url = item.Url;

                pictureList.Add(file);
            }
            result.DetailPicture = pictureList;

            return result;
        }

        /// <summary>
        /// 删除展会
        /// </summary>
        /// <param name="exhibition">展会信息</param>
        /// <returns></returns>
        public async Task<bool> DeleteExhibition(Exhibition exhibition)
        {
            var entity = await _exhibitionRepository.GetExhibition(exhibition.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionNotFind.ToDescriptionName(), ErrorCode.ExhibitionNotFind.GetHashCode());
            }

            return await _exhibitionRepository.DeleteExhibition(exhibition);
        }

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <param name="exhibitionName">展会名称</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<ExhibitionListReponse>> GetExhibitionPageList(string exhibitionName, int pageIndex = 1, int pageSize = 10)
        {
            return await _exhibitionRepository.GetExhibitionPageList(exhibitionName, pageIndex, pageSize);
        }

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<WXExhibitionListReponse>> WX_GetExhibitionPageList(int pageIndex = 1, int pageSize = 10)
        {
            return await _exhibitionRepository.WX_GetExhibitionPageList(pageIndex, pageSize);
        }

        /// <summary>
        /// 获取展会
        /// </summary>
        /// <param name="exhibitionId">展会Id</param>
        /// <returns></returns>
        public async Task<WXExhibitionDetailResponse> WX_GetExhibitionById(long exhibitionId)
        {
            var entity = await _exhibitionRepository.GetExhibition(exhibitionId);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionNotFind.ToDescriptionName(), ErrorCode.ExhibitionNotFind.GetHashCode());
            }

            WXExhibitionDetailResponse result = new WXExhibitionDetailResponse()
            {
                ExhibitionId = entity.Id,
                ExhibitionName = entity.ExhibitionName,
                ExhibitionCode = entity.ExhibitionCode,
                ExhibitionTime = entity.ExhibitionTime,
                ExhibitionAddress = entity.ExhibitionAddress
            };

            var detailPicture = await _fileRepository.GetFileList(new List<long> { exhibitionId },EnumRelationType.ExhibitionDetailPicture);

            List<FileResponse> pictureList = new List<FileResponse>();
            foreach (var item in detailPicture)
            {
                FileResponse file = new FileResponse();
                file.FileId = item.FileId;
                file.FileName = item.Name;
                file.Url = item.Url;

                pictureList.Add(file);
            }
            result.DetailPicture = pictureList;

            return result;
        }
    }
}
