﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using System.Collections.Generic;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class GetCategoryProductListOrderSortResponse
    {
        /// <summary>
        /// 英文
        /// </summary>
        public List<CategoryProductResponse> EnProductList { get; set; }

        /// <summary>
        /// 中文
        /// </summary>
        public List<CategoryProductResponse> CnProductList { get; set; }

    }

    public class CategoryProductResponse
    {
        /// <summary>
        /// 商品Id
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        /// 分类Id
        /// </summary>
        public long CategoryId { get; set; }

        /// <summary>
        /// 语言: 0->英文; 1->中文
        /// </summary>
        public int ProductLanguage { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public string ProductImg { get; set; }
    }
}
