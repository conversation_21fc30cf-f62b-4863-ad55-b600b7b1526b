﻿using Microsoft.Extensions.DependencyInjection;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Xml.XPath;

namespace Welshine.Official.Core.Extensions
{
    public static class SwaggerXmlLoaderExtensions
    {
        /// <summary>
        /// 动态加载私仓SDK生成swagger的xml注释文件
        /// 需要将SDK的xml文件生成操作修改为嵌入的资源
        /// </summary>
        /// <param name="swaggerGenOptions"></param>
        public static void DTHYSwaggerXmlLoad(this SwaggerGenOptions swaggerGenOptions)
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies().Where(x=>x.GetName().Name.Contains("SDK")).ToList();
            foreach (var assembly in assemblies)
            {
                var assemblyName = string.Format("{0}.{0}.xml", assembly.GetName().Name);
                var stream = assembly.GetManifestResourceStream(assemblyName);
                if(stream != null)
                {
                    var xdoc = new XPathDocument(stream);
                    swaggerGenOptions.IncludeXmlComments(() => xdoc, true);
                }
            }
        }
    }
}
