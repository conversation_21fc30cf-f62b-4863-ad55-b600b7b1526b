﻿
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.RestfulApi.Helper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Welshine.Official.Core.Exceptions;
using Serilog;

namespace Welshine.Official.Core.Attributes
{
    /// <summary>
    /// 异常处理器
    /// </summary>
    public class WebApiExceptionAttribute : ExceptionFilterAttribute
    {
        /// <summary>
        /// 重写异常处理，统一处理
        /// </summary>
        /// <param name="actionExecutedContext"></param>
        public override void OnException(ExceptionContext actionExecutedContext)
        {
            //获取异常对象
            System.Exception ex = actionExecutedContext.Exception;

            if (ex is BusinessException be)
            {
                if (be.GetType().IsGenericType)
                {
                    actionExecutedContext.Result = new JsonResult(WebApiResponseHelp.Result(be.Code, be.Message));
                }
                else
                {
                    actionExecutedContext.Result = new JsonResult(WebApiResponseHelp.Result(be.Code, be.Message));
                }

                actionExecutedContext.ExceptionHandled = true;
                //base.OnException(actionExecutedContext);
            }
            else if (ex.Message.Contains("IDX10223"))
            {
                //认证鉴权中间抛出错误，另外方法是升级
                actionExecutedContext.Result = new UnauthorizedObjectResult(ApiStatus.Unauthorized.GetHashCode());
                actionExecutedContext.ExceptionHandled = true;
            }
            else
            {
                Log.Logger.Error(ex, ex.ToString());
#if Debug
            actionExecutedContext.Result = new JsonResult(WebApiResponseHelp.Failure(ex.Message+ex.StackTrace));
#else
                actionExecutedContext.Result = new JsonResult(WebApiResponseHelp.Failure(ex.Message + ex.StackTrace));
                //actionExecutedContext.Result = new JsonResult(WebApiResponseHelp.Failure("请求失败，系统异常"));
#endif
                actionExecutedContext.ExceptionHandled = true;

            }


            base.OnException(actionExecutedContext);
        }
    }
}
