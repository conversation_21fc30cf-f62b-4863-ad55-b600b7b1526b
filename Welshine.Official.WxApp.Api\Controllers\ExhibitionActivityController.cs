﻿using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Ocsp;
using Serilog;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Request;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    public class ExhibitionActivityController : BaseApiController
    {
        IExhibitionActivityService _exhibitionActivityService;
        IExhibitionActivityParticipantService _exhibitionActivityParticipantService;

        IWxService _wxService;

        public ExhibitionActivityController(IExhibitionActivityService exhibitionActivityService, IExhibitionActivityParticipantService exhibitionActivityParticipantService, IWxService wxService)
        {
            _exhibitionActivityService = exhibitionActivityService;
            _exhibitionActivityParticipantService = exhibitionActivityParticipantService;
            _wxService = wxService;
        }

        /// <summary>
        /// 展会活动列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<ExhibitionActivityResponse>>> GetExhibitionActivityPageList([FromBody] RequestPageModel<GetExhibitionActivityPageListRequest> request)
        {
            PageRows<ExhibitionActivityResponse> pageRows = new PageRows<ExhibitionActivityResponse>() { Data = new System.Collections.Generic.List<ExhibitionActivityResponse>() };
            try
            {
                DateTime? dt1 = request.RequestParams.ActivityTimeScope == null ? null : request.RequestParams.ActivityTimeScope.From;
                DateTime? dt2 = request.RequestParams.ActivityTimeScope == null ? null : request.RequestParams.ActivityTimeScope.To;
                var orderType = OrderByType.Desc;
                if (request.SortType == SortType.Asc)
                {
                    orderType = OrderByType.Asc;
                }
                var pageRowList = await _exhibitionActivityService.GetExhibitionActivityPageList(request.PageIndex, request.PageSize, request.OrderFile, orderType, request.RequestParams.ExhibitionActivityName, dt1, dt2, request.RequestParams.Status);
                pageRows.Total = pageRowList.Total;
                pageRows.Data = _mapper.Map<List<ExhibitionActivityResponse>>(pageRowList.Data);

                return Success("查询成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionActivityPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 展会活动详情
        /// </summary>
        /// <response code="2252">展会活动不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ExhibitionActivityDetailResponse>> GetExhibitionActivityDetail([FromBody] BaseRequest<ExhibitionActivityIdRequest> request)
        {
            ExhibitionActivityDetailResponse result = null;
            try
            {
                result = await _exhibitionActivityService.GetExhibitionActivityDetail(request.Body.ExhibitionActivityId);
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionActivityDetail " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 获取参与人员信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ExhibitionActivityParticipant>> GetParticipantCheckInInfo([FromBody] BaseRequest<ParticipanttIsCheckInRequest> request)
        {
            ExhibitionActivityParticipant result = null;
            try
            {
                string openId = await _wxService.GetOpenId(request.Body.Code);
                result = await _exhibitionActivityParticipantService.GetParticipantCheckInInfo(request.Body.ExhibitionActivityId, openId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetParticipantCheckInInfo " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 展会活动参与人员登记
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2257">省份不存在</response>
        /// <response code="2258">市不存在</response>
        /// <response code="2259">活动已结束</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> ParticipantCheckIn([FromBody] BaseRequest<ParticipantCheckInRequest> request)
        {
            string result = "";
            try
            {
                string openId = await _wxService.GetOpenId(request.Body.Code);
                result = await _exhibitionActivityParticipantService.ParticipantCheckIn(request.Body.ExhibitionActivityId, request.Body.Name, request.Body.Mobile, request.Body.type, request.Body.Company, request.Body.ProvinceId, request.Body.CityId, request.Body.Longitude, request.Body.Latitude, openId);
                return Success("登记成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("ParticipantCheckIn " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 获取微信授权手机号
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> GetWeCahtAuthPhone([FromBody] BaseRequest<WxAuthPhoneRequet> request)
        {
            string result = null;
            try
            {
                result = await _wxService.GetWxPhone(request.Body.Code);
                if (string.IsNullOrWhiteSpace(result))
                {
                    return Failure(ErrorCode.GetWxPhoneError, result);
                }
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetWeCahtAuthPhone ex:" + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail, result);
            }
        }
    }
}
