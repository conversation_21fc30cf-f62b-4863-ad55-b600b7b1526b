﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;
using Welshine.Official.Domain.VO.Admin.Request;

namespace Welshine.Official.Domain.VO.Admin.SaveData
{
    /// <summary>
    /// 底部横幅数据
    /// </summary>
    public class BottomData
    {
        /// <summary>
        /// 中文
        /// </summary>
        [Required(ErrorMessage = "中文是必填项")]
        public BottomCNItem CNInfo { get; set; }

        /// <summary>
        /// 英文
        /// </summary>
        [Required(ErrorMessage = "英文是必填项")]
        public BottomENItem ENInfo { get; set; }
    }

    public class BottomCNItem
    {
        /// <summary>
        /// 名称
        /// </summary>
        [Required(ErrorMessage = "名称是必填项")]
        [StringLength(40, ErrorMessage = "名称长度不符", MinimumLength = 1)]
        public string CompanyIntroduction { get; set; }

        /// <summary>
        /// 电话
        /// </summary> 
        [Required(ErrorMessage = "电话必填,请完善")]
        [ArrayRequired(ErrorMessage = "电话必填,请完善")]
        [StringArray("^[0-9_\\-@&=`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", 15, ErrorMessage = "电话格式错误")]
        [ArrayMaxLength(3, ErrorMessage = "手机最多三个")]
        public List<string> StorePhoneList { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        [StringLength(20, ErrorMessage = "传真长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "传真格式错误")]
        public string StoreFax { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(30, ErrorMessage = "邮箱长度不符", MinimumLength = 1)]
        [RegularExpression("^[A-Za-z0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "邮箱格式错误")]
        public string StoreEmail { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(200, ErrorMessage = "备注长度不符", MinimumLength = 1)]
        public string ReMark { get; set; }

        /// <summary>
        /// PC端横幅照片
        /// </summary>
        [Required(ErrorMessage = "PC端横幅照片必填,请完善")]
        public string PcBottomImg { get; set; }

        /// <summary>
        /// 移动端横幅照片
        /// </summary>
        [Required(ErrorMessage = "移动端横幅照片必填,请完善")]
        public string MobileBottomImg { get; set; }

        /// <summary>
        /// 二维码
        /// </summary>
        public string QRCodeImg { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [Required(ErrorMessage = "地址是必填项")]
        [StringLength(200, ErrorMessage = "地址长度不符", MinimumLength = 1)]
        public string StoreAddress { get; set; }
    }

    public class BottomENItem
    {
        /// <summary>
        /// 名称
        /// </summary>
        [Required(ErrorMessage = "名称是必填项")]
        [StringLength(100, ErrorMessage = "名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "名称不允许有中文")]
        public string CompanyIntroduction { get; set; }

        /// <summary>
        /// 电话
        /// </summary> 
        [Required(ErrorMessage = "电话必填,请完善")]
        [ArrayRequired(ErrorMessage = "电话必填,请完善")]
        [StringArray("^[0-9_\\-@&=`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", 15, ErrorMessage = "电话格式错误")]
        [ArrayMaxLength(3, ErrorMessage = "手机最多三个")]
        public List<string> StorePhoneList { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        [StringLength(20, ErrorMessage = "传真长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "传真格式错误")]
        public string StoreFax { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(30, ErrorMessage = "邮箱长度不符", MinimumLength = 1)]
        [RegularExpression("^[A-Za-z0-9_\\-@&=|`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "邮箱格式错误")]
        public string StoreEmail { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(200, ErrorMessage = "备注长度不符", MinimumLength = 1)]
        public string ReMark { get; set; }

        /// <summary>
        /// PC端横幅照片
        /// </summary>
        [Required(ErrorMessage = "PC端横幅照片必填,请完善")]
        public string PcBottomImg { get; set; }

        /// <summary>
        /// 移动端横幅照片
        /// </summary>
        [Required(ErrorMessage = "移动端横幅照片必填,请完善")]
        public string MobileBottomImg { get; set; }

        /// <summary>
        /// 二维码
        /// </summary>
        public string QRCodeImg { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [Required(ErrorMessage = "地址是必填项")]
        [StringLength(200, ErrorMessage = "地址长度不符", MinimumLength = 1)]
        public string StoreAddress { get; set; }
    }
}
