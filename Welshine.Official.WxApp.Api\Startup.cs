using Autofac;
using Autofac.Extensions.DependencyInjection;
using Autofac.Extras.CommonServiceLocator;
using CommonServiceLocator;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Core;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.Filters;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="containerBuilder"></param>
        public void ConfigureContainer(ContainerBuilder containerBuilder)
        {
            containerBuilder.RegisterModule<AutofacModuleRegister>();
        }
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllersWithViews()
               .AddControllersAsServices()
              .AddNewtonsoftJson(options =>
              {
                  options.SerializerSettings.ContractResolver = new NullToEmptyStringResolver();
                  options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                  options.SerializerSettings.StringEscapeHandling = StringEscapeHandling.Default;
                   //options.SerializerSettings.Converters.Add(new SafeStringConverter());
              })
           ;//控制器当做实例创建;
            services.AddControllers(options =>
             {
                 var serviceProvider = services.BuildServiceProvider();
                 var customJsonInputFormatter = new CustomJsonInputFormatter(
                             serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger<CustomJsonInputFormatter>(),
                             serviceProvider.GetRequiredService<IOptions<MvcNewtonsoftJsonOptions>>().Value.SerializerSettings,
                             serviceProvider.GetRequiredService<System.Buffers.ArrayPool<char>>(),
                             serviceProvider.GetRequiredService<Microsoft.Extensions.ObjectPool.ObjectPoolProvider>(),
                             options,
                             serviceProvider.GetRequiredService<IOptions<MvcNewtonsoftJsonOptions>>().Value
                     );
                 options.InputFormatters.Insert(0, customJsonInputFormatter);

             });
            // 配置跨域处理，允许所有来源
            services.AddCors(options =>
            options.AddPolicy("cors",
            p => p.SetIsOriginAllowed(origin => true)
                    .AllowAnyMethod()
                    .AllowAnyHeader().AllowCredentials()));
            services.Configure<AliOssSettings>(Configuration.GetSection("AliOss"));
            services.Configure<AppConfig>(Configuration.GetSection("AppConfig"));
            services.AddAutoMapper(typeof(AutoMapperServiceProfile));
            services.AddFreeRedis(Configuration);
            services.AddMySql(Configuration);
            services.AddHttpClient();
            // 注册Swagger服务
            services.AddSwaggerGen(c =>
            {
                // 添加文档信息
                c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo { Title = "官网小程序API", Version = "v1" });
                c.SchemaFilter<EnumSchemaFilter>();
                c.SchemaFilter<SwaggerAddEnumDescriptions>();
                c.SchemaFilter<SwaggerExcludeFilter>();
                foreach (var itemPath in XmlCommentsFilePath)
                {
                    c.IncludeXmlComments(itemPath, true);
                }
                c.CustomOperationIds(apiDesc =>
                {
                    var controllerAction = apiDesc.ActionDescriptor as ControllerActionDescriptor;
                    return controllerAction.ControllerName + "-" + controllerAction.ActionName;
                });
                c.DocumentFilter<SwaggerHiddenApiFilter>();
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            app.UsePathBase("/"+Program.AppName);
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            ServiceLocator.SetLocatorProvider(() => new AutofacServiceLocator(app.ApplicationServices.GetAutofacRoot()));
            app.UseRouting();
            // 允许所有跨域，cors是在ConfigureServices方法中配置的跨域策略名称
            app.UseCors("cors");
            //权限
            app.UseAuthentication();
            app.UseAuthorization();

            var hideSwagger = "false" == System.Environment.GetEnvironmentVariable("UseSwagger");
            
            if (!hideSwagger)
            {
                app.UseSwagger();
                // 配置SwaggerUI
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint($"{Environment.GetEnvironmentVariable("APIBASEPATH")}/swagger/v1/swagger.json", "图册小程序API");
                    //c.RoutePrefix = string.Empty;
                    //添加xml文件
                });
            }
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.Map("/", context =>
                {
                    context.Response.Redirect("/swagger/");
                    return Task.CompletedTask;
                });
            });
        }
        #region swagger
        List<string> XmlCommentsFilePath
        {
            get
            {
                var basePath = AppContext.BaseDirectory;
                DirectoryInfo d = new DirectoryInfo(basePath);
                FileInfo[] files = d.GetFiles("*.xml");
                var xmls = files.Select(a => Path.Combine(basePath, a.FullName)).ToList();
                return xmls;
            }
        }
        #endregion
    }
}
