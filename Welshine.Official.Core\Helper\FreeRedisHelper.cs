﻿using FreeRedis;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Welshine.Official.Core
{
    public sealed class FreeRedisHelper
    {
        private static FreeRedisConnectConfig _freeRedisConnectConfig;
        private static RedisClient _instance;
       
        private static object _configObj = new object();
        private static object _defalutObj = new object();
       
        public static void Init(FreeRedisConnectConfig freeRedisConnectConfig)
        {
            if (_freeRedisConnectConfig == null)
            {
                lock (_configObj)
                {
                    if (_freeRedisConnectConfig == null)
                    {
                        _freeRedisConnectConfig = freeRedisConnectConfig;
                    }
                }
            }
        }

       
        public static RedisClient DefaultInstance
        {
            get
            {
                if (_freeRedisConnectConfig == null)
                {
                    throw new Exception("未调用Init方法，初始化配置！");
                }
                if (_instance == null)
                {
                    lock (_defalutObj)
                    {
                        if (_instance == null)
                        {
                            _instance = new RedisClient(_freeRedisConnectConfig.DefaultConnectStr);
                        }
                    }
                }
                return _instance;
            }
        }

       

        public static T Get<T>(string key)where T : class
        {
           
            return  DefaultInstance.Get<T>(key);
            
        }

        public static void Set<T>(string key, T value, int expireSeconds = -1)
        {
             DefaultInstance.Set<T>(key,value, expireSeconds);
        }

       
    }
   
}
