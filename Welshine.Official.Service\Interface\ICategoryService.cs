﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 分类管理服务接口
    /// </summary>
    public interface ICategoryService
    {
        /// <summary>
        /// 添加分类
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        Task<bool> AddCategory(Category category);

        /// <summary>
        /// 编辑分类
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        Task<bool> EditCategory(Category category);

        /// <summary>
        /// 删除分类
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        Task<bool> DeleteCategory(Category category);

        /// <summary>
        /// 获取分类列表
        /// </summary>
        /// <returns></returns>
        Task<List<GetCategoryResponse>> GetCategoryList();

        /// <summary>
        /// 获取分类列表(官网前台)
        /// </summary>
        /// <returns></returns>
        Task<List<GetWebCategoryListResponse>> GetWebCategoryList();

        /// <summary>
        /// 修改分类排序
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<bool> EditCategorySort(List<Category> list);

        /// <summary>
        /// 获取分类详情
        /// </summary>
        /// <param name="categoryId">分类Id</param>
        /// <returns></returns>
        Task<Category> GetCategory(long categoryId);

    }
}
