
using Welshine.Official.Core.RestfulApi.Helper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Linq;
using Welshine.Official.Core.Exceptions;

namespace Welshine.Official.Core.Attributes
{
    /// <summary>
    /// 
    /// </summary>
    public class ModelValidateAttribute : ActionFilterAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="filterContext"></param>
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var modelState = ((Controller)filterContext.Controller).ViewData.ModelState;
            if (!modelState.IsValid)
            {
                var errorMessage = modelState.Values
                    .SelectMany(m => m.Errors)
                    .Select(m => m.ErrorMessage)
                    .First();
                if (errorMessage.Contains("请求报文出错")
                    || string.IsNullOrWhiteSpace(modelState.Keys.FirstOrDefault())
                    || errorMessage.Contains("not valid")
                    || errorMessage.Contains("invalid"))
                {
                    filterContext.Result = new JsonResult(WebApiResponseHelp.Result(ErrorCode.SerializeFailed.GetHashCode(), "请求报文出错"));
                    return;
                }
                //直接响应验证结果
                filterContext.Result = new JsonResult(WebApiResponseHelp.Result(ErrorCode.ValidationFailed.GetHashCode(), errorMessage));
            }
        }
    }
}
