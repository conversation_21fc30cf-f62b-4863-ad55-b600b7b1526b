﻿using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;
using Welshine.Official.Web.Api.Core;

namespace Welshine.Official.Web.Api.Controllers
{
    /// <summary>
    /// 首页
    /// </summary>
    public class HomePageController : BaseApiController
    {
        private readonly IVersionsService _versionsService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="versionsService"></param>
        public HomePageController(IVersionsService versionsService)
        {
            _versionsService = versionsService;
        }

        /// <summary>
        /// 首页
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetHomePageInfoResponse>> GetHomePageInfo()
        {
            var data = await _versionsService.GetHomePageInfo();
            return Success("请求成功", data);
        }


        /// <summary>
        /// 底部横幅
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<BottomData>> GetBottomData()
        {
            var data = await _versionsService.GetBottomData();
            return Success("请求成功", data);
        }

        /// <summary>
        /// 关于我们
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<WelshineData>> GetWelshineData()
        {
            var data = await _versionsService.GetWelshineData();
            return Success("请求成功", data);
        }

        /// <summary>
        /// 侧边横幅
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<SidebarData>> GetSidebarData()
        {
            var data = await _versionsService.GetSidebarData();
            return Success("请求成功", data);
        }
    }
}
