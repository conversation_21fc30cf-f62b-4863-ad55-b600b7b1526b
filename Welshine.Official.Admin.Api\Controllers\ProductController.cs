﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 精选产品
    /// </summary>
    public class ProductController : BaseApiController
    {
        private readonly IProductService _productService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="productService"></param>
        public ProductController(IProductService productService)
        {
            _productService = productService;
        }

        /// <summary>
        /// 添加产品分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2001">产品名称已存在</response>
        /// <response code="2002">产品略缩图不存在</response>
        /// <response code="2003">产品详情图，最多可上传5张图片</response>
        /// <response code="2004">产品详情图不存在</response>
        /// <response code="2005">产品数量已达上限</response>
        /// <response code="2202">文件格式错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddProduct([FromBody] BaseRequest<AddProductRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Product product = new Product()
                { 
                    ProductName = request.Body.ProductName,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _productService.AddProduct(product, request.Body.Thumbnail, request.Body.DetailPicture);
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddCouponConfig Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2001">产品名称已存在</response>
        /// <response code="2002">产品略缩图不存在</response>
        /// <response code="2003">产品详情图，最多可上传5张图片</response>
        /// <response code="2004">产品详情图不存在</response>
        /// <response code="2006">产品Id不存在</response>
        /// <response code="2202">文件格式错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditProduct([FromBody] BaseRequest<EditProductRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Product product = new Product()
                {
                    Id  = request.Body.ProductId,
                    ProductName = request.Body.ProductName,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _productService.EditProduct(product, request.Body.Thumbnail, request.Body.DetailPicture);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditProduct Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取产品详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2006">产品Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ProductDetailResponse>> GetProductById([FromBody] BaseRequest<ProductIdRequest> request)
        {
            ProductDetailResponse result = null;
            try
            {
                result = await _productService.GetProductById(request.Body.ProductId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductById Error {u}", ex.Message);
                return Failure<ProductDetailResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 删除产品分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2006">产品Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteProduct([FromBody] BaseRequest<ProductIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Product product = new Product()
                {
                    Id = request.Body.ProductId,
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _productService.DeleteProduct(product);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteProduct Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<ProductListReponse>>> GetProductPageList([FromBody] RequestPageModel<GetProductListRequest> request)
        {
            PageRows<ProductListReponse> pageRows = null;
            try
            {
                pageRows = await _productService.GetProductPageList(request.RequestParams.ProductName, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetProductPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
    }
}
