﻿namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 用户登记表
    /// </summary>
    [SqlSugar.SugarTable("cms_user_register")]
    public class UserRegister : BaseBigEntity
    {
        /// <summary>
        /// 资料编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "code")]
        public string Code { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "phone")]
        public string Phone { get; set; }

        /// <summary>
        /// 图册编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "atlas_code")]
        public string AtlasCode { get; set; }

        /// <summary>
        /// 图册名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "atlas_name")]
        public string AtlasName { get; set; }
    }
}
