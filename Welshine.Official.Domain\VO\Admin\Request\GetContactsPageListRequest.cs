﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 联系方式查询条目
    /// </summary>
    public class GetContactsPageListRequest
    {
        /// <summary>
        /// 门店名称
        /// </summary>
        [StringLength(40, ErrorMessage = "门店名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z]+$", ErrorMessage = "门店名称格式不正确")]
        public string StoreName { get; set; }

        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [Range(0, 1, ErrorMessage = "内容状态参数错误")]
        public int? ReleaseStatus { get; set; }

        /// <summary>
        /// 审核状态: 0->待提交; 1->待审核; 2->审核通过; 3->审核驳回
        /// </summary>
        [Range(0, 3, ErrorMessage = "审核状态参数错误")]
        public int? ApproverStatus { get; set; }

        /// <summary>
        /// 发布时间筛选
        /// </summary>
        public TimeHorizon ReleaseTimeScope { get; set; }
    }
}
