﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 用户留言条目
    /// </summary>
    public class AddMessageBoardRequest
    {
        /// <summary>
        /// 姓名
        /// </summary>
        [Required(ErrorMessage = "姓名是必填项")]
        [StringLength(10, ErrorMessage = "姓名长度不符", MinimumLength = 1)]
        public string Name { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [Required(ErrorMessage = "电话是必填项")]
        [StringLength(13, ErrorMessage = "电话长度不符", MinimumLength = 1)]
        [RegularExpression("^[0-9_\\-@&=`~#%^*（()）【】{};：.、‘\"'/?><，。]+$", ErrorMessage = "电话格式错误")]
        public string Phone { get; set; }

        /// <summary>
        /// 留言内容
        /// </summary>
        [Required(ErrorMessage = "留言内容是必填项")]
        [StringLength(500, ErrorMessage = "留言内容长度不符", MinimumLength = 1)]
        public string Content { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [Required(ErrorMessage = "邮箱是必填项")]
        [StringLength(40, ErrorMessage = "邮箱长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "邮箱不允许有中文")]
        public string Email { get; set; }

        /// <summary>
        /// 地区
        /// </summary>
        [Required(ErrorMessage = "地区是必填项")]
        [StringLength(40, ErrorMessage = "地区长度不符", MinimumLength = 1)]
        public string Area { get; set; }

    }
}
