﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO;

namespace Welshine.Official.Service.Interface
{
    public interface IFileService
    {
        /// 上传文件
        /// </summary>
        /// <param name="formFile"></param>
        /// <returns></returns>
        Task<FileDto> UploadFile(IFormFile formFile, string userId, string userName, bool isOverlay);

        /// <summary>
        /// 添加文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        Task<FileDto> AddUploadFiles(Files file);
    }
}
