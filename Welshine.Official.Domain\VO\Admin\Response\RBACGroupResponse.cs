﻿using System;
using System.Collections.Generic;
using System.Text;
using Welshine.Official.Domain.Entity;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class RBACGroupResponse
    {
        /// <summary>
        /// id
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 权限分组编号
        /// </summary>
        public string GroupNo { get; set; }
        /// <summary>
        /// 权限分组名称
        /// </summary>
        public string GroupName { get; set; }
        /// <summary>
        /// 权限分组描述
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 权限分组用户名列表
        /// </summary>
        public string UserNames { get; set; }
        /// <summary>
        /// 权限分组用户信息列表
        /// </summary>
        public List<Account> AccountList { get; set; }
        /// <summary>
        /// 权限分组菜单结构
        /// </summary>
        public List<string> RBACMenuList { get; set; }
        /// <summary>
        /// 权限分组url结构
        /// </summary>
        public List<string> RBACUrlList { get; set; }
    }
}
