﻿using Newtonsoft.Json;
using System;

namespace Welshine.Official.Core
{
    /// <summary>
    /// 布尔型 传入整型或浮点型数据应该提示错误
    /// </summary>
    public class JsonConverterBool : JsonConverter
    {
        /// <summary>
        /// 是否可以转换
        /// </summary>
        /// <param name="objectType"></param>
        /// <returns></returns>
        public override bool CanConvert(Type objectType)
        {
            return true;
        }
        /// <summary>
        /// 读取布尔型
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="objectType"></param>
        /// <param name="existingValue"></param>
        /// <param name="serializer"></param>
        /// <returns></returns>
        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.Value != null)
            {
                if (reader.Value.ToString().ToLower() == "true" || reader.Value.ToString().ToLower() == "false")
                {
                    return bool.Parse(reader.Value.ToString());
                }
                else
                {
                    throw new System.Exception("bool data is invalid");
                }
            }
            return null;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="writer"></param>
        /// <param name="value"></param>
        /// <param name="serializer"></param>
        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            writer.WriteValue(value);
        }
    }
}
