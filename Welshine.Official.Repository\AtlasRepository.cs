﻿using DTHY.Core.Repository;
using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.SNGeneration;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 图册仓储实现
    /// </summary>
    public class AtlasRepository : BaseRepository<Atlas>, IAtlasRepository
    {
        private readonly IFileRepository _fileRepository;

        public AtlasRepository(IFileRepository fileRepository)
        {
            _fileRepository = fileRepository;
        }

        /// <summary>
        /// 判断图册名称是否存在
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <returns></returns>
        public async Task<bool> ExistsAtlasName(string atlasName)
        {
            return await DbScoped.SugarScope.Queryable<Atlas>().AnyAsync(x => x.AtlasName == atlasName && !x.IsDeleted);
        }

        /// <summary>
        /// 判断图册名称是否存在
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="atlasId">图册Id</param>
        /// <returns></returns>
        public async Task<bool> ExistsAtlasName(string atlasName, long atlasId)
        {
            return await DbScoped.SugarScope.Queryable<Atlas>().AnyAsync(x => x.AtlasName == atlasName && x.Id != atlasId && !x.IsDeleted);
        }

        /// <summary>
        /// 添加图册信息
        /// </summary>
        /// <param name="atlas">图册信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        public async Task<bool> AddAtlas(Atlas atlas, string thumbnail, string fileId)
        {
            atlas.AtlasCode = await CodeHelper.GetCode("TC");

            var id = await DbScoped.SugarScope.Insertable(atlas).ExecuteReturnBigIdentityAsync();
            if (id > 0)
            {
                #region 封面图
                if (!string.IsNullOrWhiteSpace(thumbnail))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = thumbnail,
                        ObjectId = id,
                        TableEnum = EnumRelationType.AtlasThumbnail,
                        CreatedBy = atlas.UpdatedBy,
                        CreatorId = atlas.CreatorId,
                        UpdatedBy = atlas.UpdatedBy,
                        ModifierId = atlas.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                #region 详情图
                if (!string.IsNullOrWhiteSpace(fileId))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = fileId,
                        ObjectId = id,
                        TableEnum = EnumRelationType.AtlasFile,
                        CreatedBy = atlas.UpdatedBy,
                        CreatorId = atlas.CreatorId,
                        UpdatedBy = atlas.UpdatedBy,
                        ModifierId = atlas.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取图册信息
        /// </summary>
        /// <param name="atlasId">图册Id</param>
        /// <returns></returns>
        public async Task<Atlas> GetAtlas(long atlasId)
        {
            return await DbScoped.SugarScope.Queryable<Atlas>().FirstAsync(x => x.Id == atlasId && !x.IsDeleted);
        }

        /// <summary>
        /// 修改图册信息
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <param name="thumbnail">封面图</param>
        /// <param name="fileId">图册文件</param>
        /// <returns></returns>
        public async Task<bool> EditAtlas(Atlas atlas, string thumbnail, string fileId)
        {
            var num = await DbScoped.SugarScope.Updateable(atlas).ExecuteCommandAsync();
            if (num > 0)
            {
                //清空关联文件
                await DbScoped.SugarScope.Updateable<FilesRelation>()
                    .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = atlas.ModifierId, UpdatedBy = atlas.UpdatedBy, UpdatedTime = atlas.UpdatedTime })
                    .Where(x => x.ObjectId == atlas.Id && (x.TableEnum == EnumRelationType.AtlasFile || x.TableEnum == EnumRelationType.AtlasThumbnail))
                    .ExecuteCommandAsync();

                #region 略缩图
                if (!string.IsNullOrWhiteSpace(thumbnail))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = thumbnail,
                        ObjectId = atlas.Id,
                        TableEnum = EnumRelationType.AtlasThumbnail,
                        CreatedBy = atlas.UpdatedBy,
                        CreatorId = atlas.CreatorId,
                        UpdatedBy = atlas.UpdatedBy,
                        ModifierId = atlas.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                #region 详情图
                if (!string.IsNullOrWhiteSpace(fileId))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = fileId,
                        ObjectId = atlas.Id,
                        TableEnum = EnumRelationType.AtlasFile,
                        CreatedBy = atlas.UpdatedBy,
                        CreatorId = atlas.CreatorId,
                        UpdatedBy = atlas.UpdatedBy,
                        ModifierId = atlas.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                return true;
            }
            return false;
        }

        /// <summary>
        /// 删除图册信息
        /// </summary>
        /// <param name="atlas">产品信息</param>
        /// <returns></returns>
        public async Task<bool> DeleteAtlas(Atlas atlas)
        {
            //删除产品
            await DbScoped.SugarScope.Updateable<Atlas>()
                .SetColumns(x => new Atlas() { IsDeleted = true, ModifierId = atlas.ModifierId, UpdatedBy = atlas.UpdatedBy, UpdatedTime = atlas.UpdatedTime })
                .Where(x => x.Id == atlas.Id)
                .ExecuteCommandAsync();

            //清空产品关联文件
            await DbScoped.SugarScope.Updateable<FilesRelation>()
                .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = atlas.ModifierId, UpdatedBy = atlas.UpdatedBy, UpdatedTime = atlas.UpdatedTime })
                .Where(x => x.ObjectId == atlas.Id && (x.TableEnum == EnumRelationType.AtlasFile || x.TableEnum == EnumRelationType.AtlasThumbnail))
                .ExecuteCommandAsync();

            return true;
        }

        /// <summary>
        /// 获取图册列表
        /// </summary>
        /// <param name="atlasName">图册名称</param>
        /// <param name="times">时间筛选</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<AtlasListReponse>> GetAtlasPageList(string atlasName, TimeHorizon times, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<AtlasListReponse> result = new PageRows<AtlasListReponse>();

            result.Data = await DbScoped.SugarScope.Queryable<Atlas>()
                .Where(x => !x.IsDeleted)
                .WhereIF(!string.IsNullOrWhiteSpace(atlasName), x => x.AtlasName.Contains(atlasName))
                .WhereIF(times != null && times.From != null, x => x.CreatedTime >= times.From)
                .WhereIF(times != null && times.To != null, x => x.CreatedTime <= times.To)
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .Select(x => new AtlasListReponse()
                {
                    AtlasId = x.Id,
                    AtlasCode = x.AtlasCode,
                    AtlasName = x.AtlasName,
                    CreatedTime = x.CreatedTime,
                    UpdatedBy = x.UpdatedBy
                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            result.Total = totalNumber;

            var ids = result.Data.Select(x => x.AtlasId).ToList();

            var files = await _fileRepository.GetFileList(ids, EnumRelationType.AtlasFile);

            foreach (var item in result.Data)
            {
                var file = files.Where(x => x.ObjectId == item.AtlasId).FirstOrDefault();
                if (file != null)
                {
                    item.AtlasFile = new Domain.VO.FileResponse() { FileId = file.FileId, FileName = file.Name, Url = file.Url };
                }
            }

            return result;
        }

        /// <summary>
        /// 获取最新图册
        /// </summary>
        /// <param name="openId">用户openId</param>
        /// <returns></returns>
        public async Task<AtlasDetailResponse> WX_GetAtlas(string openId)
        {
            AtlasDetailResponse result = null;
            var atlas = await DbScoped.SugarScope.Queryable<Atlas>().Where(x => !x.IsDeleted).OrderByDescending(x => x.UpdatedTime).FirstAsync();
            if (atlas != null)
            {
                result = new AtlasDetailResponse()
                {
                    AtlasId = atlas.Id,
                    AtlasCode = atlas.AtlasCode,
                    AtlasName = atlas.AtlasName,
                    UpdatedTime = atlas.UpdatedTime
                };

                var files = await _fileRepository.GetFileList(new List<long> { result.AtlasId });

                var thumbnail = files.FirstOrDefault(x => x.TableEnum == EnumRelationType.AtlasThumbnail);
                if (thumbnail != null)
                {
                    result.Thumbnail = new Domain.VO.FileResponse() { FileId = thumbnail.FileId, FileName = thumbnail.Name, Url = thumbnail.Url };
                }

                var atlasFile = files.FirstOrDefault(x => x.TableEnum == EnumRelationType.AtlasFile);
                if (atlasFile != null)
                {
                    result.AtlasFile = new Domain.VO.FileResponse() { FileId = atlasFile.FileId, FileName = atlasFile.Name, Url = atlasFile.Url };
                }

                if (!string.IsNullOrWhiteSpace(openId))
                {
                    var redisKey = "UserCheckIn:" + DateTime.Now.ToString("yyyyMMdd") + ":" + openId;
                    var value = await FreeRedisHelper.DefaultInstance.GetAsync(redisKey);
                    if (value != "")
                    {
                        int UploadCount = 0;
                        int.TryParse(value, out UploadCount);
                        result.UploadCount = UploadCount;
                    }
                }
            }

            return result;
        }
    }
}