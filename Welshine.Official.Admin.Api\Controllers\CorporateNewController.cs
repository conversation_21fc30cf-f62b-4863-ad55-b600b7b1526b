﻿using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.RestfulApi.Helper;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 企业资讯
    /// </summary>
    public class CorporateNewController:BaseApiController
    {
        private readonly ICorporateNewService _corporateNewService;

        public CorporateNewController(ICorporateNewService corporateNewService) 
        {
            _corporateNewService = corporateNewService;
        }

        /// <summary>
        /// 添加企业资讯
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<CorporateNewResponse> AddNew([FromBody]BaseRequest<AddCorporateNewRequest> request)
        {
            var body = request.Body;
            var currentUser = GetUserInfo();
            if (body.CNItem.NewTitle.Length>100)
            {
                return Failure<CorporateNewResponse>(ErrorCode.ValidationFailed.GetHashCode(), "中文文章标题最多为100字");
            }
            if (body.ENItem.NewTitle.Length>300)
            {
                return Failure<CorporateNewResponse>(ErrorCode.ValidationFailed.GetHashCode(), "英文文章标题最多为300字");
            }
            var data = _corporateNewService.AddNew(body.CNItem.NewTitle, body.CNItem.NewContent, body.CNItem.NewImage
                , body.ENItem.NewTitle, body.ENItem.NewContent, body.ENItem.NewImage,body.CNItem.PublishDate,body.ENItem.PublishDate, currentUser, request.Body.NewColumn.Value, request.Body.NewRecommend.Value, request.Body.ENItem.Description, request.Body.CNItem.Description);
            return Success("请求成功", data);
        }
        /// <summary>
        /// 删除企业资讯
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse DeleteNew([FromBody] BaseRequest<OperateCorporateNewRequest> request)
        {
            var body = request.Body;
            var currentUser = GetUserInfo();
            _corporateNewService.DeleteNew(body.NewId.Value, currentUser);
            return Success("请求成功");
        }
        /// <summary>
        /// 分页查询企业资讯
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<PageRows<CorporateNewResponse>> FindCorporateNewPageList([FromBody] RequestPageModel<FindCorporateNewPageListRequest> request)
        {
            var body = request.RequestParams;
            DateTime? startDate = null;
            DateTime? endDate = null;
            var orderType = OrderByType.Desc;
            if (request.SortType == SortType.Asc)
            {
                orderType = OrderByType.Asc;
            }
            if (body.CreatedTimeScope!=null)
            {
                startDate = body.CreatedTimeScope.From;
                endDate = body.CreatedTimeScope.To;
            }
            var data = _corporateNewService.FindCorporateNewPageList(body.WelshineTitle, body.ReleaseStatus, startDate, endDate, body.NewColumn, body.NewRecommend, request.PageIndex, request.PageSize,request.OrderFile, orderType);
            return Success("请求成功", data);
        }
        /// <summary>
        /// 编辑企业资讯
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse EditNew([FromBody]BaseRequest<EditCorporateNewRequest> request)
        {
            var body = request.Body;
            var currentUser = GetUserInfo();
            if (body.CNItem.NewTitle.Length > 100)
            {
                return WebApiResponseHelp.Result(ErrorCode.ValidationFailed.GetHashCode(), "中文文章标题最多为100字");
            }
            if (body.ENItem.NewTitle.Length > 300)
            {
                return WebApiResponseHelp.Result(ErrorCode.ValidationFailed.GetHashCode(), "英文文章标题最多为300字");
            }
            _corporateNewService.EditNew(body.NewId.Value,body.CNItem.NewTitle,body.CNItem.NewContent,body.CNItem.NewImage
                ,body.ENItem.NewTitle,body.ENItem.NewContent,body.ENItem.NewImage,body.CNItem.PublishDate,body.ENItem.PublishDate, currentUser
                , request.Body.NewColumn.Value, request.Body.NewRecommend.Value, request.Body.ENItem.Description, request.Body.CNItem.Description);
            return Success("请求成功");
        }
        /// <summary>
        /// 查询资讯明细
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<CorporateNewDetailResponse> FindNewDetail([FromBody]BaseRequest<OperateCorporateNewRequest> request)
        {
            var body = request.Body;
            var data = _corporateNewService.FindNewDetail(body.NewId.Value);
            return Success("请求成功", data);
        }
        /// <summary>
        /// 修改资讯发布状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse SwitchNewStatus([FromBody] BaseRequest<SwitchNewStatusRequest>  request)
        {
            var body = request.Body;
            var currentUser = GetUserInfo();
            _corporateNewService.SwitchNewStatus(body.NewId.Value, body.ReleaseStatus.Value, currentUser);
            return Success("请求成功");
        }

        /// <summary>
        /// 获取文章栏目热门推荐数
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<int> GetRecommendCountByNewColumn([FromBody] BaseRequest<GetRecommendCountByNewColumnRequest> request)
        {
            var body = request.Body;
            int count = _corporateNewService.GetRecommendCountByNewColumn(body.NewColumn.Value);
            return Success<int>("请求成功", count);
        }
    }
}
