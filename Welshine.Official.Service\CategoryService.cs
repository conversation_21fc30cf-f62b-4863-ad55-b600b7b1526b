﻿using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Spreadsheet;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata.Ecma335;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 分类管理服务接口实现
    /// </summary>
    public class CategoryService : ICategoryService
    {
        private readonly ICategoryRepository _categoryRepository;
        private readonly ICategoryProductRepository _categoryProductRepository;

        public CategoryService(ICategoryRepository categoryRepository, ICategoryProductRepository categoryProductRepository)
        {
            _categoryRepository = categoryRepository;
            _categoryProductRepository = categoryProductRepository;
        }

        /// <summary>
        /// 添加分类
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public async Task<bool> AddCategory(Category category)
        {
            if (category.FatherId > 0)
            {
                Category parentCategory = await _categoryRepository.GetCategoryById(category.FatherId);
                if (parentCategory == null)
                {
                    throw new BusinessException(ErrorCode.CategoryParentNoFoundError.ToDescriptionName(), ErrorCode.CategoryParentNoFoundError.GetHashCode());
                }
                category.Level = parentCategory.Level + 1;
            }

            if (category.Level > 1 && !string.IsNullOrWhiteSpace(category.LogoImgUrl))
            {
                throw new BusinessException(ErrorCode.CategoryLogoError.ToDescriptionName(), ErrorCode.CategoryLogoError.GetHashCode());
            }
            if (category.Level > 1 && !string.IsNullOrWhiteSpace(category.LogoImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryLogoError.ToDescriptionName(), ErrorCode.CategoryLogoError.GetHashCode());
            }
            if (category.Level > 1 && !string.IsNullOrWhiteSpace(category.MobileImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryMobileLogoError.ToDescriptionName(), ErrorCode.CategoryMobileLogoError.GetHashCode());
            }

            if (category.Level == 1 && string.IsNullOrWhiteSpace(category.LogoImgUrl))
            {
                throw new BusinessException(ErrorCode.CategoryLogoNotUpError.ToDescriptionName(), ErrorCode.CategoryLogoNotUpError.GetHashCode());
            }
            if (category.Level == 1 && string.IsNullOrWhiteSpace(category.LogoImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryLogoNotUpError.ToDescriptionName(), ErrorCode.CategoryLogoNotUpError.GetHashCode());
            }
            if (category.Level == 1 && string.IsNullOrWhiteSpace(category.MobileImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryMobileLogoNotUpError.ToDescriptionName(), ErrorCode.CategoryMobileLogoNotUpError.GetHashCode());
            }
            if (category.Level > 2)
            {
                throw new BusinessException(ErrorCode.CategoryLevelError.ToDescriptionName(), ErrorCode.CategoryLevelError.GetHashCode());
            }

            if (await _categoryRepository.ExistsCategoryName("en", category.NameEN, category.Level))
            {
                throw new BusinessException(ErrorCode.CategoryNameENError.ToDescriptionName(), ErrorCode.CategoryNameENError.GetHashCode());
            }
            if (await _categoryRepository.ExistsCategoryName("cn", category.NameCN, category.Level))
            {
                throw new BusinessException(ErrorCode.CategoryNameCNError.ToDescriptionName(), ErrorCode.CategoryNameCNError.GetHashCode());
            }

            int sort = await _categoryRepository.GetCategorySort(category.FatherId);
            category.Sort = sort;

            return await _categoryRepository.Add(category);
        }

        /// <summary>
        /// 编辑分类
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public async Task<bool> EditCategory(Category category)
        {
            Category entity = await _categoryRepository.GetCategoryById(category.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
            }

            if (await _categoryRepository.ExistsCategoryName("en", category.NameEN, category.Id, category.Level))
            {
                throw new BusinessException(ErrorCode.CategoryNameENError.ToDescriptionName(), ErrorCode.CategoryNameENError.GetHashCode());
            }

            if (await _categoryRepository.ExistsCategoryName("cn", category.NameCN, category.Id, category.Level))
            {
                throw new BusinessException(ErrorCode.CategoryNameCNError.ToDescriptionName(), ErrorCode.CategoryNameCNError.GetHashCode());
            }

            if (entity.Level > 1 && !string.IsNullOrWhiteSpace(category.LogoImgUrl))
            {
                throw new BusinessException(ErrorCode.CategoryLogoError.ToDescriptionName(), ErrorCode.CategoryLogoError.GetHashCode());
            }
            if (entity.Level > 1 && !string.IsNullOrWhiteSpace(category.LogoImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryLogoError.ToDescriptionName(), ErrorCode.CategoryLogoError.GetHashCode());
            }
            if (category.Level > 1 && !string.IsNullOrWhiteSpace(category.MobileImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryMobileLogoError.ToDescriptionName(), ErrorCode.CategoryMobileLogoError.GetHashCode());
            }

            if (entity.Level == 1 && string.IsNullOrWhiteSpace(category.LogoImgUrl))
            {
                throw new BusinessException(ErrorCode.CategoryLogoNotUpError.ToDescriptionName(), ErrorCode.CategoryLogoNotUpError.GetHashCode());
            }
            if (entity.Level == 1 && string.IsNullOrWhiteSpace(category.LogoImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryLogoNotUpError.ToDescriptionName(), ErrorCode.CategoryLogoNotUpError.GetHashCode());
            }
            if (entity.Level == 1 && string.IsNullOrWhiteSpace(category.MobileImgUrlActivate))
            {
                throw new BusinessException(ErrorCode.CategoryMobileLogoNotUpError.ToDescriptionName(), ErrorCode.CategoryMobileLogoNotUpError.GetHashCode());
            }

            entity.NameEN = category.NameEN;
            entity.NameCN = category.NameCN;
            entity.LogoImgUrl = category.LogoImgUrl;
            entity.LogoImgUrlActivate = category.LogoImgUrlActivate;
            entity.MobileImgUrlActivate = category.MobileImgUrlActivate;
            entity.UpdatedTime = category.UpdatedTime;
            entity.UpdatedBy = category.UpdatedBy;
            entity.ModifierId = category.ModifierId;

            return await _categoryRepository.Update(entity);
        }

        /// <summary>
        /// 删除分类
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public async Task<bool> DeleteCategory(Category category)
        {
            Category entity = await _categoryRepository.GetCategoryById(category.Id);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
            }

            if (await _categoryRepository.ExistsChild(category.Id))
            {
                throw new BusinessException(ErrorCode.CategoryDeleteChilrenError.ToDescriptionName(), ErrorCode.CategoryDeleteChilrenError.GetHashCode());
            }

            if (entity.Level == 2)
            {
                if (await _categoryProductRepository.ExistsCategoryProduct(category.Id))
                {
                    throw new BusinessException(ErrorCode.CategoryDeleteProductError.ToDescriptionName(), ErrorCode.CategoryDeleteProductError.GetHashCode());
                }
            }

            entity.IsDeleted = category.IsDeleted;
            entity.UpdatedTime = category.UpdatedTime;
            entity.UpdatedBy = category.UpdatedBy;
            entity.ModifierId = category.ModifierId;

            return await _categoryRepository.Update(entity);
        }

        /// <summary>
        /// 获取分类列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<GetCategoryResponse>> GetCategoryList()
        {
            List<Category> list = await _categoryRepository.GetAllCategoryList();

            List<Category> parents = list.Where(x => x.FatherId == 0).OrderBy(x => x.Sort).ToList();

            List<CategoryProduct> categoryProductList = await _categoryProductRepository.GetEntityList(x => !x.IsDeleted);

            List<GetCategoryResponse> result = new List<GetCategoryResponse>();
            foreach (Category category in parents)
            {
                GetCategoryResponse cr = new GetCategoryResponse()
                {
                    CategoryId = category.Id,
                    CategoryNameEN = category.NameEN,
                    CategoryNameCN = category.NameCN,
                    logoImgUrl = category.LogoImgUrl,
                    logoImgUrlActivate = category.LogoImgUrlActivate,
                    MobileImgUrlActivate = category.MobileImgUrlActivate,
                    parentCategoryId = category.FatherId,
                    sort = category.Sort,
                    isDelete = !list.Any(x => x.FatherId == category.Id),
                    Children = GetCategoryChildren(list, category, categoryProductList)
                };
                result.Add(cr);
            }

            return result;
        }

        /// <summary>
        /// 获取分类列表(官网前台)
        /// </summary>
        /// <returns></returns>
        public async Task<List<GetWebCategoryListResponse>> GetWebCategoryList()
        {
            List<Category> list = await _categoryRepository.GetAllCategoryList();

            List<Category> parents = list.Where(x => x.FatherId == 0).OrderBy(x => x.Sort).ToList();

            List<CategoryProduct> categoryProductList = await _categoryProductRepository.GetEntityList(x => !x.IsDeleted && x.ReleaseStatus == 1);

            List<GetWebCategoryListResponse> result = new List<GetWebCategoryListResponse>();
            foreach (Category category in parents)
            {
                GetWebCategoryListResponse cr = new GetWebCategoryListResponse()
                {
                    CategoryId = category.Id,
                    CategoryNameEN = category.NameEN,
                    CategoryNameCN = category.NameCN,
                    logoImgUrl = category.LogoImgUrl,
                    logoImgUrlActivate = category.LogoImgUrlActivate,
                    mobileImgUrlActivate = category.MobileImgUrlActivate,
                    parentCategoryId = category.FatherId,
                    sort = category.Sort,
                    isDelete = !list.Any(x => x.FatherId == category.Id),
                    ENShow = categoryProductList.Any(x => x.CategoryId == category.Id && x.ProductLanguage == 0),
                    CNShow = categoryProductList.Any(x => x.CategoryId == category.Id && x.ProductLanguage == 1),
                    Children = GetWebCategoryChildren(list, category, categoryProductList)
                };
                result.Add(cr);
            }

            return result;
        }

        /// <summary>
        /// 递归获取分类子级
        /// </summary>
        /// <param name="categoryList"></param>
        /// <param name="category"></param>
        /// <returns></returns>
        public List<GetWebCategoryListResponse> GetWebCategoryChildren(List<Category> categoryList, Category category, List<CategoryProduct> categoryProductList)
        {
            List<Category> list = categoryList.Where(x => x.FatherId == category.Id).OrderBy(x => x.Sort).ToList();
            if (list == null || list.Count == 0)
            {
                return null;
            }

            List<GetWebCategoryListResponse> result = new List<GetWebCategoryListResponse>();
            foreach (Category child in list)
            {
                GetWebCategoryListResponse cr = new GetWebCategoryListResponse()
                {
                    CategoryId = child.Id,
                    CategoryNameEN = child.NameEN,
                    CategoryNameCN = child.NameCN,
                    logoImgUrl = child.LogoImgUrl,
                    parentCategoryId = child.FatherId,
                    sort = child.Sort,
                    isDelete = !list.Any(x => x.FatherId == child.Id),
                    ENShow = categoryProductList.Any(x => x.CategoryId == child.Id && x.ProductLanguage == 0),
                    CNShow = categoryProductList.Any(x => x.CategoryId == child.Id && x.ProductLanguage == 1),
                    Children = GetWebCategoryChildren(categoryList, child, categoryProductList)
                };
                result.Add(cr);
            }

            return result;
        }

        /// <summary>
        /// 递归获取分类子级
        /// </summary>
        /// <param name="categoryList"></param>
        /// <param name="category"></param>
        /// <returns></returns>
        public List<GetCategoryResponse> GetCategoryChildren(List<Category> categoryList, Category category, List<CategoryProduct> categoryProductList)
        {
            List<Category> list = categoryList.Where(x => x.FatherId == category.Id).OrderBy(x => x.Sort).ToList();
            if (list == null || list.Count == 0)
            {
                return null;
            }

            List<GetCategoryResponse> result = new List<GetCategoryResponse>();
            foreach (Category child in list)
            {
                GetCategoryResponse cr = new GetCategoryResponse()
                {
                    CategoryId = child.Id,
                    CategoryNameEN = child.NameEN,
                    CategoryNameCN = child.NameCN,
                    logoImgUrl = child.LogoImgUrl,
                    parentCategoryId = child.FatherId,
                    sort = child.Sort,
                    isDelete = !categoryList.Any(x => x.FatherId == child.Id),
                    Children = GetCategoryChildren(categoryList, child, categoryProductList)
                };
                if (child.Level == 2)
                {
                    cr.isDelete = !categoryProductList.Any(x => x.CategoryId == cr.CategoryId);
                }
                result.Add(cr);
            }

            return result;
        }

        /// <summary>
        /// 修改分类排序
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public async Task<bool> EditCategorySort(List<Category> list)
        {
            var ids = list.Select(x => x.Id).ToList();
            List<Category> categoryList = await _categoryRepository.GetEntityList(x => !x.IsDeleted && ids.Contains(x.Id));

            List<Category> updateList = new List<Category>();
            foreach (var category in list)
            {
                Category entity = categoryList.FirstOrDefault(x => x.Id == category.Id);
                if (entity == null)
                {
                    throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
                }
                entity.Sort = category.Sort;
                entity.ModifierId = category.ModifierId;
                entity.UpdatedBy = category.UpdatedBy;
                entity.UpdatedTime = category.UpdatedTime;
                updateList.Add(entity);
            }

            return await _categoryRepository.Update(updateList);
        }

        /// <summary>
        /// 获取分类详情
        /// </summary>
        /// <param name="categoryId">分类Id</param>
        /// <returns></returns>
        public async Task<Category> GetCategory(long categoryId)
        {
            Category entity = await _categoryRepository.GetCategoryById(categoryId);
            if (entity == null)
            {
                throw new BusinessException(ErrorCode.CategoryNoFoundError.ToDescriptionName(), ErrorCode.CategoryNoFoundError.GetHashCode());
            }

            return entity;
        }
    }
}
