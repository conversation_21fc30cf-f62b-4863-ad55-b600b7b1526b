﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 获取产品列表入参
    /// </summary>
    public class GetProductListRequest
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        [StringLength(10, ErrorMessage = "产品名称长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "产品名称格式不正确")]
        public string ProductName { get; set; }
    }
}