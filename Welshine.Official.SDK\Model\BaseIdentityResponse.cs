﻿using System;
using System.Collections.Generic;
using System.Text;
using Refit;
namespace Welshine.Official.SDK.Model
{
    public class BaseIdentityResponse
    {
        /// <summary>
        /// 消息
        /// </summary>
        [AliasAs("message")]
        public string Message { get; set; }
        /// <summary>
        /// 编码
        /// </summary>
        [AliasAs("code")]
        public string Code { get; set; }
    }
    public class CheckTokenResponse : BaseIdentityResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        [AliasAs("success")]
        public bool Success { get; set; }
    }





}
