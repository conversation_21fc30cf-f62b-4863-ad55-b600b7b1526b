﻿using Welshine.Official.Core.Extensions;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.Linq;
namespace Welshine.Official.Core.Filters
{
    /// <summary>
    ///  swagger enum 支持
    /// </summary>
    public class SwaggerAddEnumDescriptions : ISchemaFilter
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="schema"></param>
        /// <param name="context"></param>
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (schema?.Properties == null || context.Type == null)
                return;
            if (context.Type.IsEnum)
            {

                List<string> enumDescriptions = new List<string>();
                Enum.GetNames(context.Type)
                    .ToList()
                    .ForEach(name =>
                    {
                        var e = (Enum)Enum.Parse(context.Type, name);
                        enumDescriptions.Add($"{name}({e.ToDescriptionName()})={Convert.ToInt64(Enum.Parse(context.Type, name))}");
                    });
                schema.Description += string.Join(",", enumDescriptions);
            }
        }
    }
}
