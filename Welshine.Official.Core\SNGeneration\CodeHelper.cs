﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Welshine.Official.Core.SNGeneration
{
    /// <summary>
    /// 生成编码
    /// </summary>
    public class CodeHelper
    {
        /// <summary>
        /// 获取编码
        /// </summary>
        /// <param name="prefix"></param>
        /// <param name="module"></param>
        /// <param name="len"></param>
        /// <param name="format"></param>
        /// <returns></returns>
        public static async Task<string> GetCode(string prefix, int len = 4)
        {
            var sum = await FreeRedisHelper.DefaultInstance.IncrAsync("snId:"+prefix);
            return prefix + sum.ToString().PadLeft(len, '0');
        }

        /// <summary>
        /// 获取展会活动券码
        /// </summary>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="prefix">券码前缀</param>
        /// <returns></returns>
        public static async Task<string> GetExhibitionActivityCode(long exhibitionActivityId, string prefix)
        {
            var sum = await FreeRedisHelper.DefaultInstance.IncrAsync("exhibitionActivityCode:" + exhibitionActivityId);
            return prefix + sum.ToString().PadLeft(6, '0');
        }

    }
}
