﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Linq;
namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 枚举状态 比如修改状态 
    /// 0草稿 1上线 2下线 不能修改为0 就该这么写 [EnumExcludeDataType(typeof(EnumModelStatus),excludeEnum:new int[1] { (int)EnumModelStatus.Default},ErrorMessage ="状态出错")]
    /// </summary>
    public class EnumExcludeDataTypeAttribute: DataTypeAttribute
    {
        /// <summary>
        /// 
        /// </summary>
       public Type EnumType { get; set; }
        /// <summary>
        /// 
        /// </summary>
       public int[] ExcludeEnum { get; set; }
        /// <summary>
        /// 枚举状态排除
        /// </summary>
        /// <param name="enumType"></param>
        /// <param name="excludeEnum"></param>
        public EnumExcludeDataTypeAttribute(Type enumType,int[] excludeEnum) : base("Enumeration")
        {
            if (enumType == null)
            {
                throw new InvalidOperationException("Type cannot be null");
            }
            if (!enumType.IsEnum)
            {
                throw new InvalidOperationException("Type must be an enum");
            }
            EnumType =enumType; 
            ExcludeEnum = excludeEnum;
        }
        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public override bool IsValid(object value)
        {
            if (value == null) return true;
            if (!Enum.IsDefined(this.EnumType, value))
            {
                return false;
            }
            if (ExcludeEnum.Contains((int)value))
            {
                return false;
            }
            return true;
        }
   }
}
