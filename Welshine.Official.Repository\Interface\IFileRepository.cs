﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO;

namespace Welshine.Official.Repository.Interface
{
    public interface IFileRepository
    {
        /// <summary>
        /// 添加关联
        /// </summary>
        /// <param name="relationList"></param>
        /// <returns></returns>
        Task SaveFileRelateList(List<FilesRelation> relationList);

        /// <summary>
        /// 关联
        /// </summary>
        /// <returns></returns>
        Task<List<FilesRelation>> GetFileRelateList(Expression<Func<FilesRelation, bool>> where);

        /// <summary>
        /// 添加文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        Task AddFile(Files file);

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="idList">文件Id</param>
        /// <returns></returns>
        Task<List<Files>> GetList(List<string> idList);

        /// <summary>
        /// 判断文件是否存在
        /// </summary>
        /// <param name="fileId">文件Id</param>
        /// <returns></returns>
        Task<bool> Exists(string fileId);

        /// <summary>
        /// 判断文件是否存在
        /// </summary>
        /// <param name="md5">文件MD5值</param>
        /// <returns></returns>
        Task<Files> ExistsByMD5(string md5);

        /// <summary>
        /// 文件列表
        /// </summary>
        /// <param name="idList">关联Id</param>
        /// <param name="relationType">枚举</param>
        /// <returns></returns>
        Task<List<FileDto>> GetFileList(List<long> idList, EnumRelationType? relationType = null);

        /// <summary>
        /// 删除文件关联
        /// </summary>
        /// <param name="list"></param>
        /// <param name="storeDetailPicture"></param>
        /// <param name="userName"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task DeleteFileRelateList(List<long> list, EnumRelationType storeDetailPicture, string userName, string userId);
    }
}
