﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// sql 防注入
    /// </summary>
    public class SqlSafeAttribute : ValidationAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                var valueAsString = value.ToString();
                if (!string.IsNullOrWhiteSpace(valueAsString))
                {

                    if (CheckBadStr(valueAsString))
                    {
                        var errorMessage = "文本有危险字符，请重新填写.";
                        return new ValidationResult(errorMessage);
                    }

                }
            }
            return ValidationResult.Success;
        }

        /// <summary>
        /// 错误字符
        /// </summary>
        private static List<string> bidStrlist = new List<string>(){
                    "net user","exec","net localgroup","select","asc","char","mid","insert","order","delete"
                    ,"truncate","xp_cmdshell","delete","like"
                };
        /// <summary>
        /// 检查错误字符
        /// </summary>
        /// <param name="strString"></param>
        /// <returns></returns>
        public static bool CheckBadStr(string strString)
        {
            bool outValue = false;
            if (strString != null && strString.Trim().Length > 0)
            {
                string tempStr = strString.ToLower();
                return bidStrlist.Any(x => tempStr.Contains(x));
            }
            return outValue;
        }
    }
}
