﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 产品详情条目
    /// </summary>
    public class ProductDetailResponse
    {
        /// <summary>
        /// 产品Id
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        /// 产品编码
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品略缩图
        /// </summary>
        public FileResponse Thumbnail { get; set; }

        /// <summary>
        /// 详情图
        /// </summary>
        public List<FileResponse> DetailPicture { get; set; }
    }
}
