﻿
using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Collections.Generic;
using System.Linq;

namespace Welshine.Official.Core.Filters
{
    /// <summary>
    ///  Check Bearer
    /// </summary>
    public class AuthorizeCheckBearerOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            var hasAuthorize = context.MethodInfo.DeclaringType != null
                && (context.MethodInfo.DeclaringType.GetCustomAttributes(true).OfType<AuthorizeAttribute>().Any()
                     || context.MethodInfo.GetCustomAttributes(true).OfType<AuthorizeAttribute>().Any())
                 && !(context.MethodInfo.DeclaringType.GetCustomAttributes(true).OfType<AllowAnonymousAttribute>().Any()
                     || context.MethodInfo.GetCustomAttributes(true).OfType<AllowAnonymousAttribute>().Any());

            if (hasAuthorize)
            {
                operation.Responses.Add("401", new OpenApiResponse { Description = "Unauthorized" });
                operation.Responses.Add("403", new OpenApiResponse { Description = "Forbidden" });

                operation.Security = new List<OpenApiSecurityRequirement>
                {
                    new OpenApiSecurityRequirement
                    {
                        [
                            new OpenApiSecurityScheme{
                            Reference = new OpenApiReference{
                                Type= ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        }
                        ] = new string[]{ }
                    }
                };

            }
        }
    }
}
