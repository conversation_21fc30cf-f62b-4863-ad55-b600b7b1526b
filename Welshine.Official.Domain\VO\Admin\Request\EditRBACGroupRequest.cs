﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class EditRBACGroupRequest
    {
        /// <summary>
        /// 权限分组id
        /// </summary>
        [Required(ErrorMessage = "分组id不能为必填")]
        public long? GroupId { get; set; }
        /// <summary>
        /// 分组名称
        /// </summary>
        [Required(ErrorMessage = "分组名称为必填")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z]{1,10}$", ErrorMessage = "分组名称应为1-10个中文字或字母")]
        public string GroupName { get; set; }
        /// <summary>
        /// 分组描述
        /// </summary>
        [Required(ErrorMessage = "分组描述为必填")]
        [StringLength(30, ErrorMessage = "分组描述内容长度应为1-30", MinimumLength = 1)]
        public string Description { get; set; }
        /// <summary>
        /// 权限菜单列表
        /// </summary>
        public List<string> RBACMenuList { get; set; }
        /// <summary>
        /// 权限url列表
        /// </summary>
        public List<string> RBACUrlList { get; set; }
    }
}
