﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class AddCNCategoryProductRequest
    {
        /// <summary>
        /// 分类Id
        /// </summary>
        [Required(ErrorMessage = "分类Id是必填项")]
        [Range(0, long.MaxValue, ErrorMessage = "分类Id参数错误")]
        public long? CategoryId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Required(ErrorMessage = "产品名称是必填项")]
        [StringLength(100, ErrorMessage = "产品名称长度不符", MinimumLength = 1)]
        public string ProductName { get; set; }

        /// <summary>
        /// 商品图片Id
        /// </summary>
        [Required(ErrorMessage = "商品图片Id是必填项")]
        [ArrayRequired(ErrorMessage = "商品图片Id是必填项")]
        [ArrayMaxLength(20, ErrorMessage = "商品图片Id限制最多20张")]
        public List<string> ProductImgId { get; set; }

        /// <summary>
        /// 商品介绍
        /// </summary>
        [StringLength(2000, ErrorMessage = "商品介绍长度不符", MinimumLength = 1)]
        public string ProductIntroduce { get; set; }

        /// <summary>
        /// 角标图片Id
        /// </summary>
        //[Required(ErrorMessage = "角标图片Id是必填项")]
        //[ArrayRequired(ErrorMessage = "角标图片Id是必填项")]
        [ArrayMaxLength(10, ErrorMessage = "角标图片Id限制最多10张")]
        public List<string> CornerMarkImgId { get; set; }

        /// <summary>
        /// 商品规格
        /// </summary>
        [Required(ErrorMessage = "商品规格是必填项")]
        [ArrayRequired(ErrorMessage = "商品规格是必填项")]
        //[ArrayMaxLength(5, ErrorMessage = "商品规格限制最多5个")]
        public List<ProductIntroduce> IntroduceList { get; set; }
    }
}
