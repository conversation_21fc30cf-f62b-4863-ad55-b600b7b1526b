﻿using CsvHelper.Configuration.Attributes;
using System;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class ExhibitionActivityParticipantResponse
    {
        /// <summary>
        /// 是否现场客户:0->否;1->是;
        /// </summary>
        [Name("现场客户")]
        public string IsScene { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [Name("姓名")]
        public string Name { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [Name("电话")]
        public string Mobile { get; set; }

        /// <summary>
        /// 类型:0->经销;1->终端;
        /// </summary>
        [Name("类型")]
        public string Type { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [Name("公司名称")]
        public string Company { get; set; }

        /// <summary>
        /// 省名称
        /// </summary>
        [Name("省份")]
        public string ProvinceName { get; set; }

        /// <summary>
        /// 市名称
        /// </summary>
        [Name("城市")]
        public string CityName { get; set; }

        /// <summary>
        /// 券码
        /// </summary>
        [Name("券码")]
        public string Code { get; set; }

        /// <summary>
        /// 参与时间
        /// </summary>
        [Name("参与时间")]
        public DateTime CreatedTime { get; set; }

    }
}
