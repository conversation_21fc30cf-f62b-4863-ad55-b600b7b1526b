﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.App.Request
{
    public class ParticipantCheckInRequest
    {
        /// <summary>
        /// 展会活动Id
        /// </summary>
        [Required(ErrorMessage = "展会活动Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "展会活动Id参数错误")]
        public long ExhibitionActivityId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [Required(ErrorMessage = "姓名必填,请完善")]
        [StringLength(5, ErrorMessage = "姓名长度不符", MinimumLength = 1)]
        public string Name { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [Required(ErrorMessage = "电话必填,请完善")]
        [StringLength(11, ErrorMessage = "电话长度不符", MinimumLength = 1)]
        public string Mobile { get; set; }

        /// <summary>
        /// 类型: 0->经销;1->终端;
        /// </summary>
        [Required(ErrorMessage = "类型必填,请完善")]
        [Range(0, 1, ErrorMessage = "类型值错误")]
        public int type { get; set; }

        /// <summary>
        /// 公司名
        /// </summary>
        [Required(ErrorMessage = "公司名必填,请完善")]
        [StringLength(20, ErrorMessage = "公司名长度不符", MinimumLength = 1)]
        public string Company { get; set; }

        /// <summary>
        /// 省份Id
        /// </summary>
        //[Required(ErrorMessage = "省份Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "省份参数错误")]
        public long? ProvinceId { get; set; }

        /// <summary>
        /// 市id
        /// </summary>
        //[Required(ErrorMessage = "市id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "市参数错误")]
        public long? CityId { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        [Required(ErrorMessage = "经度是必填项,请填写后再提交")]
        [Range(-180.0d, 180.0d, ErrorMessage = "经度错误")]
        public decimal Longitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        [Required(ErrorMessage = "纬度是必填项,请填写后再提交")]
        [Range(-90.0d, 90.0d, ErrorMessage = "纬度错误")]
        public decimal Latitude { get; set; }

        /// <summary>
        /// 动态令牌
        /// </summary>
        [Required(ErrorMessage = "动态令牌是必填项,请完善", AllowEmptyStrings = false)]
        public string Code { get; set; }
    }
}
