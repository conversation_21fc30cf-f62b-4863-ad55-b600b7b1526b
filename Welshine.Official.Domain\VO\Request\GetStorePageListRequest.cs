﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Request
{
    /// <summary>
    /// 
    /// </summary>
    public class GetStorePageListRequest
    {
        /// <summary>
        /// 门店名
        /// </summary>
        [StringLength(40, ErrorMessage = "门店名长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[\u4e00-\u9fa5_a-zA-Z0-9]+$", ErrorMessage = "门店名格式不正确!")]
        public string StoreName { get; set; }
    }
}
