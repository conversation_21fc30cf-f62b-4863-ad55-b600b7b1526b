﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Routing;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.RestfulApi.Helper;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 权限管理
    /// </summary>
    public class RBACController: BaseApiController
    {
        private readonly RBACGRoupService _rbacService = new RBACGRoupService();
        private readonly IEnumerable<EndpointDataSource> _endpointSources;
        public RBACController(IEnumerable<EndpointDataSource> endpointSources)
        { 
            _endpointSources = endpointSources;
        }
        /// <summary>
        /// 获取权限管理分组列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<PageRows<RBACGroupResponse>> GetRBACGroupPageList([FromBody]RequestPageModel request)
        {
            var data = _rbacService.GetRBACGroupPageList(request.PageIndex, request.PageSize);
            return Success("请求成功", data);
        }
        /// <summary>
        /// 新增权限管理分组
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse AddRBACGroup([FromBody] BaseRequest<AddRBACGroupRequest> request)
        {
            var currentUser = GetUserInfo();
            var body = request.Body;
            if (!body.RBACUrlList.ToHashSet().IsProperSubsetOf(ListAllEndpoints()))
            {
                return WebApiResponseHelp.Result(ErrorCode.RBACUrlListError.GetHashCode(), ErrorCode.RBACUrlListError.ToDescriptionName());
            }
            _rbacService.AddRBACGroup(body.GroupName, body.Description, body.RBACMenuList, body.RBACUrlList, currentUser);
            return Success("添加成功");
        }
        /// <summary>
        /// 查询权限分组明细
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<RBACGroupResponse> FindRBACGroupDetail([FromBody] BaseRequest<FindRBACGroupDetailRequest> request)
        {
            var data = _rbacService.FindRBACGroupDetail(request.Body.GroupId.Value);
            return Success("请求成功",data);
        }
        /// <summary>
        /// 编辑权限分组明细
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse EditRBACGroup([FromBody] BaseRequest<EditRBACGroupRequest> request)
        {
            var body = request.Body;
            if (!body.RBACUrlList.ToHashSet().IsProperSubsetOf(ListAllEndpoints()))
            {
                return WebApiResponseHelp.Result(ErrorCode.RBACUrlListError.GetHashCode(), ErrorCode.RBACUrlListError.ToDescriptionName());
            }
            _rbacService.EditGroup(body.GroupId.Value, body.GroupName, body.Description, body.RBACMenuList, body.RBACUrlList);
            return Success("修改成功");
        }
        /// <summary>
        /// 模糊搜索用户信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<PageRows<AccountResponse>> SearchAccount([FromBody] RequestPageModel<SearchUserRequest> request)
        {
            var accountService = new AccountService();
            var data = accountService.SearchAccount(request.RequestParams.UserName, request.PageIndex, request.PageSize);
            return Success("请求成功", data);
        }
        /// <summary>
        /// 添加权限分组用户
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse AddRBACGroupUsers([FromBody] BaseRequest<EditRBACGroupUserRequest> request)
        {
            var body = request.Body;
            _rbacService.AddGroupUsers(body.GroupId.Value, body.UserIds);
            return Success("请求成功");
        }
        /// <summary>
        /// 删除权限分组用户
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse RemoveRBACGroupUsers([FromBody] BaseRequest<EditRBACGroupUserRequest> request)
        {
            var body = request.Body;
            _rbacService.RemoveGroupUsers(body.GroupId.Value, body.UserIds);
            return Success("请求成功");
        }
        /// <summary>
        /// 验证url请求权限
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public BaseResponse<bool> RBACValidate([FromBody] BaseRequest<RBACValidateRequest> request)
        {
            var body = request.Body;
            var currentUser = GetUserInfo();
            var data = _rbacService.RBACValidate(currentUser.Roles.ToList(),body.RequestUrl);
            return Success("请求成功", data);
        }

        private List<string> ListAllEndpoints()
        {
            var endpoints = _endpointSources
                .SelectMany(es => es.Endpoints)
                .OfType<RouteEndpoint>();
            var output = endpoints.Select(
                e =>
                {
                    var controller = e.Metadata
                        .OfType<ControllerActionDescriptor>()
                        .FirstOrDefault();
                    var action = controller != null
                        ? $"{controller.ControllerName}.{controller.ActionName}"
                        : null;
                    var controllerMethod = controller != null
                        ? $"{controller.ControllerTypeInfo.FullName}:{controller.MethodInfo.Name}"
                        : null;
                    return new
                    {
                        Method = e.Metadata.OfType<HttpMethodMetadata>().FirstOrDefault()?.HttpMethods?[0],
                        Route = $"/{e.RoutePattern.RawText.TrimStart('/')}",
                        Action = action,
                        ControllerMethod = controllerMethod
                    };
                }
            );

            return output.Select(x=>x.Route).ToList();
        }
    }
}
