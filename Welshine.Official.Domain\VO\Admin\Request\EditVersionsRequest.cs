﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class EditVersionsRequest<T> : AddVersionsRequest<T>
    {
        /// <summary>
        /// 版本Id
        /// </summary>
        [Required(ErrorMessage = "版本Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "版本Id参数错误")]
        public long? VersionsId { get; set; }
    }
}
