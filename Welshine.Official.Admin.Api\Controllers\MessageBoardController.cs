﻿using CsvHelper;
using DocumentFormat.OpenXml.Office2010.CustomUI;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Org.BouncyCastle.Ocsp;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 留言区
    /// </summary>
    public class MessageBoardController : BaseApiController
    {
        private readonly IMessageBoardService _messageBoardService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="messageBoardService"></param>
        public MessageBoardController(IMessageBoardService messageBoardService)
        {
            _messageBoardService = messageBoardService;
        }

        /// <summary>
        /// 获取留言区列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<MessageBoardPageListResponse>>> GetMessageBoardPageList([FromBody] RequestPageModel<GetMessageBoardPageListRequest> request)
        {
            PageRows<MessageBoardPageListResponse> pageRows = null;
            try
            {
                pageRows = await _messageBoardService.GetMessageBoardPageList(request.RequestParams.Phone, request.RequestParams.CreatedTimeScope, request.OrderFile, request.SortType, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetMessageBoardPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 导出留言区列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ExportMessageBoardList([FromBody] BaseRequest<GetMessageBoardPageListRequest> request)
        {
            var basePath = "";
            var baseDirectory = AppContext.BaseDirectory;
            List<MessageBoardPageListResponse> list = null;
            try
            {
                list = await _messageBoardService.GetMessageBoardList(request.Body.Phone, request.Body.CreatedTimeScope);
                if (!list.Any())
                {
                    throw new BusinessException(ErrorCode.NoDataExport.ToDescriptionName(), ErrorCode.NoDataExport.GetHashCode());
                }

                basePath = baseDirectory + $"/ExportMessageBoard/{System.Guid.NewGuid().ToString("N")}".Replace('/', Path.DirectorySeparatorChar);
                var basePathCopy = baseDirectory + $"/官网留言_{DateTime.Now.ToString("yyyyMMdd")}".Replace('/', Path.DirectorySeparatorChar);//用于压缩
                if (!Directory.Exists(basePath))
                {
                    Directory.CreateDirectory(basePath);
                }

                List<Task<string>> tasks = new List<Task<string>>();
                int pageIndex = 0; //页码 0也就是第一条 
                int pageSize = 5000; //每页条数    

                while (pageIndex * pageSize < list.Count)
                {
                    var task = GetAllExportPath(basePath, pageIndex + 1, request);
                    var path = await task;
                    if (string.IsNullOrWhiteSpace(path)) break;

                    pageIndex++;
                }

                string zipName = $"官网留言_{DateTime.Now.ToString("yyyyMMdd")}.zip";
                string zipPath = baseDirectory + $"/{zipName}".Replace('/', Path.DirectorySeparatorChar);
                if (Directory.Exists(basePathCopy))
                {
                    Directory.Delete(basePathCopy, true);
                }
                Directory.Move(basePath, basePathCopy);
                ZipUtility zip = new ZipUtility();
                zip.ZipFileFromDirectory(basePathCopy, zipPath, 5);
                Directory.Delete(basePathCopy, true);
                if (Directory.Exists(basePath))
                {
                    Directory.Delete(basePath, true);
                }
                ///文件流
                var filepath = zipPath;
                var provider = new FileExtensionContentTypeProvider();
                var fileInfo = new FileInfo(filepath);
                var ext = fileInfo.Extension;
                new FileExtensionContentTypeProvider().Mappings.TryGetValue(ext, out var contentment);
                return File(await System.IO.File.ReadAllBytesAsync(filepath), contentment ?? "application/octet-stream", fileInfo.Name);
            }
            catch (BusinessException ex)
            {
                return Json(Failure(ex.Code, ex.Message, list));
            }
            catch (System.Exception ex)
            {
                Log.Error("ExportMessageBoardList " + ex.Message + ex.StackTrace);
                return Json(Failure(ErrorCode.SystemError, list));
            }
        }

        /// <summary>
        /// 获取导出csv地址
        /// </summary>
        /// <param name="basePath"></param>
        /// <param name="pageIndex"></param>
        /// <returns></returns>
        private async Task<string> GetAllExportPath(string basePath, int pageIndex, BaseRequest<GetMessageBoardPageListRequest> request)
        {
            var pageRows = (await _messageBoardService.GetMessageBoardPageList(request.Body.Phone, request.Body.CreatedTimeScope, "CreatedTime", SortType.Desc, pageIndex, pageSize: 5000)).Data;
            if (!pageRows.Any()) return null;

            foreach (var row in pageRows)
            {
                row.CreatedTime = row.CreatedTime.AddHours(8);
            }

            var csvFilePath = basePath + $"/官网留言_{DateTime.Now.ToString("yyyyMMdd")}_{pageIndex}.csv".Replace('/', Path.DirectorySeparatorChar);

            await using (var writer = new StreamWriter(csvFilePath, false, Encoding.UTF8))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    csv.WriteHeader<MessageBoardPageListResponse>();
                    await csv.NextRecordAsync();
                    await csv.WriteRecordsAsync(pageRows);
                }
            }

            return csvFilePath;
        }
    }
}
