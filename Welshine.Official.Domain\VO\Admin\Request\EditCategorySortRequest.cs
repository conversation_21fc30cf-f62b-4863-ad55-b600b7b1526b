﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 修改分类排序条目
    /// </summary>
    public class EditCategorySortRequest
    {
        /// <summary>
        /// 分类Id
        /// </summary>
        [Required(ErrorMessage = "分类Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "分类Id参数错误")]
        public long? CategoryId { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Required(ErrorMessage = "排序必填,请完善")]
        [Range(1, int.MaxValue, ErrorMessage = "排序参数错误")]
        public int? Sort { get; set; }
    }
}
