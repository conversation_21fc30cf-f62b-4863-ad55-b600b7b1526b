﻿using DTHY.Core.Repository;
using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.SNGeneration;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 展会仓储实现
    /// </summary>
    public class ExhibitionRepository : BaseRepository<Exhibition>, IExhibitionRepository
    {
        private readonly IFileRepository _fileRepository;

        public ExhibitionRepository(IFileRepository fileRepository)
        {
            _fileRepository = fileRepository;
        }

        /// <summary>
        /// 判断展会名称是否存在
        /// </summary>
        /// <param name="exhibitionName">展会名称</param>
        /// <returns></returns>
        public async Task<bool> ExistsExhibitionName(string exhibitionName)
        {
            return await DbScoped.SugarScope.Queryable<Exhibition>().AnyAsync(x => x.ExhibitionName == exhibitionName && !x.IsDeleted);
        }

        /// <summary>
        /// 判断展会名称是否存在
        /// </summary>
        /// <param name="exhibitionName">展会名称</param>
        /// <param name="exhibitionId">展会Id</param>
        /// <returns></returns>
        public async Task<bool> ExistsExhibitionName(string exhibitionName, long exhibitionId)
        {
            return await DbScoped.SugarScope.Queryable<Exhibition>().AnyAsync(x => x.ExhibitionName == exhibitionName && x.Id != exhibitionId && !x.IsDeleted);
        }

        /// <summary>
        /// 添加展会信息
        /// </summary>
        /// <param name="exhibition">展会信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> AddExhibition(Exhibition exhibition, string thumbnail, List<string> detailPicture)
        {
            exhibition.ExhibitionCode = await CodeHelper.GetCode("TCZH");

            var id = await DbScoped.SugarScope.Insertable(exhibition).ExecuteReturnBigIdentityAsync();
            if (id > 0)
            {
                #region 略缩图
                if (!string.IsNullOrWhiteSpace(thumbnail))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = thumbnail,
                        ObjectId = id,
                        TableEnum = EnumRelationType.ExhibitionThumbnail,
                        CreatedBy = exhibition.UpdatedBy,
                        CreatorId = exhibition.CreatorId,
                        UpdatedBy = exhibition.UpdatedBy,
                        ModifierId = exhibition.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                #region 详情图
                foreach (var item in detailPicture)
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = item,
                        ObjectId = id,
                        TableEnum = EnumRelationType.ExhibitionDetailPicture,
                        CreatedBy = exhibition.UpdatedBy,
                        CreatorId = exhibition.CreatorId,
                        UpdatedBy = exhibition.UpdatedBy,
                        ModifierId = exhibition.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取展会信息
        /// </summary>
        /// <param name="exhibitionId">展会Id</param>
        /// <returns></returns>
        public async Task<Exhibition> GetExhibition(long exhibitionId)
        {
            return await DbScoped.SugarScope.Queryable<Exhibition>().FirstAsync(x => x.Id == exhibitionId && !x.IsDeleted);
        }

        /// <summary>
        /// 修改展会信息
        /// </summary>
        /// <param name="exhibition">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        public async Task<bool> EditExhibition(Exhibition exhibition, string thumbnail, List<string> detailPicture)
        {
            var num = await DbScoped.SugarScope.Updateable(exhibition).ExecuteCommandAsync();
            if (num > 0)
            {
                //清空关联文件
                await DbScoped.SugarScope.Updateable<FilesRelation>()
                    .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = exhibition.ModifierId, UpdatedBy = exhibition.UpdatedBy, UpdatedTime = exhibition.UpdatedTime })
                    .Where(x => x.ObjectId == exhibition.Id && (x.TableEnum == EnumRelationType.ExhibitionDetailPicture || x.TableEnum == EnumRelationType.ExhibitionThumbnail))
                    .ExecuteCommandAsync();

                #region 略缩图
                if (!string.IsNullOrWhiteSpace(thumbnail))
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = thumbnail,
                        ObjectId = exhibition.Id,
                        TableEnum = EnumRelationType.ExhibitionThumbnail,
                        CreatedBy = exhibition.UpdatedBy,
                        CreatorId = exhibition.CreatorId,
                        UpdatedBy = exhibition.UpdatedBy,
                        ModifierId = exhibition.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                #region 详情图
                foreach (var item in detailPicture)
                {
                    FilesRelation fr = new FilesRelation()
                    {
                        FilesId = item,
                        ObjectId = exhibition.Id,
                        TableEnum = EnumRelationType.ExhibitionDetailPicture,
                        CreatedBy = exhibition.UpdatedBy,
                        CreatorId = exhibition.CreatorId,
                        UpdatedBy = exhibition.UpdatedBy,
                        ModifierId = exhibition.ModifierId
                    };
                    await DbScoped.SugarScope.Insertable(fr).ExecuteCommandAsync();
                }
                #endregion

                return true;
            }
            return false;
        }

        /// <summary>
        /// 删除展会信息
        /// </summary>
        /// <param name="exhibition">产品信息</param>
        /// <returns></returns>
        public async Task<bool> DeleteExhibition(Exhibition exhibition)
        {
            //删除产品
            await DbScoped.SugarScope.Updateable<Exhibition>()
                .SetColumns(x => new Exhibition() { IsDeleted = true, ModifierId = exhibition.ModifierId, UpdatedBy = exhibition.UpdatedBy, UpdatedTime = exhibition.UpdatedTime })
                .Where(x => x.Id == exhibition.Id)
                .ExecuteCommandAsync();

            //清空产品关联文件
            await DbScoped.SugarScope.Updateable<FilesRelation>()
                .SetColumns(x => new FilesRelation() { IsDeleted = true, ModifierId = exhibition.ModifierId, UpdatedBy = exhibition.UpdatedBy, UpdatedTime = exhibition.UpdatedTime })
                .Where(x => x.ObjectId == exhibition.Id && (x.TableEnum == EnumRelationType.ExhibitionDetailPicture || x.TableEnum == EnumRelationType.ExhibitionThumbnail))
                .ExecuteCommandAsync();

            return true;
        }

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <param name="exhibitionName">展会名称</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<ExhibitionListReponse>> GetExhibitionPageList(string exhibitionName, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<ExhibitionListReponse> result = new PageRows<ExhibitionListReponse>();

            result.Data = await DbScoped.SugarScope.Queryable<Exhibition>()
                .Where(x => !x.IsDeleted)
                .WhereIF(!string.IsNullOrWhiteSpace(exhibitionName), x => x.ExhibitionName.Contains(exhibitionName))
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .Select(x => new ExhibitionListReponse()
                {
                    ExhibitionId = x.Id,
                    ExhibitionCode = x.ExhibitionCode,
                    ExhibitionName = x.ExhibitionName,
                    ExhibitionTime = x.ExhibitionTime,
                    ExhibitionAddress = x.ExhibitionAddress,
                    UpdatedBy = x.UpdatedBy,
                    UpdatedTime = x.UpdatedTime
                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            result.Total = totalNumber;

            return result;
        }

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<WXExhibitionListReponse>> WX_GetExhibitionPageList(int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<WXExhibitionListReponse> result = new PageRows<WXExhibitionListReponse>();

            result.Data = await DbScoped.SugarScope.Queryable<Exhibition>()
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .Select(x => new WXExhibitionListReponse()
                {
                    ExhibitionId = x.Id,
                    ExhibitionName = x.ExhibitionName,
                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            result.Total = totalNumber;

            var ids = result.Data.Select(x => x.ExhibitionId).ToList();

            var files = await _fileRepository.GetFileList(ids, EnumRelationType.ExhibitionThumbnail);

            foreach (var item in result.Data)
            {
                var file = files.Where(x => x.ObjectId == item.ExhibitionId).FirstOrDefault();
                if (file != null)
                {
                    item.Thumbnail = file.Url;
                }
            }

            return result;
        }
    }
}
