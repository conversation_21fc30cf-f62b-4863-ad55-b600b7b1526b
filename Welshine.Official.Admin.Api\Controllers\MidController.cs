﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Dto;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 首页横幅
    /// </summary>
    public class MidController : BaseApiController
    {
        private readonly IVersionsService _versionsService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="versionsService"></param>
        public MidController(IVersionsService versionsService)
        {
            _versionsService = versionsService;
        }

        /// <summary>
        /// 添加首页横幅并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2205">版本标题已存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddMidVersions([FromBody] BaseRequest<AddVersionsRequest<MidData>> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                VersionsDto<MidData> versions = new VersionsDto<MidData>()
                {
                    VersionType = EnumVersionType.Mid.GetHashCode(),
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    Title = request.Body.Title,
                    Content = request.Body.Content,
                    Data = request.Body.Data,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.AddVersions(entity);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddMidVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取首页横幅详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetVersionsResponse<MidData>>> GetMidVersions([FromBody] BaseRequest<GetVersionsIdRequest> request)
        {
            GetVersionsResponse<MidData> result = null;
            try
            {
                result = await _versionsService.GetVersions<MidData>(EnumVersionType.Mid.GetHashCode(), request.Body.VersionsId.Value);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetMidVersions Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 修改首页横幅并提交审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <response code="2205">标题已存在</response>
        /// <response code="2208">状态为已发布,暂时无法编辑</response>
        /// <response code="2209">审核状态不为待提交或审核驳回,暂时无法编辑</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditMidVersions([FromBody] BaseRequest<EditVersionsRequest<MidData>> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                VersionsDto<MidData> versions = new VersionsDto<MidData>()
                {
                    Id = request.Body.VersionsId.Value,
                    VersionType = EnumVersionType.Mid.GetHashCode(),
                    ApproverStatus = request.Body.SaveType == 0 ? 0 : 1,
                    Title = request.Body.Title,
                    Content = request.Body.Content,
                    Data = request.Body.Data,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };
                if (request.Body.SaveType == 1)
                {
                    versions.SubmitApprover = user.UserName.ToString() ?? "system";
                    versions.SubmitApproverId = user.UserId;
                    versions.SubmitApprovalTime = DateTime.Now;
                }

                Versions entity = _mapper.Map<Versions>(versions);
                result = await _versionsService.EditVersions(versions);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditMidVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> MidExistsRelease()
        {
            bool result = false;
            try
            {
                result = await _versionsService.ExistsRelease(EnumVersionType.Mid.GetHashCode());
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("ExistsRelease Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 删除版本信息
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <response code="2210">状态为已发布,暂时无法删除</response>
        /// <response code="2211">审核状态为待审核,暂时无法删除</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> MidDeleteVersions([FromBody] BaseRequest<GetVersionsIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    VersionType = EnumVersionType.Mid.GetHashCode(),
                    Id = request.Body.VersionsId.Value,
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now,
                };

                result = await _versionsService.DeleteVersions(versions);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteBannerVersions Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        ///// <summary>
        ///// 提交版本信息审核
        ///// </summary>
        ///// <param name="request"></param>
        ///// <response code="2207">版本Id不存在</response>
        ///// <response code="2212">版本已发布,暂时无法提交审核</response>
        ///// <response code="2213">版本已提交审核或已审核通过,暂时无法提交审核</response>
        ///// <returns></returns>
        //[HttpPost]
        //public async Task<BaseResponse<bool>> SubmitMidVersionsApprove([FromBody] BaseRequest<GetVersionsIdRequest> request)
        //{
        //    bool result = false;
        //    try
        //    {
        //        var user = GetUserInfo();

        //        Versions versions = new Versions()
        //        {
        //            Id = request.Body.VersionsId,
        //            VersionType = EnumVersionType.Mid.GetHashCode(),
        //            ApproverStatus = 1,
        //            ModifierId = user.UserId,
        //            UpdatedBy = user.UserName.ToString() ?? "system",
        //            UpdatedTime = DateTime.Now,
        //            SubmitApprover = user.UserName.ToString() ?? "system",
        //            SubmitApproverId = user.UserId,
        //            SubmitApprovalTime = DateTime.Now
        //        };

        //        result = await _versionsService.SubmitVersionsApprove(versions);
        //        return Success("提交成功", result);
        //    }
        //    catch (BusinessException ex)
        //    {
        //        return Failure(ex.Code, ex.Message, result);
        //    }
        //    catch (System.Exception ex)
        //    {
        //        Log.Error("SubmitVersionsApprove Error {u}", ex.Message);
        //        return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
        //    }
        //}

        /// <summary>
        /// 版本信息审核
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2207">业务Id不存在</response>
        /// <response code="2214">审核状态不为待审核,暂时无法审核</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> MidVersionsApprove([FromBody] BaseRequest<BannerVersionsApproveRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Versions versions = new Versions()
                {
                    Id = request.Body.VersionsId.Value,
                    VersionType = EnumVersionType.Mid.GetHashCode(),
                    ApproverStatus = request.Body.ApproverStatus.Value,
                    ApprovalOpinion = request.Body.ApprovalOpinion,
                    ApproverId = user.UserId,
                    Approver = user.UserName.ToString() ?? "system",
                    ApprovalTime = DateTime.Now,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _versionsService.VersionsApprove(versions);
                return Success("审核成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("VersionsApprove Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取版本列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<VersionsListResponse>>> GetMidPageList([FromBody] RequestPageModel<VersionsListRequest> request)
        {
            PageRows<VersionsListResponse> pageRows = null;
            try
            {
                var user = GetUserInfo();

                pageRows = await _versionsService.GetVersionsPageList(user.UserId, EnumVersionType.Mid.GetHashCode(), request.RequestParams.VersionsTitle, request.RequestParams.ReleaseStatus, request.RequestParams.ApproverStatus, request.RequestParams.ReleaseTimeScope, request.OrderFile, request.SortType, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetVersionsPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        ///// <summary>
        ///// 版本发布
        ///// </summary>
        ///// <param name="request"></param>
        ///// <response code="2207">版本Id不存在</response>
        ///// <response code="2216">版本已发布</response>
        ///// <response code="2217">版本审核状态不为审核通过,暂时无法发布</response>
        ///// <returns></returns>
        //[HttpPost]
        //public async Task<BaseResponse<bool>> MidVersionsRelease([FromBody] BaseRequest<GetVersionsIdRequest> request)
        //{
        //    bool result = false;
        //    try
        //    {
        //        var user = GetUserInfo();

        //        Versions versions = new Versions()
        //        {
        //            VersionType = EnumVersionType.Mid.GetHashCode(),
        //            Id = request.Body.VersionsId,
        //            ModifierId = user.UserId,
        //            UpdatedBy = user.UserName.ToString() ?? "system",
        //            UpdatedTime = DateTime.Now,
        //        };

        //        result = await _versionsService.VersionsRelease(versions);
        //        return Success("发布成功", result);
        //    }
        //    catch (BusinessException ex)
        //    {
        //        return Failure(ex.Code, ex.Message, result);
        //    }
        //    catch (System.Exception ex)
        //    {
        //        Log.Error("VersionsRelease Error {u}", ex.Message);
        //        return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
        //    }
        //}

        /// <summary>
        /// 预览
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<VersionsListResponse>>> GetMidReleaseVersions()
        {
            List<VersionsListResponse> pageRows = null;
            try
            {
                pageRows = await _versionsService.GetAllReleaseVersions();

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetAllReleaseVersions " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

    }
}
