﻿using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 
    /// </summary>
    public class StringArrayAttribute : ValidationAttribute
    {
        private string _regex;
        private int _length;
        /// <summary>
        /// 
        /// </summary>
        public StringArrayAttribute(string regex, int length)
        {
            _regex = regex; _length = length;
        }
        /// <summary>
        /// 验证
        /// </summary>
        /// <param name="value"></param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                if (string.IsNullOrWhiteSpace(_regex)) return ValidationResult.Success;
                var at = value.GetType();
                if (!at.IsGenericType) return ValidationResult.Success;

                foreach (var v in value as dynamic)
                {
                    if (v == null || !System.Text.RegularExpressions.Regex.IsMatch(v.ToString(), _regex) || (_length > 0 && v.ToString().Length > _length))
                    {
                        return new ValidationResult(ErrorMessage);
                    }
                }

            }
            return ValidationResult.Success;
        }
    }
}
