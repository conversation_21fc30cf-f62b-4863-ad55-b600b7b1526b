﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 添加侧边浮层条目
    /// </summary>
    public class AddSidebarVersionsRequest
    {
        /// <summary>
        /// 保存类型: 0->保存; 1->保存提交审核
        /// </summary>
        [Required(ErrorMessage = "保存类型必填,请完善")]
        [Range(0, 1, ErrorMessage = "保存类型参数错误")]
        public int? SaveType { get; set; }

        /// <summary>
        /// 版本标题
        /// </summary>
        [Required(ErrorMessage = "版本标题是必填项")]
        [StringLength(30, ErrorMessage = "版本标题长度不符", MinimumLength = 1)]
        public string VersionsTitle { get; set; }

        /// <summary>
        /// 版本内容
        /// </summary>
        [Required(ErrorMessage = "版本内容是必填项")]
        [StringLength(50, ErrorMessage = "版本内容长度不符", MinimumLength = 1)]
        public string VersionsContent { get; set; }

        /// <summary>
        /// 中文
        /// </summary>
        [Required(ErrorMessage = "中文是必填项")]
        public List<SidebarItem> CNInfo { get; set; }

        /// <summary>
        /// 英文
        /// </summary>
        [Required(ErrorMessage = "英文是必填项")]
        public List<SidebarItem> ENInfo { get; set; }
    }

    public class SidebarItem
    {
        /// <summary>
        /// 排序
        /// </summary>
        [Required(ErrorMessage = "排序类型必填,请完善")]
        [Range(0, int.MaxValue, ErrorMessage = "排序参数错误")]
        public int? Sort { get; set; }

        /// <summary>
        /// 浮层logo
        /// </summary>
        [Required(ErrorMessage = "浮层logo必填,请完善")]
        [RegularExpression(@"^[\s\S]*\.(jpeg|jpg|png)$", ErrorMessage = "图片仅支持jpg，jpeg，png格式")]
        public string LogoImg { get; set; }

        /// <summary>
        /// 展示图片
        /// </summary>
        [RegularExpression(@"^[\s\S]*\.(jpeg|jpg|png)$", ErrorMessage = "图片仅支持jpg，jpeg，png格式")]
        public string ShowImg { get; set; }

        /// <summary>
        /// 展示文字
        /// </summary>
        [StringLength(100, ErrorMessage = "展示文字长度不符", MinimumLength = 1)]
        public string VersionsContent { get; set; }

    }
}
