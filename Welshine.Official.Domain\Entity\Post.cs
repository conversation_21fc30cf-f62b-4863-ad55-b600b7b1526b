﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    [SqlSugar.SugarTable("cms_post")]
    public class Post:BaseBigEntity
    {
        /// <summary>
        /// 部门id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "department_id")]
        public long DepartmentId { get; set; }
        /// <summary>
        /// 岗位名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "post_name")]
        public string PostName { get; set; }
    }
}
