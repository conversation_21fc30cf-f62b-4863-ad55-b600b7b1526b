-- 用户自定义标签模板表
CREATE TABLE `ext_label_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '微信OpenId',
  `json_content` longtext NOT NULL COMMENT '模板完整内容(JSON)',
  `label_thumbnail` longtext NOT NULL COMMENT '模板缩略图(base64)',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `creator_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `modifier_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户自定义标签模板表';

-- 已打印的标签表
CREATE TABLE `ext_printed_label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '微信OpenId',
  `json_content` longtext NOT NULL COMMENT '已打印的标签完整内容(JSON)',
  `label_thumbnail` longtext NOT NULL COMMENT '模板缩略图(base64)',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `creator_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `modifier_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='已打印的标签表';

