﻿using Welshine.Official.Core.Extensions;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Linq;

namespace Welshine.Official.Core.Filters
{
    /// <summary>
    /// Swagger文档枚举字段显示枚举属性和枚举值,以及枚举描述
    /// </summary>
    public class EnumSchemaFilter : ISchemaFilter
    {
        /// <summary>
        /// 实现接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="context"></param>

        public void Apply(OpenApiSchema model, SchemaFilterContext context)
        {
            if (context.Type.IsEnum)
            {
                model.Enum.Clear();
                Enum.GetNames(context.Type)
                    .ToList()
                    .ForEach(name =>
                    {
                        var e = (Enum)Enum.Parse(context.Type, name);
                        model.Enum.Add(new OpenApiString(
                            $"{name}({e.ToDescriptionName()})={Convert.ToInt64(Enum.Parse(context.Type, name))}"));
                    });
                if (model.Nullable == false)
                {
                    model.Description += " //如果有必填校验会失效,有默认值";
                }
            }
            //else if (context.MemberInfo?.GetCustomAttributes(typeof(RequiredAttribute), true).Length > 0
            //   && model.Nullable == false && ("integer" == model.Type || "number" == model.Type))
            //{
            //    model.Description += " //必填校验会失效,有默认值";
            //}
        }
    }
}
