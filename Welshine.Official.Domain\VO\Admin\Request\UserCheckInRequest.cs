﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 用户登记
    /// </summary>
    public class UserCheckInRequest
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        public CreatedTimeScope CreatedTimeScope { get; set; }
        /// <summary>
        /// 手机
        /// </summary>
        [StringLength(11, ErrorMessage = "手机长度不符", MinimumLength = 1)]
        [RegularExpression(@"^[0-9]+$", ErrorMessage = "手机格式错误")]
        public string Phone { get; set; }
    }
}
