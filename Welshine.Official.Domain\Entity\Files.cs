﻿namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 文件表
    /// </summary>
    [SqlSugar.SugarTable("cms_files")]
    public class Files: BaseEntity<string>
    {
        /// <summary>
        /// 基础路径
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "base_url")]
        public string BaseUrl { get; set; }

        /// <summary>
        /// 路径
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "url")]
        public string Url { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "user_id")]
        public string UserId { get; set; }

        /// <summary>
        /// 类型（0无 1 图片 2 文本）
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "file_type")]
        public int FileType { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "name")]
        public string Name { get; set; }

        /// <summary>
        /// 大小
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "length")]
        public long Length { get; set; }

        /// <summary>
        /// ContentType
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "content_type")]
        public string ContentType { get; set; }

        /// <summary>
        /// 文件MD5值
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "md5")]
        public string MD5 { get; set; }
    }
}
