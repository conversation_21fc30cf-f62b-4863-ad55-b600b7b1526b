using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.App.Request;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 打印机管理控制器
    /// </summary>
    public class PrinterManagerController : BaseApiController
    {
        private readonly IPrinterManagerService _printerManagerService;
        private readonly ILabelService _labelService;

        public PrinterManagerController(IPrinterManagerService printerManagerService, ILabelService labelService)
        {
            _printerManagerService = printerManagerService;
            _labelService = labelService;
        }

        /// <summary>
        /// 检查打印机设备是否在库
        /// </summary>
        /// <param name="request">检查设备请求</param>
        /// <response code="2001">参数错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<CheckPrinterDeviceResponse>> CheckPrinterDevice([FromBody] BaseRequest<CheckPrinterDeviceRequest> request)
        {
            CheckPrinterDeviceResponse result = null;
            try
            {
                result = await _printerManagerService.CheckPrinterDevice(request.Body.Model, request.Body.SN);
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("CheckPrinterDevice Error {u}", ex.Message);
                return Failure<CheckPrinterDeviceResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 解析Excel生成打印标签内容
        /// </summary>
        /// <param name="file">上传的excel文件流(xls/xlsx/csv)</param>
        /// <param name="templateLength">模版内容长度</param>
        /// <param name="hasHeader">是否包含标题栏</param>
        /// <returns>print_labels 数组</returns>
        [HttpPost]
        public async Task<BaseResponse<object>> PrintLabelByExcel([FromForm] IFormFile file, [FromForm] int templateLength, [FromForm] bool hasHeader)
        {
            if (file == null || file.Length == 0)
            {
                return Failure<object>(ErrorCode.FileFormatError, null);
            }
            if (templateLength <= 0)
            {
                return Failure<object>(ErrorCode.Param, null);
            }
            var allowType = new[] { ".xlsx", ".xls", ".csv" };
            if (!allowType.Any(x => file.FileName.ToLower().EndsWith(x)))
            {
                return Failure<object>(ErrorCode.FileFormatError, null);
            }

            try
            {
                var list = new List<string[]>();
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);
                    stream.Position = 0;
                    list = ExcelImportHelper.ParseFirstSheetRows(stream, file.FileName);
                }

                if (hasHeader && list.Count > 0)
                {
                    list.RemoveAt(0);
                }

                // 按模板长度切片
                var result = new
                {
                    print_labels = list
                        .Where(r => r != null && r.Length > 0)
                        .SelectMany(r =>
                        {
                            var items = new List<Dictionary<string, object>>();
                            for (int i = 0; i < r.Length; i += templateLength)
                            {
                                var slice = r.Skip(i).Take(templateLength).Select(x => x ?? string.Empty).ToArray();
                                if (slice.Length < templateLength)
                                {
                                    var padded = new string[templateLength];
                                    for (int k = 0; k < templateLength; k++)
                                    {
                                        padded[k] = k < slice.Length ? slice[k] : string.Empty;
                                    }
                                    slice = padded;
                                }
                                items.Add(new Dictionary<string, object> { { "label_contents", slice } });
                            }
                            return items;
                        }).ToList()
                };

                return Success<object>("解析成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure<object>(ex.Code, ex.Message, null);
            }
            catch (System.Exception ex)
            {
                Log.Error("PrintLabelByExcel Error {u}", ex.Message);
                return Failure<object>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", null);
            }
        }

        /// <summary>
        /// 保存标签模板
        /// </summary>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddLabel([FromBody] BaseRequest<AddLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.AddLabelAsync(request.Body.OpenId, request.Body.Json);
                return Success("保存成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 获取标签模板（分页）
        /// </summary>
        [HttpPost]
        public async Task<BaseResponse<PageRows<Domain.Entity.LabelTemplate>>> GetLabels([FromBody] RequestPageModel<GetLabelsRequest> request)
        {
            PageRows<Domain.Entity.LabelTemplate> pageRows = null;
            try
            {
                pageRows = await _labelService.GetLabelsAsync(request.RequestParams.OpenId, request.PageIndex, request.PageSize);
                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetLabels Error {u}", ex.Message);
                return Failure<PageRows<Domain.Entity.LabelTemplate>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }

        /// <summary>
        /// 删除标签模板
        /// </summary>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteLabel([FromBody] BaseRequest<DeleteLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.DeleteLabelAsync(request.Body.OpenId, request.Body.LabelId);
                return Success("删除成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 添加已打印的标签
        /// </summary>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddPrintedLabel([FromBody] BaseRequest<AddPrintedLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.AddPrintedLabelAsync(request.Body.OpenId, request.Body.Json);
                return Success("保存成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddPrintedLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 获取已打印的标签（分页）
        /// </summary>
        [HttpPost]
        public async Task<BaseResponse<PageRows<Domain.Entity.PrintedLabel>>> GetPrintedLabels([FromBody] RequestPageModel<GetPrintedLabelsRequest> request)
        {
            PageRows<Domain.Entity.PrintedLabel> pageRows = null;
            try
            {
                pageRows = await _labelService.GetPrintedLabelsAsync(request.RequestParams.OpenId, request.PageIndex, request.PageSize);
                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetPrintedLabels Error {u}", ex.Message);
                return Failure<PageRows<Domain.Entity.PrintedLabel>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }

        /// <summary>
        /// 删除已打印的标签
        /// </summary>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeletePrintedLabel([FromBody] BaseRequest<DeletePrintedLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.DeletePrintedLabelAsync(request.Body.OpenId, request.Body.LabelId);
                return Success("删除成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeletePrintedLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 获取系统标签模版分类列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<LabelTemplateCategoryResponse>>> GetSystemLabelTemplateCategories([FromBody] BaseRequest<GetSystemLabelTemplateCategoriesRequest> request)
        {
            List<LabelTemplateCategoryResponse> result = null;
            try
            {
                result = await _labelService.GetSystemLabelTemplateCategoriesAsync();
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetSystemLabelTemplateCategories Error {u}", ex.Message);
                return Failure<List<LabelTemplateCategoryResponse>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 通过分类获取系统标签模版列表（分页）
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<SystemLabelTemplateResponse>>> GetSystemLabelTemplatesByCategory([FromBody] RequestPageModel<GetSystemLabelTemplatesByCategoryRequest> request)
        {
            PageRows<SystemLabelTemplateResponse> pageRows = null;
            try
            {
                pageRows = await _labelService.GetSystemLabelTemplatesByCategoryAsync(request.RequestParams.CategoryId, request.PageIndex, request.PageSize);
                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetSystemLabelTemplatesByCategory Error {u}", ex.Message);
                return Failure<PageRows<SystemLabelTemplateResponse>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }

        /// <summary>
        /// 搜索系统标签模版列表（分页）
        /// </summary>
        /// <param name="request">搜索请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<SystemLabelTemplateResponse>>> SearchSystemLabelTemplates([FromBody] RequestPageModel<SearchSystemLabelTemplatesRequest> request)
        {
            PageRows<SystemLabelTemplateResponse> pageRows = null;
            try
            {
                pageRows = await _labelService.SearchSystemLabelTemplatesAsync(
                    request.RequestParams.TemplateName,
                    request.RequestParams.LabelSize,
                    request.RequestParams.SceneTags,
                    request.RequestParams.CategoryId,
                    request.PageIndex,
                    request.PageSize);
                return Success("搜索成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("SearchSystemLabelTemplates Error {u}", ex.Message);
                return Failure<PageRows<SystemLabelTemplateResponse>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }
    }
}
