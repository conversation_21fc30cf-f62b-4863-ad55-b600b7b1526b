﻿using AutoMapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class AreaService : IAreaService
    {
        IAreaRepository _areaRepository;
        IMapper _mapper;

        public AreaService(IAreaRepository areaRepository, IMapper mapper)
        {
            _areaRepository = areaRepository;
            _mapper = mapper;
        }

        public async Task<List<Area>> getAreaList()
        {
            List<Area> list = new List<Area>();
            string value = await FreeRedisHelper.DefaultInstance.GetAsync<string>("area");
            if (value == null)
            {
                list = await _areaRepository.GetEntityList(x => true);
                await FreeRedisHelper.DefaultInstance.SetAsync<string>("area", JsonConvert.SerializeObject(list));
            }
            else
            {
                list = JsonConvert.DeserializeObject<List<Area>>(value);
            }
            return list;
        }
    }
}
