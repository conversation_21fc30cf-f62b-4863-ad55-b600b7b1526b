-- 创建系统默认标签模版表
CREATE TABLE `ext_system_label_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(200) NOT NULL COMMENT '模版名称',
  `template_content` longtext NOT NULL COMMENT '模版内容(JSON)',
  `template_thumbnail` longtext DEFAULT NULL COMMENT '模版缩略图(base64)',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `template_description` varchar(500) DEFAULT NULL COMMENT '模版描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (0:禁用 1:启用)',
  `label_size` varchar(50) DEFAULT NULL COMMENT '标签尺寸(如:100x50mm)',
  `scene_tags` varchar(200) DEFAULT NULL COMMENT '适用场景标签(如:商品,价格,地址等)',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `creator_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `modifier_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 (0:未删除 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_template_name` (`template_name`) COMMENT '模版名称索引',
  KEY `idx_category_id` (`category_id`) COMMENT '分类ID索引',
  KEY `idx_sort` (`sort`) COMMENT '排序索引',
  KEY `idx_is_enabled` (`is_enabled`) COMMENT '启用状态索引',
  KEY `idx_created_time` (`created_time`) COMMENT '创建时间索引',
  CONSTRAINT `fk_system_template_category` FOREIGN KEY (`category_id`) REFERENCES `ext_label_template_category` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统默认标签模版表';

-- 插入示例系统模版数据
INSERT INTO `ext_system_label_template` 
(`template_name`, `template_content`, `category_id`, `template_description`, `sort`, `label_size`, `scene_tags`) VALUES

-- 通用标签模版
('简单文本标签', '{"width":100,"height":50,"elements":[{"type":"text","x":10,"y":25,"text":"示例文本","fontSize":12}]}', 1, '简单的文本标签模版', 1, '100x50mm', '通用,文本'),
('标题内容标签', '{"width":120,"height":60,"elements":[{"type":"text","x":10,"y":20,"text":"标题","fontSize":14,"bold":true},{"type":"text","x":10,"y":40,"text":"内容描述","fontSize":10}]}', 1, '包含标题和内容的标签模版', 2, '120x60mm', '通用,标题'),

-- 商品标签模版
('商品名称价格标签', '{"width":80,"height":40,"elements":[{"type":"text","x":5,"y":15,"text":"商品名称","fontSize":10},{"type":"text","x":5,"y":30,"text":"￥99.00","fontSize":12,"bold":true}]}', 2, '显示商品名称和价格的标签', 1, '80x40mm', '商品,价格'),
('商品条码标签', '{"width":100,"height":50,"elements":[{"type":"text","x":10,"y":15,"text":"商品名称","fontSize":10},{"type":"barcode","x":10,"y":25,"value":"1234567890","width":80,"height":20}]}', 2, '包含商品名称和条码的标签', 2, '100x50mm', '商品,条码'),

-- 地址标签模版
('收件人地址标签', '{"width":150,"height":80,"elements":[{"type":"text","x":10,"y":20,"text":"收件人：张三","fontSize":12},{"type":"text","x":10,"y":35,"text":"电话：13800138000","fontSize":10},{"type":"text","x":10,"y":50,"text":"地址：北京市朝阳区xxx街道","fontSize":10},{"type":"text","x":10,"y":65,"text":"邮编：100000","fontSize":10}]}', 3, '完整的收件人地址信息标签', 1, '150x80mm', '地址,收件人'),
('简化地址标签', '{"width":120,"height":50,"elements":[{"type":"text","x":10,"y":20,"text":"张三 13800138000","fontSize":10},{"type":"text","x":10,"y":35,"text":"北京市朝阳区xxx街道","fontSize":9}]}', 3, '简化版地址标签', 2, '120x50mm', '地址,简化'),

-- 价格标签模版
('促销价格标签', '{"width":60,"height":40,"elements":[{"type":"text","x":5,"y":15,"text":"原价：￥199","fontSize":8,"strikethrough":true},{"type":"text","x":5,"y":30,"text":"现价：￥99","fontSize":12,"bold":true,"color":"red"}]}', 4, '显示原价和现价的促销标签', 1, '60x40mm', '价格,促销'),
('会员价格标签', '{"width":80,"height":35,"elements":[{"type":"text","x":5,"y":15,"text":"会员价","fontSize":10,"color":"gold"},{"type":"text","x":5,"y":28,"text":"￥88.00","fontSize":14,"bold":true}]}', 4, '会员专享价格标签', 2, '80x35mm', '价格,会员'),

-- 条码标签模版
('标准条码标签', '{"width":100,"height":30,"elements":[{"type":"barcode","x":10,"y":5,"value":"1234567890123","width":80,"height":20,"showText":true}]}', 5, '标准的条码标签', 1, '100x30mm', '条码,标准'),
('二维码标签', '{"width":60,"height":60,"elements":[{"type":"qrcode","x":10,"y":10,"value":"https://example.com","size":40}]}', 5, '二维码标签模版', 2, '60x60mm', '二维码,链接');
