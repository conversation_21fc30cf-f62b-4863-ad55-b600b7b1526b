-- 创建系统默认标签模版表
CREATE TABLE `ext_system_label_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(200) NOT NULL COMMENT '模版名称',
  `template_content` longtext NOT NULL COMMENT '模版内容(JSON)',
  `template_thumbnail` longtext DEFAULT NULL COMMENT '模版缩略图(base64)',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `template_description` varchar(500) DEFAULT NULL COMMENT '模版描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (0:禁用 1:启用)',
  `label_size` varchar(50) DEFAULT NULL COMMENT '标签尺寸(如:100x50mm)',
  `scene_tags` varchar(200) DEFAULT NULL COMMENT '适用场景标签(如:商品,价格,地址等)',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `creator_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `modifier_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 (0:未删除 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_template_name` (`template_name`) COMMENT '模版名称索引',
  KEY `idx_category_id` (`category_id`) COMMENT '分类ID索引',
  KEY `idx_sort` (`sort`) COMMENT '排序索引',
  KEY `idx_is_enabled` (`is_enabled`) COMMENT '启用状态索引',
  KEY `idx_created_time` (`created_time`) COMMENT '创建时间索引',
  CONSTRAINT `fk_system_template_category` FOREIGN KEY (`category_id`) REFERENCES `ext_label_template_category` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统默认标签模版表';

-- 插入真实系统模版数据
INSERT INTO `ext_system_label_template`
(`template_name`, `template_content`, `category_id`, `template_description`, `sort`, `label_size`, `scene_tags`) VALUES

-- 存储标签模版
('存储标签1', '{"TemplateName":"存储标签1","Width":50,"Height":30,"Rotate":1,"Copies":1,"Density":3,"HorizontalNum":0,"VerticalNum":0,"PaperType":1,"Gap":2,"Speed":30,"FirstCut":1,"CutType":1,"DeviceSn":"T0145B","ImageWidth":50,"ImageHeight":30,"PreviewPath":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_001.png","DrawObjects":[{"AntiColor":false,"X":0,"Y":0,"Width":50,"Height":30,"Content":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_001.png","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"IMAGE","Orientation":0},{"AntiColor":false,"X":17,"Y":3,"Width":33,"Height":5,"Content":"品名","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":17,"Y":12,"Width":30,"Height":5,"Content":"操作人","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":17,"Y":21,"Width":33,"Height":5,"Content":"日期","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"DATE"}]}', 1, '存储标签模版，包含品名、操作人、日期字段', 1, '50x30mm', '存储,仓库,管理'),

('存储标签2', '{"TemplateName":"存储标签2","Width":50,"Height":30,"Rotate":1,"Copies":1,"Density":3,"HorizontalNum":0,"VerticalNum":0,"PaperType":1,"Gap":2,"Speed":30,"FirstCut":1,"CutType":1,"DeviceSn":"T0145B","ImageWidth":50,"ImageHeight":30,"PreviewPath":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_002B.png","DrawObjects":[{"AntiColor":false,"X":0,"Y":0,"Width":50,"Height":30,"Content":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_002B.png","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"IMAGE","Orientation":0},{"AntiColor":false,"X":21,"Y":2,"Width":33,"Height":4,"Content":"品名","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":21,"Y":9,"Width":30,"Height":4,"Content":"最高存储量","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":21,"Y":16,"Width":33,"Height":4,"Content":"最低存储量","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":21,"Y":23,"Width":33,"Height":4,"Content":"责任人","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6}]}', 1, '存储标签模版，包含品名、存储量、责任人字段', 2, '50x30mm', '存储,仓库,库存'),

-- 食品留样标签模版
('食品留样标签1', '{"TemplateName":"食品留样标签1","Width":50,"Height":30,"Rotate":1,"Copies":1,"Density":3,"HorizontalNum":0,"VerticalNum":0,"PaperType":1,"Gap":2,"Speed":30,"FirstCut":1,"CutType":1,"DeviceSn":"T0145B","ImageWidth":50,"ImageHeight":30,"PreviewPath":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_003.png","DrawObjects":[{"AntiColor":false,"X":0,"Y":0,"Width":50,"Height":30,"Content":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_003.png","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"IMAGE","Orientation":0},{"AntiColor":false,"X":16,"Y":5.5,"Width":33,"Height":4,"Content":"留样餐次","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"OPTION","InputRegex":"|早餐 |午餐 |晚餐"},{"AntiColor":false,"X":16,"Y":11.5,"Width":30,"Height":4,"Content":"留样品名","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":16,"Y":17.5,"Width":33,"Height":4,"Content":"留样时间","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"DATE","InputMax":6},{"AntiColor":false,"X":16,"Y":23.5,"Width":33,"Height":4,"Content":"留样人","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6}]}', 2, '食品留样标签，包含餐次、品名、时间、留样人', 1, '50x30mm', '食品,留样,餐饮'),

('食品留样标签2', '{"TemplateName":"食品留样标签2","Width":50,"Height":30,"Rotate":1,"Copies":1,"Density":3,"HorizontalNum":0,"VerticalNum":0,"PaperType":1,"Gap":2,"Speed":30,"FirstCut":1,"CutType":1,"DeviceSn":"T0145B","ImageWidth":50,"ImageHeight":30,"PreviewPath":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_004.png","DrawObjects":[{"AntiColor":false,"X":0,"Y":0,"Width":50,"Height":30,"Content":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_004.png","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"IMAGE","Orientation":0},{"AntiColor":false,"X":15,"Y":2,"Width":33,"Height":3.5,"Content":"餐次","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"3.5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"OPTION","InputRegex":"|早餐 |午餐 |晚餐"},{"AntiColor":false,"X":15,"Y":6.5,"Width":30,"Height":3.5,"Content":"菜品","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"3.5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":15,"Y":11,"Width":30,"Height":3.5,"Content":"重量","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"3.5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":15,"Y":15.5,"Width":33,"Height":3.5,"Content":"时间","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"3.5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"DATE_TIME","InputMax":6},{"AntiColor":false,"X":15,"Y":20,"Width":33,"Height":3.5,"Content":"留样人","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"3.5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":15,"Y":24.5,"Width":33,"Height":3.5,"Content":"制作人","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"3.5","Format":"TEXT","Orientation":0,"IsInput":true,"InputFormat":"TEXT","InputMax":6}]}', 2, '食品留样标签，包含餐次、菜品、重量、时间、留样人、制作人', 2, '50x30mm', '食品,留样,餐饮,制作'),

-- 物料/区域标签模版
('物料/区域标签1', '{"TemplateName":"物料/区域标签1","Width":50,"Height":30,"Rotate":1,"Copies":1,"Density":3,"HorizontalNum":0,"VerticalNum":0,"PaperType":1,"Gap":2,"Speed":30,"FirstCut":1,"CutType":1,"DeviceSn":"T0145B","ImageWidth":50,"ImageHeight":30,"PreviewPath":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/blank.png","Thumbnail":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_005_Thumbnail.png","DrawObjects":[{"AntiColor":false,"X":2,"Y":6,"Width":46,"Height":12,"Content":"中文内容","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"12","Format":"TEXT","Orientation":1,"IsInput":true,"InputFormat":"TEXT","InputMax":6},{"AntiColor":false,"X":2,"Y":20,"Width":46,"Height":4,"Content":"英文内容","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"TEXT","Orientation":1,"IsInput":true,"InputFormat":"TEXT","InputMax":30}]}', 1, '物料/区域标签，支持中英文双语显示', 3, '50x30mm', '物料,区域,标识,双语'),

('物料/区域标签2', '{"TemplateName":"物料/区域标签2","Width":50,"Height":30,"Rotate":1,"Copies":1,"Density":3,"HorizontalNum":0,"VerticalNum":0,"PaperType":1,"Gap":2,"Speed":30,"FirstCut":1,"CutType":1,"DeviceSn":"T0145B","ImageWidth":50,"ImageHeight":30,"PreviewPath":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_006.png","Thumbnail":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_006_Thumbnail.png","DrawObjects":[{"AntiColor":false,"X":0,"Y":0,"Width":50,"Height":30,"Content":"https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/printer/template/50x30_006.png","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"4","Format":"IMAGE","Orientation":0},{"AntiColor":false,"X":3,"Y":2,"Width":44,"Height":24,"Content":"中文内容","FontName":"HarmonyOS Sans SC","FontStyle":2,"FontSize":"11","Format":"TEXT","Orientation":1,"AutoReturn":true,"IsInput":true,"InputFormat":"TEXT","InputMax":8}]}', 1, '物料/区域标签，带边框的中文标识', 4, '50x30mm', '物料,区域,标识,边框');
