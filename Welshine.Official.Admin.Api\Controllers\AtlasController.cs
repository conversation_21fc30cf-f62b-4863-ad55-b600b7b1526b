﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 图册管理
    /// </summary>
    public class AtlasController : BaseApiController
    {
        private readonly IAtlasService _atlasService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="atlasService"></param>
        public AtlasController(IAtlasService atlasService)
        {
            _atlasService = atlasService;
        }

        /// <summary>
        /// 添加图册分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2012">图册名称已存在</response>
        /// <response code="2013">图册封面图不存在</response>
        /// <response code="2014">图册文件不存在</response>
        /// <response code="2202">文件格式错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddAtlas([FromBody] BaseRequest<AddAtlasRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Atlas exhibition = new Atlas()
                {
                    AtlasName = request.Body.AtlasName,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _atlasService.AddAtlas(exhibition, request.Body.Thumbnail, request.Body.FileId);
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddAtlas Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 修改图册分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2012">图册名称已存在</response>
        /// <response code="2013">图册封面图不存在</response>
        /// <response code="2014">图册文件不存在</response>
        /// <response code="2015">图册Id不存在</response>
        /// <response code="2202">文件格式错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditAtlas([FromBody] BaseRequest<EditAtlasRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Atlas exhibition = new Atlas()
                {
                    Id = request.Body.AtlasId,
                    AtlasName = request.Body.AtlasName,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _atlasService.EditAtlas(exhibition, request.Body.Thumbnail, request.Body.FileId);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditAtlas Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取图册详情
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2011">图册Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<AtlasDetailResponse>> GetAtlasById([FromBody] BaseRequest<AtlasIdRequest> request)
        {
            AtlasDetailResponse result = null;
            try
            {
                result = await _atlasService.GetAtlasById(request.Body.AtlasId);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetAtlasById Error {u}", ex.Message);
                return Failure<AtlasDetailResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 删除图册分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2011">图册Id不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteAtlas([FromBody] BaseRequest<AtlasIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Atlas exhibition = new Atlas()
                {
                    Id = request.Body.AtlasId,
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _atlasService.DeleteAtlas(exhibition);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteAtlas Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取图册列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<AtlasListReponse>>> GetAtlasPageList([FromBody] RequestPageModel<GetAtlasListRequest> request)
        {
            PageRows<AtlasListReponse> pageRows = null;
            try
            {
                pageRows = await _atlasService.GetAtlasPageList(request.RequestParams.AtlasName,request.RequestParams.CreatedTimeScope, request.PageIndex, request.PageSize);

                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetAtlasPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
    }
}
