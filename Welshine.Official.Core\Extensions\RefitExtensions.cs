﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
namespace Welshine.Official.Core.Extensions
{
    public static class RefitExtensions
    {
        /// <summary>
        /// var authToken = await Service.Authenticate(authPayload).GetAuthToken();
        /// </summary>
        /// <param name="task"></param>
        /// <returns></returns>
        public static async Task<string> GetAuthToken(this Task<HttpResponseMessage> task)
        {
            var response = await task.ConfigureAwait(false);
            string authToken = response.Headers.GetValues("AuthToken").FirstOrDefault();
            return await Task.FromResult(authToken);
        }
    }
}
