﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text.RegularExpressions;

namespace Welshine.Official.Core.Extensions
{
    /// <summary>
    /// String的扩展
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// 字符串转换整型，转换失败返回默认值
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="def">默认值为0，可选</param>
        /// <returns></returns>
        public static int ToInt(this string str, int def = 0)
        {
            int result = 0;
            if (int.TryParse(str, out result)) return result;
            else return def;
        }
        /// <summary>
        /// 字符串转换长整型，转换失败返回默认值
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="def">默认值为0，可选</param>
        /// <returns></returns>
        public static long ToLong(this string str, int def = 0)
        {
            long result = 0;
            if (long.TryParse(str, out result)) return result;
            else return def;
        }
        /// <summary>
        /// 字符串转换
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="def">默认值为0，可选</param>
        /// <returns></returns>
        public static decimal ToDecimal(this string str, decimal def = 0)
        {
            decimal result = 0;
            if (string.IsNullOrWhiteSpace(str)) return result;
            if (decimal.TryParse(str, out result)) return result;
            else return def;
        }
        /// <summary>
        /// 字符串转枚举
        /// </summary>
        /// <typeparam name="TEnum"></typeparam>
        /// <param name="str"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static TEnum ToEnum<TEnum>(this string str, TEnum defaultValue) where TEnum : struct
        {
            if (!string.IsNullOrEmpty(str))
            {
                Enum.TryParse(str, true, out defaultValue);//忽略大小写
            }
            return defaultValue;
        }

        /// <summary>
        /// MD5签名方法
        /// </summary>
        /// <param name="inputText"></param>
        /// <returns></returns>
        public static string ToMD5(this string inputText)
        {
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] fromData = System.Text.Encoding.UTF8.GetBytes(inputText);
            byte[] targetData = md5.ComputeHash(fromData);
            string byte2String = null;

            for (int i = 0; i < targetData.Length; i++)
            {
                byte2String += targetData[i].ToString("x2");
            }

            return byte2String;

        }
        /// <summary>
        /// 模板生成 {name} @name
        /// </summary>
        /// <typeparam name="T">对象</typeparam>
        /// <param name="content">模板</param>
        /// <param name="body">数据</param>
        /// <returns></returns>
        public static string Template<T>(this string content, T body) where T : class
        {
            if (body == null || string.IsNullOrWhiteSpace(content)) return content;
            Dictionary<string, string> values = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonConvert.SerializeObject(body));
            //var values = Newtonsoft.Json.Linq.JObject.FromObject(body).ToObject<Dictionary<string, object>>();
            return TemplateBody(content, values);
        }
        /// <summary>
        /// 模板生成 {name} @name
        /// </summary>
        /// <param name="content">模板</param>
        /// <param name="body">数据</param>
        /// <returns></returns>
        public static string TemplateBody(this string content, Dictionary<string, string> body)
        {
            if (body.Count == 0) return content;
            foreach (var item in body)
            {
                content = Regex.Replace(content, "@" + item.Key, item.Value ?? "", RegexOptions.IgnoreCase);
            }
            string pattern = @"\{(?<Keyword>[^}]+)\}";
            content = Regex.Replace(content, pattern, mt =>
             {
                 string memberName = mt.Groups["Keyword"].Value;
                 var key = body.Keys.FirstOrDefault(x => x.ToLower() == memberName.ToLower());
                 if (string.IsNullOrWhiteSpace(key)) return "";
                 return body[key];
             });
            return content;
        }

        /// <summary>
        /// 数据库Like查询的字符串转义
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string ToSqlEscape(this string value)
        {
            if (!string.IsNullOrEmpty(value))
                return value.Replace("_", @"\_").Replace("%", @"\%");
            return value;
        }
    }
}
