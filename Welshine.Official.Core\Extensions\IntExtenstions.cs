﻿using System;

namespace Welshine.Official.Core.Extensions
{
    /// <summary>
    /// Int 相关的扩展
    /// </summary>
    public static class IntExtenstions
    {
        /// <summary>
        /// 在value后添加小数
        /// </summary>
        /// <param name="value"></param>
        /// <param name="num"></param>
        /// <param name="len"></param>
        /// <returns></returns>
        public static decimal ToScore(this long value, long num, int len = 8)
        {
            if (num.ToString().Length > len)
            {
                len = num.ToString().Length;
            }

            var n = (((decimal)(num * Math.Pow(0.1, len))) * 10);

            return ((decimal)value) * 10 + n;
        }
    }
}
