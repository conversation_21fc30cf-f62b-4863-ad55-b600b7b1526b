using System;

namespace Welshine.Official.Domain.VO.App.Response
{
    /// <summary>
    /// 检查打印机设备响应
    /// </summary>
    public class CheckPrinterDeviceResponse
    {
        /// <summary>
        /// 打印机型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 设备SN码
        /// </summary>
        public string SN { get; set; }

        /// <summary>
        /// 设备是否在库 (true:在库 false:不在库)
        /// </summary>
        public bool IsInStock { get; set; }

        /// <summary>
        /// 首次查询时间
        /// </summary>
        public DateTime? FirstQueryTime { get; set; }

        /// <summary>
        /// 最后查询时间
        /// </summary>
        public DateTime? LastQueryTime { get; set; }

        /// <summary>
        /// 查询次数
        /// </summary>
        public int QueryCount { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remark { get; set; }
    }
}
