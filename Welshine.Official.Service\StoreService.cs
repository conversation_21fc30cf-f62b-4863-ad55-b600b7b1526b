﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.SNGeneration;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class StoreService : IStoreService
    {
        IStoreRepository _storeRepository;
        IFileRepository _fileRepository;
        /// <summary>
        /// 映射
        /// </summary>
        IMapper _mapper;

        public StoreService(IStoreRepository storeRepository, IMapper mapper, IFileRepository fileRepository)
        {
            _storeRepository = storeRepository;
            _mapper = mapper;
            _fileRepository = fileRepository;
        }
        /// <summary>
        /// 添加门店
        /// </summary>
        /// <param name="body"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<GetStoreDetailReponse> AddStore(AddStoreRequest body, string userName, string userId)
        {
            var po = _mapper.Map<Stores>(body);

            po.CreatedBy = userName; po.CreatedTime = DateTime.Now;
            po.UpdatedBy = userName; po.UpdatedTime = DateTime.Now;
            po.ModifierId = userId; po.CreatorId = userId;
            var fileList = await _fileRepository.GetList(
               new List<string>() { body.StoreRelation, body.StoreThumbnail }.Union(body.StoreUrlList).ToList());
            //校验
            ValidateImage(body.StoreUrlList, body.StoreRelation, body.StoreThumbnail, fileList);
            ///校验同名
            if (await _storeRepository.AnyEntity(x => x.StoreName == po.StoreName && x.IsDeleted == false))
            {
                throw new BusinessException(ErrorCode.NameExistError.ToDescriptionName(), ErrorCode.NameExistError.GetHashCode());
            }
            if (body.StoreShowHomepage)
            {
                ///校验首页显示
                if (await _storeRepository.AnyEntity(x => x.StoreShowHomepage == true && x.IsDeleted == false))
                {
                    throw new BusinessException(ErrorCode.StoreShowHomeError.ToDescriptionName(), ErrorCode.StoreShowHomeError.GetHashCode());
                }

            }

            po.StoreCode = await CodeHelper.GetCode("TCMD");

            //提交
            await _storeRepository.AddBigIdentity(po);
            var relateList = new List<FilesRelation>();
            foreach (var item in body.StoreUrlList)
            {
                var relatePo = new FilesRelation();
                relatePo.CreatedBy = userName; relatePo.CreatedTime = DateTime.Now;
                relatePo.UpdatedBy = userName; relatePo.UpdatedTime = DateTime.Now;
                relatePo.FilesId = item; relatePo.ObjectId = po.Id;
                relatePo.TableEnum = EnumRelationType.StoreDetailPicture;
                relatePo.ModifierId = userId; relatePo.CreatorId = userId;
                relateList.Add(relatePo);
            }
            await _fileRepository.SaveFileRelateList(relateList);
            return await Get(po.Id);
        }
        /// <summary>
        /// 校验图片
        /// </summary>
        /// <param name="storeUrlList"></param>
        /// <param name="relationFileId"></param>
        /// <param name="thumbnailFileId"></param>
        /// <param name="fileList"></param>
        /// <exception cref="BusinessException"></exception>
        private static void ValidateImage(List<string> storeUrlList,string relationFileId, 
            string thumbnailFileId, List<Files> fileList)
        {
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            var url = "";
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }
            foreach (var item in storeUrlList)
            {
                if (!fileList.Any(x => x.Id == item))
                {
                    throw new BusinessException($"详情图 文件Id:{item} 不存在", ErrorCode.ParamFileIdError.GetHashCode());
                }
                url = fileList.Find(x => x.Id == item).Url;
                if (!fileFormat.Any(u => url.ToLower().EndsWith(u)))
                {
                    throw new BusinessException($"详情图 文件Id:{item} 格式错误", ErrorCode.FileFormatError.GetHashCode());
                }
            }
            if (!string.IsNullOrWhiteSpace(relationFileId) &&!fileList.Any(x => x.Id == relationFileId))
            {
                throw new BusinessException($"联系 文件Id:{relationFileId} 不存在", ErrorCode.ParamFileIdError.GetHashCode());
            }
            if (!fileList.Any(x => x.Id == thumbnailFileId))
            {
                throw new BusinessException($"略缩图 文件Id:{thumbnailFileId} 不存在", ErrorCode.ParamFileIdError.GetHashCode());
            }
            url = fileList.Find(x => x.Id == relationFileId)?.Url;
            if (!string.IsNullOrWhiteSpace(relationFileId) && !fileFormat.Any(u => url.ToLower().EndsWith(u)))
            {
                throw new BusinessException($"联系 文件Id:{relationFileId} 格式错误", ErrorCode.FileFormatError.GetHashCode());
            }
            url = fileList.Find(x => x.Id == thumbnailFileId).Url;
            if (!fileFormat.Any(u => url.ToLower().EndsWith(u)))
            {
                throw new BusinessException($"联系 文件Id:{thumbnailFileId} 格式错误", ErrorCode.FileFormatError.GetHashCode());
            }
        }

        /// <summary>
        /// 编辑门店
        /// </summary>
        /// <param name="body"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<GetStoreDetailReponse> EditStore(EditStoreRequest body, string userName, string userId)
        {
            var store = await _storeRepository.GetEntity(x => x.Id == body.StoreId && x.IsDeleted == false);
            if (store == null)
            {
                return null;
            }
            var po = _mapper.Map(body, store);
            po.UpdatedBy = userName; po.UpdatedTime = DateTime.Now;
            po.ModifierId = userId; 
            var fileList = await _fileRepository.GetList(
               new List<string>() { body.StoreRelation, body.StoreThumbnail }.Union(body.StoreUrlList).ToList());
            //校验
            ValidateImage(body.StoreUrlList, body.StoreRelation, body.StoreThumbnail, fileList);
            if (await _storeRepository.AnyEntity(x => x.Id != body.StoreId &&x.StoreName == po.StoreName && x.IsDeleted == false))
            {
                throw new BusinessException(ErrorCode.NameExistError.ToDescriptionName(), ErrorCode.NameExistError.GetHashCode());
            }
            if (body.StoreShowHomepage)
            {
                ///校验首页显示
                if (await _storeRepository.AnyEntity(x => x.Id != body.StoreId && x.StoreShowHomepage == true && x.IsDeleted == false))
                {
                    throw new BusinessException(ErrorCode.StoreShowHomeError.ToDescriptionName(), ErrorCode.StoreShowHomeError.GetHashCode());
                }
            }
            //准备数据
            var detailUrlList = await _fileRepository.GetFileRelateList(x=>x.ObjectId==po.Id&&x.TableEnum==EnumRelationType.StoreDetailPicture&&x.IsDeleted==false);
            var oldStoreUrlList = detailUrlList.Select(x => x.FilesId).ToList();
            var deleteUrlList = oldStoreUrlList.Except(body.StoreUrlList).ToList();
            var addUrlList = body.StoreUrlList.Except(oldStoreUrlList).ToList();
            //提交
            await _storeRepository.Update(po);
          
            var relateList = new List<FilesRelation>();
            foreach (var item in deleteUrlList)
            {
                var relatePo = detailUrlList.Find(x => x.FilesId == item);
                if (relatePo == null)
                {
                    continue;
                }
                relatePo.UpdatedBy = userName; relatePo.UpdatedTime = DateTime.Now;
                relatePo.ModifierId = userId;
                relatePo.IsDeleted = true;
                relateList.Add(relatePo);
            }
            foreach (var item in addUrlList)
            {
                var relatePo = new FilesRelation();
                relatePo.CreatedBy = userName; relatePo.CreatedTime = DateTime.Now;
                relatePo.UpdatedBy = userName; relatePo.UpdatedTime = DateTime.Now;
                relatePo.FilesId = item; relatePo.ObjectId = po.Id;
                relatePo.TableEnum = EnumRelationType.StoreDetailPicture;
                relatePo.ModifierId = userId;relatePo.CreatorId = userId;
                relateList.Add(relatePo);
            }
            await _fileRepository.SaveFileRelateList(relateList);
            return await Get(po.Id);
        }
        public async Task<bool> Any(long storeId)
        {
            return await _storeRepository.AnyEntity(x => x.Id == storeId && x.IsDeleted == false);
        }
        /// <summary>
        /// 删除门店
        /// </summary>
        /// <param name="storeId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<bool> Delete(long storeId, string userName, string userId)
        {
            var store=await _storeRepository.GetEntity(x => x.Id == storeId&&x.IsDeleted==false);
            if (store == null)
            {
                return false;
            }
            store.IsDeleted = true;
            store.UpdatedBy = userName;
            store.UpdatedTime = DateTime.Now;
            store.ModifierId = userId; 
            var result= await _storeRepository.Update(store);
            if (result)
            {
                await _fileRepository.DeleteFileRelateList(new List<long>() { storeId }, 
                    EnumRelationType.StoreDetailPicture, userName, userId);
            }
            return result;
        }

       
        /// <summary>
        /// 详情
        /// </summary>
        /// <param name="storeId"></param>
        /// <returns></returns>
        public async Task<GetStoreDetailReponse> Get(long storeId)
        {
            var store = await _storeRepository.GetEntity(x => x.Id == storeId && x.IsDeleted == false);
            if (store == null)
            {
                return null;
            }
            var result = _mapper.Map<GetStoreDetailReponse>(store);
            var fileList = await _fileRepository.GetList(
                new List<string>() { result.StoreRelation, result.StoreThumbnail });
            var relationFile = fileList.Find(x => x.Id == result.StoreRelation);
            if (relationFile != null)
            {
                result.StoreRelationUrl = relationFile.BaseUrl+ relationFile.Url;
            }
            var thumbnailFile = fileList.Find(x => x.Id == result.StoreThumbnail);
            if (thumbnailFile != null)
            {
                result.StoreThumbnailUrl = thumbnailFile.BaseUrl + thumbnailFile.Url;
            }
            result.StoreDetailUrlList = await _fileRepository.GetFileList(
                new List<long>() { result.StoreId }, EnumRelationType.StoreDetailPicture);
            return result;
        }
        /// <summary>
        /// 默认门店
        /// </summary>
        /// <returns></returns>
        public async Task<GetStoreDetailReponse> GetDefaultStoreDetail()
        {
            var store = await _storeRepository.GetEntity(x => x.StoreShowHomepage == true && x.IsDeleted == false);
            if (store == null)
            {
                return null;
            }
            var result = _mapper.Map<GetStoreDetailReponse>(store);
            var fileList = await _fileRepository.GetList(
                new List<string>() { result.StoreRelation, result.StoreThumbnail });
            var relationFile = fileList.Find(x => x.Id == result.StoreRelation);
            if (relationFile != null)
            {
                result.StoreRelationUrl = relationFile.BaseUrl + relationFile.Url;
            }
            var thumbnailFile = fileList.Find(x => x.Id == result.StoreRelation);
            if (thumbnailFile != null)
            {
                result.StoreThumbnailUrl = thumbnailFile.BaseUrl + thumbnailFile.Url;
            }
            result.StoreDetailUrlList = await _fileRepository.GetFileList(
                new List<long>() { result.StoreId }, EnumRelationType.StoreDetailPicture);
            return result;
        }
        /// <summary>
        /// 门店列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <param name="storeName"></param>
        /// <returns></returns>
        public async Task<PageRows<GetStorePageListReponse>> GetStorePageList(int pageIndex, int pageSize, 
            string orderBy, string storeName)
        {
            Expression<Func<Stores, bool>> where = x => x.IsDeleted == false;
            if (!string.IsNullOrWhiteSpace(storeName))
            {
                where = where.And(x=>x.StoreName.Contains(storeName));
            }
            var storePageList = await _storeRepository.GetPageList(pageIndex, pageSize, 
                orderBy, where);
            var list = _mapper.Map<List<GetStorePageListReponse>>(storePageList.Data);
            if (list.Any())
            {
                //var idList = list.Select(x => x.StoreId).ToList();
                var fileIdList = list.Select(x => x.StoreRelation).Union(list.Select(x => x.StoreThumbnail)).ToList();
                var fileList = await _fileRepository.GetList(fileIdList);
                foreach (var item in list)
                {
                    var relationFile = fileList.Find(x => x.Id == item.StoreRelation);
                    if (relationFile != null)
                    {
                        item.StoreRelationUrl = relationFile.BaseUrl + relationFile.Url;
                    }
                    var thumbnailFile = fileList.Find(x => x.Id == item.StoreThumbnail);
                    if (thumbnailFile != null)
                    {
                        item.StoreThumbnailUrl = thumbnailFile.BaseUrl + thumbnailFile.Url;
                    }
                    
                }
            }
            return new PageRows<GetStorePageListReponse>()
            {
                Total= storePageList.Total,
                Data=list
            };
        }

        /// <summary>
        /// 门店列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <param name="storeName"></param>
        /// <returns></returns>
        public async Task<PageRows<GetStorePageListReponse>> GetWXStorePageList(int pageIndex, int pageSize,
            string orderBy, string storeName)
        {
            Expression<Func<Stores, bool>> where = x => x.IsDeleted == false && !x.StoreShowHomepage;
            if (!string.IsNullOrWhiteSpace(storeName))
            {
                where = where.And(x => x.StoreName.Contains(storeName));
            }
            var storePageList = await _storeRepository.GetPageList(pageIndex, pageSize,
                orderBy, where);
            var list = _mapper.Map<List<GetStorePageListReponse>>(storePageList.Data);
            if (list.Any())
            {
                var fileIdList = list.Select(x => x.StoreRelation).Union(list.Select(x => x.StoreThumbnail)).ToList();
                var fileList = await _fileRepository.GetList(fileIdList);
                foreach (var item in list)
                {
                    var relationFile = fileList.Find(x => x.Id == item.StoreRelation);
                    if (relationFile != null)
                    {
                        item.StoreRelationUrl = relationFile.BaseUrl + relationFile.Url;
                    }
                    var thumbnailFile = fileList.Find(x => x.Id == item.StoreThumbnail);
                    if (thumbnailFile != null)
                    {
                        item.StoreThumbnailUrl = thumbnailFile.BaseUrl + thumbnailFile.Url;
                    }

                }
            }
            return new PageRows<GetStorePageListReponse>()
            {
                Total = storePageList.Total,
                Data = list
            };
        }
    }
}
