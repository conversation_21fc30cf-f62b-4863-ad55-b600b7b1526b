﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;
using Welshine.Official.Domain.Enum;

namespace Welshine.Official.Domain.VO
{
    /// <summary>
    /// 
    /// </summary>
    public class FileDto
    {
        /// <summary>
        /// Desc:文件Id
        /// Default:
        /// Nullable:True
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "id")]
        public string FileId { get; set; }
        /// <summary>
        /// Desc:基础路径
        /// Default:
        /// Nullable:True
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "base_url")]
        public string BaseUrl { get; set; }
        /// <summary>
        /// Desc:路径
        /// Default:
        /// Nullable:True
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "url")]
        public string Url { get; set; }
        /// <summary>
        /// Desc:用户ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "user_id")]
        public string UserId { get; set; }
        /// <summary>
        /// Desc:类型（0无 1 图片 2 文本）
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "file_type")]
        public int FileType { get; set; }
        /// <summary>
        /// Desc:文件名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "name")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:大小
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "length")]
        public long Length { get; set; }
        /// <summary>
        /// Desc:ContentType
        /// Default:
        /// Nullable:True
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "content_type")]
        public string ContentType { get; set; }
        /// <summary>
        /// Desc:ObjectId
        /// Default:
        /// Nullable:True
        /// </summary>
        [SqlSugar.SugarColumn(IsIgnore =true)]
        public long ObjectId { get; set; }
        /// <summary>
        /// 文件创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_time", IsOnlyIgnoreUpdate = true)]
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 文件关联时间
        /// </summary>
        [SugarColumn(ColumnName = "updated_time")]
        public DateTime? UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 关联类型:
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "table_enum")]
        public EnumRelationType TableEnum { get; set; }
    }
}
