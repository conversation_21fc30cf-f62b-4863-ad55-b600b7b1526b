﻿using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    public class FileRepository : IFileRepository
    {
        /// <summary>
        /// 添加文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task AddFile(Files file)
        {
           await DbScoped.SugarScope.Insertable(file).ExecuteCommandAsync();
        }

        public async Task DeleteFileRelateList(List<long> list, EnumRelationType storeDetailPicture, string userName, string userId)
        {
            await DbScoped.SugarScope.Updateable<FilesRelation>().SetColumns(f=>new FilesRelation() { 
                UpdatedBy=userName,
                ModifierId=userId,
                IsDeleted=true,
                UpdatedTime=DateTime.Now
            }).Where(f => f.IsDeleted==false
            &&f.TableEnum== storeDetailPicture
            &&SqlFunc.ContainsArray(list,f.ObjectId)).ExecuteCommandAsync();
        }



        /// <summary>
        /// 判断文件是否存在
        /// </summary>
        /// <param name="fileId">文件Id</param>
        /// <returns></returns>
        public async Task<bool> Exists(string fileId)
        {
            return await DbScoped.SugarScope.Queryable<Files>().AnyAsync(x => x.Id == fileId);
        }

        /// <summary>
        /// 判断文件是否存在
        /// </summary>
        /// <param name="md5">文件MD5值</param>
        /// <returns></returns>
        public async Task<Files> ExistsByMD5(string md5)
        {
            return await DbScoped.SugarScope.Queryable<Files>().FirstAsync(x => x.MD5 == md5);
        }

        public async Task<List<FileDto>> GetFileList(List<long> idList, EnumRelationType? relationType = null)
        {
            return await DbScoped.SugarScope.Queryable<Files, FilesRelation>
                ((f, fr) => f.Id == fr.FilesId && f.IsDeleted == false && fr.IsDeleted == false)
                .Where((f, fr) => SqlFunc.ContainsArray(idList, fr.ObjectId))
                .WhereIF(relationType != null, (f, fr) => fr.TableEnum == relationType)
                .Select<FileDto>((f, fr) => new FileDto()
                {
                    FileId = f.Id,
                    UserId = fr.CreatedBy,
                    Url = f.BaseUrl + f.Url,
                    BaseUrl = f.BaseUrl,
                    ContentType = f.ContentType,
                    FileType = f.FileType,
                    Length = f.Length,
                    Name = f.Name,
                    ObjectId = fr.ObjectId,
                    TableEnum = fr.TableEnum
                })
                .ToListAsync();
        }

        public async Task<List<FilesRelation>> GetFileRelateList(Expression<Func<FilesRelation, bool>> where)
        {
            return await DbScoped.SugarScope.Queryable<FilesRelation>().Where(where).ToListAsync();
        }

        public async Task<List<Files>> GetList(List<string> idList)
        {
          return  await DbScoped.SugarScope.Queryable<Files>()
                .Where(x=>SqlFunc.ContainsArray(idList,x.Id)&&x.IsDeleted==false).ToListAsync();
        }
        public async Task SaveFileRelateList(List<FilesRelation> relationList)
        {
            if (relationList == null || !relationList.Any()) return;
            await DbScoped.SugarScope.Storageable(relationList).ExecuteCommandAsync();
        }
    }
}
