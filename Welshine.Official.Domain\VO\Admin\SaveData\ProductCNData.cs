﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;

namespace Welshine.Official.Domain.VO.Admin.SaveData
{
    /// <summary>
    /// 中文产品介绍数据
    /// </summary>
    public class ProductCNData
    {
        /// <summary>
        /// PC端
        /// </summary>
        [Required(ErrorMessage = "PC端是必填项")]
        public ProductCNItem PC { get; set; }

        /// <summary>
        /// 手机端
        /// </summary>
        [Required(ErrorMessage = "手机端是必填项")]
        public ProductCNItem Mobile { get; set; }
    }

    public class ProductCNItem
    {
        /// <summary>
        /// 详情图
        /// </summary>
        [Required(ErrorMessage = "详情图是必填项")]
        [ArrayRequired(ErrorMessage = "详情图是必填项")]
        [ArrayMaxLength(10, ErrorMessage = "详情图限制最多10张")]
        public List<SlideShow> Img { get; set; }
    }
}
