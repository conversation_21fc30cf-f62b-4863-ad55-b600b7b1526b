-- 修改标签模版表，添加模版名称和分类ID字段
ALTER TABLE `ext_label_template` 
ADD COLUMN `template_name` varchar(200) DEFAULT NULL COMMENT '模版名称' AFTER `label_thumbnail`,
ADD COLUMN `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID' AFTER `template_name`;

-- 添加索引
ALTER TABLE `ext_label_template` 
ADD INDEX `idx_template_name` (`template_name`) COMMENT '模版名称索引',
ADD INDEX `idx_category_id` (`category_id`) COMMENT '分类ID索引';

-- 添加外键约束（可选，根据实际需要决定是否添加）
-- ALTER TABLE `ext_label_template` 
-- ADD CONSTRAINT `fk_label_template_category` 
-- FOREIGN KEY (`category_id`) REFERENCES `ext_label_template_category` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
