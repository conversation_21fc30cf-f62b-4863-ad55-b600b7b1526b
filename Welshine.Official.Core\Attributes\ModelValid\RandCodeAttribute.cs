﻿using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{

    /// <summary>
    /// 验证码校验
    /// </summary>
    public class RandCodeAttribute : ValidationAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                var valueAsString = value.ToString();
                if (!string.IsNullOrWhiteSpace(valueAsString))
                {
                    string errorMessage = "";
                    if (!System.Text.RegularExpressions.Regex.IsMatch(valueAsString, @"^\d*$") || valueAsString.Length != 6)
                    {
                        errorMessage = "验证码格式不正确，只能输入数字且6位，请重新填写.";
                        return new ValidationResult(errorMessage);
                    }

                }
            }
            return ValidationResult.Success;
        }
    }
}
