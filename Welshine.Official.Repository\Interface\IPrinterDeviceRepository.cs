using DTHY.Core.Repository;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 打印机设备仓储接口
    /// </summary>
    public interface IPrinterDeviceRepository : IBaseRepository<PrinterDevice>
    {
        /// <summary>
        /// 根据型号和SN码查询设备
        /// </summary>
        /// <param name="model">打印机型号</param>
        /// <param name="sn">设备SN码</param>
        /// <returns></returns>
        Task<PrinterDevice> GetByModelAndSN(string model, string sn);

        /// <summary>
        /// 更新查询记录
        /// </summary>
        /// <param name="device">设备信息</param>
        /// <returns></returns>
        Task<bool> UpdateQueryRecord(PrinterDevice device);
    }
}
