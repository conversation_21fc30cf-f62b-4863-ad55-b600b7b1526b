﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class AlterPasswordRequest
    {
        /// <summary>
        /// 旧密码
        /// </summary>
        [Required(ErrorMessage ="旧密码为必填")]
        [RegularExpression(@"^(?=.*?[a-zA-Z])(?=.*?\d).*$", ErrorMessage = "旧密码须存在英文字母（大写或小写）和数字")]
        [StringLength(15, ErrorMessage = "旧密码长度必须为8-15位", MinimumLength = 8)]
        public string OldPassword { get; set; }
        /// <summary>
        /// 新密码
        /// </summary>
        [Required(ErrorMessage = "新密码为必填")]
        [RegularExpression(@"^(?=.*?[a-zA-Z])(?=.*?\d).*$", ErrorMessage = "新密码须存在英文字母（大写或小写）和数字")]
        [StringLength(15, ErrorMessage = "新密码长度必须为8-15位", MinimumLength = 8)]
        public string NewPassword { get; set; }
    }
}
