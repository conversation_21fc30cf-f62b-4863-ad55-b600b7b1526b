﻿using DocumentFormat.OpenXml.Office2021.DocumentTasks;
using DTHY.Core.Repository;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 分类产品仓储接口
    /// </summary>
    public interface ICategoryProductRepository : IBaseRepository<CategoryProduct>
    {
        /// <summary>
        /// 获取分类产品
        /// </summary>
        /// <param name="categoryProductId"></param>
        /// <returns></returns>
        Task<CategoryProduct> GetCategoryProductById(long categoryProductId);

        /// <summary>
        /// 获取当前分类商品最大排序
        /// </summary>
        /// <param name="categoryId">分类Id</param>
        /// <param name="productLanguage">语言: 0->英文; 1->中文</param>
        /// <returns></returns>
        Task<int> GetMaxSort(long categoryId, int productLanguage);

        /// <summary>
        /// 获取分类产品列表
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="productName">商品名称</param>
        /// <param name="ReleaseStatus">内容状态</param>
        /// <param name="times">创建时间</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PageRows<CategoryProductListResponse>> GetCategoryProductPageList(int? productLanguage, string productName, int? ReleaseStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 根据分类Id获取已发布的分类商品
        /// </summary>
        /// <param name="productLanguage">语言: 0->英文;1->中文;</param>
        /// <param name="categoryId">分类id</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PageRows<GetWebCategoryProductListResponse>> GetCategoryProductListByCategoryId(long? categoryId, int? productLanguage, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 分类是否有产品
        /// </summary>
        /// <param name="categoryId">分类Id</param>
        /// <returns></returns>
        Task<bool> ExistsCategoryProduct(long categoryId);

        /// <summary>
        /// 根据分类Id获取已发布的分类商品(并按sort排序)
        /// </summary>
        /// <param name="categoryId">分类id</param>
        /// <returns></returns>
        Task<GetCategoryProductListOrderSortResponse> GetCategoryProductListByCategoryId(long categoryId);
    }
}
