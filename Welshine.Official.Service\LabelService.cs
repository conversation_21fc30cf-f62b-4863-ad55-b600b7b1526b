using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;
using SqlSugar.IOC;
using SqlSugar;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 标签服务
    /// </summary>
    public class LabelService : ILabelService
    {
        private readonly ILabelTemplateRepository _labelTemplateRepository;
        private readonly IPrintedLabelRepository _printedLabelRepository;

        public LabelService(ILabelTemplateRepository labelTemplateRepository, IPrintedLabelRepository printedLabelRepository)
        {
            _labelTemplateRepository = labelTemplateRepository;
            _printedLabelRepository = printedLabelRepository;
        }

        public async Task<bool> AddLabelAsync(string openId, string jsonContent)
        {
            if (string.IsNullOrWhiteSpace(openId))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());
            if (string.IsNullOrWhiteSpace(jsonContent))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            var entity = new LabelTemplate
            {
                OpenId = openId,
                JsonContent = jsonContent
            };
            return await _labelTemplateRepository.Add(entity);
        }

        public async Task<PageRows<LabelTemplate>> GetLabelsAsync(string openId, int pageIndex, int pageSize)
        {
            RefAsync<int> total = 0;
            var list = await DbScoped.SugarScope.Queryable<LabelTemplate>()
                .Where(x => !x.IsDeleted && x.OpenId == openId)
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .ToPageListAsync(pageIndex, pageSize, total);
            return new PageRows<LabelTemplate> { Data = list, Total = total };
        }

        public async Task<bool> DeleteLabelAsync(string openId, long labelId)
        {
            var entity = await _labelTemplateRepository.QueryByID(labelId);
            if (entity == null || entity.IsDeleted || entity.OpenId != openId) return false;
            entity.IsDeleted = true;
            entity.UpdatedTime = DateTime.Now;
            return await _labelTemplateRepository.Update(entity);
        }

        public async Task<bool> AddPrintedLabelAsync(string openId, string jsonContent)
        {
            if (string.IsNullOrWhiteSpace(openId))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());
            if (string.IsNullOrWhiteSpace(jsonContent))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            var entity = new PrintedLabel
            {
                OpenId = openId,
                JsonContent = jsonContent
            };
            return await _printedLabelRepository.Add(entity);
        }

        public async Task<PageRows<PrintedLabel>> GetPrintedLabelsAsync(string openId, int pageIndex, int pageSize)
        {
            RefAsync<int> total = 0;
            var list = await DbScoped.SugarScope.Queryable<PrintedLabel>()
                .Where(x => !x.IsDeleted && x.OpenId == openId)
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .ToPageListAsync(pageIndex, pageSize, total);
            return new PageRows<PrintedLabel> { Data = list, Total = total };
        }

        public async Task<bool> DeletePrintedLabelAsync(string openId, long labelId)
        {
            var entity = await _printedLabelRepository.QueryByID(labelId);
            if (entity == null || entity.IsDeleted || entity.OpenId != openId) return false;
            entity.IsDeleted = true;
            entity.UpdatedTime = DateTime.Now;
            return await _printedLabelRepository.Update(entity);
        }

        /// <summary>
        /// 获取系统标签模版分类列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<LabelTemplateCategoryResponse>> GetSystemLabelTemplateCategoriesAsync()
        {
            var categories = await DbScoped.SugarScope.Queryable<LabelTemplateCategory>()
                .Where(x => !x.IsDeleted && x.IsEnabled)
                .OrderBy(x => x.Sort)
                .OrderBy(x => x.Id)
                .ToListAsync();

            return categories.Select(x => new LabelTemplateCategoryResponse
            {
                CategoryId = x.Id,
                CategoryName = x.CategoryName
            }).ToList();
        }

        /// <summary>
        /// 通过分类获取系统标签模版列表（分页）
        /// </summary>
        /// <param name="categoryId">分类ID，为空则获取全部系统模版</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns></returns>
        public async Task<PageRows<SystemLabelTemplateResponse>> GetSystemLabelTemplatesByCategoryAsync(long? categoryId, int pageIndex, int pageSize)
        {
            RefAsync<int> total = 0;
            var query = DbScoped.SugarScope.Queryable<SystemLabelTemplate>()
                .Where(x => !x.IsDeleted && x.IsEnabled);

            // 如果指定了分类ID，则按分类筛选
            if (categoryId.HasValue)
            {
                query = query.Where(x => x.CategoryId == categoryId.Value);
            }

            var list = await query
                .OrderBy(x => x.Sort)
                .OrderBy(x => x.Id)
                .ToPageListAsync(pageIndex, pageSize, total);

            var result = list.Select(x => new SystemLabelTemplateResponse
            {
                TemplateId = x.Id,
                TemplateName = x.TemplateName,
                TemplateContent = x.TemplateContent,
                TemplateThumbnail = x.TemplateThumbnail,
                TemplateDescription = x.TemplateDescription,
                LabelSize = x.LabelSize,
                SceneTags = x.SceneTags
            }).ToList();

            return new PageRows<SystemLabelTemplateResponse> { Data = result, Total = total };
        }
    }
}

