﻿using AutoMapper;
using DocumentFormat.OpenXml.Wordprocessing;
using SqlSugar.IOC;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.SNGeneration;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Repository;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;
using Ubiety.Dns.Core;

namespace Welshine.Official.Service
{
    public class ExhibitionActivityService : IExhibitionActivityService
    {
        IExhibitionActivityRepository _exhibitionActivityRepository;
        IFileRepository _fileRepository;
        IExhibitionActivityParticipantRepository _exhibitionActivityParticipantRepository;

        public ExhibitionActivityService(IExhibitionActivityRepository exhibitionActivityRepository, IFileRepository fileRepository, IExhibitionActivityParticipantRepository exhibitionActivityParticipantRepository)
        {
            _exhibitionActivityRepository = exhibitionActivityRepository;
            _fileRepository = fileRepository;
            _exhibitionActivityParticipantRepository = exhibitionActivityParticipantRepository;
        }

        /// <summary>
        /// 添加展会活动
        /// </summary>
        /// <param name="exhibitionActivityName">活动名称</param>
        /// <param name="exhibitionActivityStartTime">活动开始时间</param>
        /// <param name="exhibitionActivityEndTime">活动结束时间</param>
        /// <param name="exhibitionActivityAddress">活动地点</param>
        /// <param name="addressLongitude">经度</param>
        /// <param name="addressLatitude">纬度</param>
        /// <param name="codePrefix">券码前缀</param>
        /// <param name="entryPicture">活动入口图</param>
        /// <param name="participationPicture">活动参与图</param>
        /// <param name="codePicture">活动券码图</param>
        /// <param name="userName">创建人名称</param>
        /// <param name="userId">创建人id</param>
        /// <returns></returns>
        public async Task<bool> AddExhibitionActivity(string exhibitionActivityName, DateTime exhibitionActivityStartTime, DateTime exhibitionActivityEndTime, string exhibitionActivityAddress, decimal addressLongitude, decimal addressLatitude, string codePrefix, string entryPicture, string participationPicture, string codePicture, string userName, string userId)
        {
            var fileList = await _fileRepository.GetList(new List<string>() { entryPicture, participationPicture, codePicture });

            //校验
            ValidateImage(entryPicture, participationPicture, codePicture, fileList);

            ExhibitionActivity exhibitionActivity = new ExhibitionActivity();
            exhibitionActivity.ExhibitionActivityCode = await CodeHelper.GetCode("TCHD");
            exhibitionActivity.ExhibitionActivityName = exhibitionActivityName;
            exhibitionActivity.ExhibitionActivityStartTime = exhibitionActivityStartTime;
            exhibitionActivity.ExhibitionActivityEndTime = exhibitionActivityEndTime;
            exhibitionActivity.ExhibitionActivityAddress = exhibitionActivityAddress;
            exhibitionActivity.AddressLongitude = addressLongitude;
            exhibitionActivity.AddressLatitude = addressLatitude;
            exhibitionActivity.CodePrefix = codePrefix;
            exhibitionActivity.EntryPicture = entryPicture;
            exhibitionActivity.ParticipationPicture = participationPicture;
            exhibitionActivity.CodePicture = codePicture;
            exhibitionActivity.CreatedBy = userName;
            exhibitionActivity.CreatedTime = DateTime.Now;
            exhibitionActivity.UpdatedBy = userName;
            exhibitionActivity.UpdatedTime = DateTime.Now;
            exhibitionActivity.ModifierId = userId;
            exhibitionActivity.CreatorId = userId;

            bool result = await _exhibitionActivityRepository.AddBigIdentity(exhibitionActivity);
            await FreeRedisHelper.DefaultInstance.SetAsync<int>("exhibitionActivityCode:" + exhibitionActivity.Id, 0);
            return result;
        }

        /// <summary>
        /// 校验图片
        /// </summary>
        /// <param name="entryPicture">活动入口图</param>
        /// <param name="participationPicture">活动参与图</param>
        /// <param name="codePicture">活动券码图</param>
        /// <param name="fileList">文件列表</param>
        /// <exception cref="BusinessException"></exception>
        private static void ValidateImage(string entryPicture, string participationPicture, string codePicture, List<Files> fileList)
        {
            var fileFormat = new List<string>() { "jpg", "jpeg", "png" };
            var url = "";
            if (fileList.Any() && !fileList.All(x => fileFormat.Any(u => x.Url.ToLower().EndsWith(u))))
            {
                throw new BusinessException(ErrorCode.FileFormatError);
            }
            if (!string.IsNullOrWhiteSpace(entryPicture) && !fileList.Any(x => x.Id == entryPicture))
            {
                throw new BusinessException($"活动入口图:{entryPicture} 不存在", ErrorCode.ParamFileIdError.GetHashCode());
            }
            url = fileList.Find(x => x.Id == entryPicture)?.Url;
            if (!string.IsNullOrWhiteSpace(entryPicture) && !fileFormat.Any(u => url.ToLower().EndsWith(u)))
            {
                throw new BusinessException($"活动入口图:{entryPicture} 格式错误", ErrorCode.FileFormatError.GetHashCode());
            }
            if (!fileList.Any(x => x.Id == participationPicture))
            {
                throw new BusinessException($"活动参与图:{participationPicture} 不存在", ErrorCode.ParamFileIdError.GetHashCode());
            }
            url = fileList.Find(x => x.Id == participationPicture)?.Url;
            if (!string.IsNullOrWhiteSpace(participationPicture) && !fileFormat.Any(u => url.ToLower().EndsWith(u)))
            {
                throw new BusinessException($"活动参与图:{participationPicture} 格式错误", ErrorCode.FileFormatError.GetHashCode());
            }
            if (!fileList.Any(x => x.Id == codePicture))
            {
                throw new BusinessException($"活动券码图:{codePicture} 不存在", ErrorCode.ParamFileIdError.GetHashCode());
            }
            url = fileList.Find(x => x.Id == codePicture)?.Url;
            if (!string.IsNullOrWhiteSpace(codePicture) && !fileFormat.Any(u => url.ToLower().EndsWith(u)))
            {
                throw new BusinessException($"活动券码图:{codePicture} 格式错误", ErrorCode.FileFormatError.GetHashCode());
            }
        }

        /// <summary>
        /// 编辑展会活动
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <param name="exhibitionActivityName">活动名称</param>
        /// <param name="exhibitionActivityStartTime">活动开始时间</param>
        /// <param name="exhibitionActivityEndTime">活动结束时间</param>
        /// <param name="exhibitionActivityAddress">活动地点</param>
        /// <param name="addressLongitude">经度</param>
        /// <param name="addressLatitude">纬度</param>
        /// <param name="entryPicture">活动入口图</param>
        /// <param name="participationPicture">活动参与图</param>
        /// <param name="codePicture">活动券码图</param>
        /// <param name="userName">创建人名称</param>
        /// <param name="userId">创建人id</param>
        /// <returns></returns>
        public async Task<bool> EditExhibitionActivity(long exhibitionActivityId, string exhibitionActivityName, DateTime exhibitionActivityStartTime, DateTime exhibitionActivityEndTime, string exhibitionActivityAddress, decimal addressLongitude, decimal addressLatitude, string entryPicture, string participationPicture, string codePicture, string userName, string userId)
        {
            ExhibitionActivity exhibitionActivity = await _exhibitionActivityRepository.GetEntity(x => x.Id == exhibitionActivityId && x.IsDeleted == false);
            if (exhibitionActivity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityNoFoundError); 
            }
            if (exhibitionActivity.Status == 1) 
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityNotDisabledError);
            }

            var fileList = await _fileRepository.GetList(new List<string>() { entryPicture, participationPicture, codePicture });
            ValidateImage(entryPicture, participationPicture, codePicture, fileList);

            exhibitionActivity.ExhibitionActivityName = exhibitionActivityName;
            exhibitionActivity.ExhibitionActivityStartTime = exhibitionActivityStartTime;
            exhibitionActivity.ExhibitionActivityEndTime = exhibitionActivityEndTime;
            exhibitionActivity.ExhibitionActivityAddress = exhibitionActivityAddress;
            exhibitionActivity.AddressLongitude = addressLongitude;
            exhibitionActivity.AddressLatitude = addressLatitude;
            exhibitionActivity.EntryPicture = entryPicture;
            exhibitionActivity.ParticipationPicture = participationPicture;
            exhibitionActivity.CodePicture = codePicture;
            exhibitionActivity.UpdatedBy = userName;
            exhibitionActivity.UpdatedTime = DateTime.Now;
            exhibitionActivity.ModifierId = userId;

            return await _exhibitionActivityRepository.Update(exhibitionActivity);
        }

        /// <summary>
        /// 展会活动列表
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="orderBy"></param>
        /// <param name="exhibitionActivityName">活动名称</param>
        /// <param name="exhibitionActivityStartTime">活动开始时间</param>
        /// <param name="exhibitionActivityEndTime">活动结束时间</param>
        /// <param name="status">活动状态:0->停用;1->启用;</param>
        /// <returns></returns>
        public async Task<PageRows<ExhibitionActivityResponse>> GetExhibitionActivityPageList(int pageIndex, int pageSize, string orderField, OrderByType orderByType, string exhibitionActivityName, DateTime? exhibitionActivityStartTime, DateTime? exhibitionActivityEndTime, int? status)
        {
            var result = new PageRows<ExhibitionActivityResponse>();
            var totalNumb = 0;
            var data = DbScoped.SugarScope.Queryable<ExhibitionActivity>()
                .WhereIF(!string.IsNullOrEmpty(exhibitionActivityName), x => x.ExhibitionActivityName.Contains(exhibitionActivityName))
                .WhereIF(status != null, x => x.Status == status)
                .WhereIF(exhibitionActivityStartTime != null, x => x.ExhibitionActivityStartTime >= exhibitionActivityStartTime)
                .WhereIF(exhibitionActivityEndTime != null, x => x.ExhibitionActivityEndTime <= exhibitionActivityEndTime)
            .Where(x => x.IsDeleted == false)
                .OrderByIF(orderField == "exhibitionActivityId", x => x.Id, orderByType)
                .OrderByIF(orderField == "exhibitionActivityCode", x => x.ExhibitionActivityCode, orderByType)
                .OrderByIF(orderField == "createdTime", x => x.CreatedTime, orderByType)
                .OrderByIF(orderField == "updatedTime", x => x.UpdatedTime, orderByType)
                .ToPageList(pageIndex, pageSize, ref totalNumb);
            var resultData = new List<ExhibitionActivityResponse>();
            if (data.Count > 0)
            {
                List<string> fileIdList = data.Select(x => x.EntryPicture).ToList();
                var fileList = await _fileRepository.GetList(fileIdList);
                foreach (ExhibitionActivity item in data)
                {
                    ExhibitionActivityResponse response = new ExhibitionActivityResponse();
                    response.ExhibitionActivityId = item.Id;
                    response.ExhibitionActivityCode = item.ExhibitionActivityCode;
                    response.ExhibitionActivityName = item.ExhibitionActivityName;
                    response.ExhibitionActivityStartTime = item.ExhibitionActivityStartTime;
                    response.ExhibitionActivityEndTime = item.ExhibitionActivityEndTime;
                    response.ExhibitionActivityAddress = item.ExhibitionActivityAddress;
                    response.Status = item.Status;
                    response.UpdatedTime = item.UpdatedTime.Value;
                    response.UpdatedBy = item.UpdatedBy;
                    response.EntryPicture = item.EntryPicture;

                    Files files = fileList.Where(x => x.Id == response.EntryPicture).First();
                    if (files != null)
                    {
                        response.EntryPictureUrl = files.BaseUrl + files.Url;
                    }
                    resultData.Add(response);
                }
            }
            result.Total = totalNumb;
            result.Data = resultData;
            return result;
        }

        /// <summary>
        /// 编辑展会活动状态
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <param name="status">活动状态:0->停用;1->启用;</param>
        /// <param name="userName">修改人名称</param>
        /// <param name="userId">修改人id</param>
        /// <returns></returns>
        public async Task<bool> EditExhibitionActivityStatus(long exhibitionActivityId, int status, string userName, string userId)
        {
            ExhibitionActivity exhibitionActivity = await _exhibitionActivityRepository.GetEntity(x => x.Id == exhibitionActivityId && x.IsDeleted == false);
            if (exhibitionActivity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityNoFoundError);
            }
            if (status == 1 && exhibitionActivity.Status != 0)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityStartError);
            }
            if (status == 0 && exhibitionActivity.Status != 1)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityStopError);
            }
            exhibitionActivity.Status = status;
            exhibitionActivity.UpdatedBy = userName;
            exhibitionActivity.ModifierId = userId;
            exhibitionActivity.UpdatedTime = DateTime.Now;
            return await _exhibitionActivityRepository.Update(exhibitionActivity);
        }

        /// <summary>
        /// 删除展会活动
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <param name="userName">修改人名称</param>
        /// <param name="userId">修改人id</param>
        /// <returns></returns>
        public async Task<bool> DeleteExhibitionActivity(long exhibitionActivityId, string userName, string userId)
        {
            ExhibitionActivity exhibitionActivity = await _exhibitionActivityRepository.GetEntity(x => x.Id == exhibitionActivityId && x.IsDeleted == false);
            if (exhibitionActivity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityNoFoundError);
            }
            if (exhibitionActivity.Status != 0)
            {
                throw new BusinessException(ErrorCode.DeleteExhibitionActivityStopError);
            }
            exhibitionActivity.IsDeleted = true;
            exhibitionActivity.UpdatedBy = userName;
            exhibitionActivity.ModifierId = userId;
            exhibitionActivity.UpdatedTime = DateTime.Now;
            return await _exhibitionActivityRepository.Update(exhibitionActivity); ;
        }

        /// <summary>
        /// 获取展会活动详情
        /// </summary>
        /// <param name="ExhibitionActivityId">活动id</param>
        /// <returns></returns>
        public async Task<ExhibitionActivityDetailResponse> GetExhibitionActivityDetail(long exhibitionActivityId)
        {
            ExhibitionActivity exhibitionActivity = await _exhibitionActivityRepository.GetEntity(x => x.Id == exhibitionActivityId && x.IsDeleted == false);
            if (exhibitionActivity == null)
            {
                throw new BusinessException(ErrorCode.ExhibitionActivityNoFoundError);
            }

            ExhibitionActivityDetailResponse result = new ExhibitionActivityDetailResponse();
            result.ExhibitionActivityId = exhibitionActivity.Id;
            result.ExhibitionActivityCode = exhibitionActivity.ExhibitionActivityCode;
            result.ExhibitionActivityName = exhibitionActivity.ExhibitionActivityName;
            result.ExhibitionActivityStartTime = exhibitionActivity.ExhibitionActivityStartTime;
            result.ExhibitionActivityEndTime = exhibitionActivity.ExhibitionActivityEndTime;
            result.ExhibitionActivityAddress = exhibitionActivity.ExhibitionActivityAddress;
            result.EntryPicture = exhibitionActivity.EntryPicture;
            result.ParticipationPicture = exhibitionActivity.ParticipationPicture;
            result.CodePicture = exhibitionActivity.CodePicture;
            result.Status = exhibitionActivity.Status;
            result.AddressLongitude = exhibitionActivity.AddressLongitude;
            result.AddressLatitude = exhibitionActivity.AddressLatitude;
            result.UpdatedTime = exhibitionActivity.UpdatedTime.Value;
            result.UpdatedBy = exhibitionActivity.UpdatedBy;

            var fileList = await _fileRepository.GetList(new List<string>() { result.EntryPicture, result.ParticipationPicture, result.CodePicture });
            var relationFile = fileList.Find(x => x.Id == result.EntryPicture);
            if (relationFile != null)
            {
                result.EntryPictureUrl = relationFile.BaseUrl + relationFile.Url;
            }
            relationFile = fileList.Find(x => x.Id == result.ParticipationPicture);
            if (relationFile != null)
            {
                result.ParticipationPictureUrl = relationFile.BaseUrl + relationFile.Url;
            }
            relationFile = fileList.Find(x => x.Id == result.CodePicture);
            if (relationFile != null)
            {
                result.CodePictureUrl = relationFile.BaseUrl + relationFile.Url;
            }

            List<ExhibitionActivityParticipant> exhibitionActivityParticipantList = await _exhibitionActivityParticipantRepository.GetEntityList(x => x.ExhibitionActivityId == exhibitionActivity.Id);
            result.LocaleCount = exhibitionActivityParticipantList.Where(x=>x.IsScene == 1).Count();
            result.OffSiteLocaleCount = exhibitionActivityParticipantList.Where(x => x.IsScene == 0).Count();

            return result;
        }
    }
}
