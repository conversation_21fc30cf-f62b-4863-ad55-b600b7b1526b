﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.Service.Interface
{
    public interface IUserService
    {
        Task<PageRows<UserCheckInResponse>> GetUserCheckInPageList(int pageIndex, int pageSize, string orderBy, CreatedTimeScope createdTimeScope, string phone);
        Task<int> GetUserCheckInCount(CreatedTimeScope createdTimeScope, string phone);
        Task<GetOpenIdResponse> GetOrAddOpenId(string openId);
        Task UpdateUserPhone(string openId, string phone);
        Task<bool> UserCheckIn(string openId, string atlasCode);
    }
}
