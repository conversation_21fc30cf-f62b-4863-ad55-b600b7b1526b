using System;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 打印机设备查询记录表
    /// </summary>
    [SqlSugar.SugarTable("ext_printer_device")]
    public class PrinterDevice : BaseBigEntity
    {
        /// <summary>
        /// 打印机型号
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "model")]
        public string Model { get; set; }

        /// <summary>
        /// 设备SN码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "sn")]
        public string SN { get; set; }

        /// <summary>
        /// 首次查询时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "first_query_time")]
        public DateTime? FirstQueryTime { get; set; }

        /// <summary>
        /// 最后查询时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "last_query_time")]
        public DateTime? LastQueryTime { get; set; }

        /// <summary>
        /// 查询次数
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "query_count")]
        public int QueryCount { get; set; } = 0;

        /// <summary>
        /// 设备状态 (0:不在库 1:在库)
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "device_status")]
        public int DeviceStatus { get; set; } = 1;

        /// <summary>
        /// 备注
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "remark")]
        public string Remark { get; set; }
    }
}
