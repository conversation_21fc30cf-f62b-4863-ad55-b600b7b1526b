﻿using System;
using Welshine.Official.Core.Extensions;

namespace Welshine.Official.Core.Exceptions
{
    /// <summary>
    /// 已知的业务异常
    /// </summary>
    public class BusinessException : ApplicationException
    {
        /// <summary>
        /// 错误编码
        /// </summary>
        public int Code { get; set; }

        public BusinessException() { }
       
        public BusinessException(string message) : base(message) { }

        public BusinessException(string message, int code) : base(message)
        {
            Code = code;
        }
        public BusinessException(ErrorCode errorCode) : this(errorCode.ToDescriptionName(), errorCode.GetHashCode())
        {
            Code = errorCode.GetHashCode();
        }
        public BusinessException(string message, System.Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 已知的业务异常
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BusinessException<T> : BusinessException
    {
        /// <summary>
        /// 数据
        /// </summary>
        public T Result { get; set; }

        public BusinessException(string message, T result) : base(message)
        {
            Result = result;
        }

        public BusinessException(string message, int code, T result) : base(message, code)
        {
            Code = code;
            Result = result;
        }
    }
}
