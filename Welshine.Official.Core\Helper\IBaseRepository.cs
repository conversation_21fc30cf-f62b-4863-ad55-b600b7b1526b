﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;

namespace DTHY.Core.Repository
{
    /// <summary>
    /// 基类接口,其他接口继承该接口
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    public interface IBaseRepository<TEntity> where TEntity : class
    {
        /// <summary>
        /// 根据ID查询
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        Task<TEntity> QueryByID(object objId);

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> Add(TEntity model);

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> AddIdentity(TEntity model);
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> AddBigIdentity(TEntity model);
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> Update(TEntity model);
        /// <summary>
        ///批量修改
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> Update(List<TEntity> model);
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<bool> DeleteByIds(object[] ids);


        /// <summary>
        /// 删除对象
        /// </summary>
        /// <param name="model">对象</param>
        /// <param name="isForce">当对象存在属性IsDeleted是生效，false-逻辑删除，true-物理删除</param>
        /// <returns></returns>
        Task<bool> Delete(TEntity model, bool isForce = false);
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> SaveEntity(TEntity model);
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> SaveModel(TEntity model);
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> SaveEntity(List<TEntity> model);
        /// <summary>
        /// 是否存在
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        Task<bool> AnyEntity(Expression<Func<TEntity, bool>> where);
        /// <summary>
        /// 存在数量
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        Task<int> CountEntity(Expression<Func<TEntity, bool>> where);
        /// <summary>
        /// 查一个
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        Task<TEntity> GetEntity(Expression<Func<TEntity, bool>> where);
        /// <summary>
        /// 查多个
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        Task<List<TEntity>> GetEntityList(Expression<Func<TEntity, bool>> where);
        /// <summary>
        /// 分页查多个
        /// </summary>
        /// <returns></returns>
        Task<PageRows<TEntity>> GetPageList(int pageIndex, int pageSize, string orderBy, Expression<Func<TEntity, bool>> where);

    }
}
