﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Linq;
using System.Reflection;

namespace Welshine.Official.Core.Filters
{
    /// <summary>
    /// 忽略一些属性
    /// </summary>
    public class SwaggerExcludeFilter : ISchemaFilter
    {
        #region ISchemaFilter Members

        /// <summary>
        /// 
        /// </summary>
        /// <param name="schema"></param>
        /// <param name="context"></param>
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (schema?.Properties == null || context.Type == null)
                return;

            var excludedProperties = context.Type.GetProperties()
                                         .Where(t =>
                                                t.GetCustomAttribute<SwaggerExcludeAttribute>()
                                                != null);

            foreach (var excludedProperty in excludedProperties)
            {
                var excluded = schema.Properties.Keys.FirstOrDefault(x => x.ToLower() == excludedProperty.Name.ToLower());
                if (excluded != null)
                    schema.Properties.Remove(excluded);
            }
        }


        #endregion
    }
    /// <summary>
    /// 忽略的属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class SwaggerExcludeAttribute : Attribute
    {
    }
}
