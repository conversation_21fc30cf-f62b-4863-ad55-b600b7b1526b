using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.Service.Interface
{
    public interface ILabelService
    {
        Task<bool> AddLabelAsync(string openId, string jsonContent);
        Task<PageRows<LabelTemplate>> GetLabelsAsync(string openId, int pageIndex, int pageSize);
        Task<bool> DeleteLabelAsync(string openId, long labelId);

        Task<bool> AddPrintedLabelAsync(string openId, string jsonContent);
        Task<PageRows<PrintedLabel>> GetPrintedLabelsAsync(string openId, int pageIndex, int pageSize);
        Task<bool> DeletePrintedLabelAsync(string openId, long labelId);

        // 新增的标签模版分类相关方法
        Task<List<LabelTemplateCategoryResponse>> GetLabelTemplateCategoriesAsync();
        Task<PageRows<LabelTemplateResponse>> GetLabelTemplatesByCategoryAsync(long? categoryId, int pageIndex, int pageSize);
    }
}

