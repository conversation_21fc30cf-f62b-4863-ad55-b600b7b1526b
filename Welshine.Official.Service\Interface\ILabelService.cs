using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;

namespace Welshine.Official.Service.Interface
{
    public interface ILabelService
    {
        Task<bool> AddLabelAsync(string openId, string jsonContent);
        Task<PageRows<LabelTemplate>> GetLabelsAsync(string openId, int pageIndex, int pageSize);
        Task<bool> DeleteLabelAsync(string openId, long labelId);

        Task<bool> AddPrintedLabelAsync(string openId, string jsonContent);
        Task<PageRows<PrintedLabel>> GetPrintedLabelsAsync(string openId, int pageIndex, int pageSize);
        Task<bool> DeletePrintedLabelAsync(string openId, long labelId);
    }
}

