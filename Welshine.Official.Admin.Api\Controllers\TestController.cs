﻿using CommonServiceLocator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Config;
using Welshine.Official.SDK;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 测试
    /// </summary>
    [AllowAnonymous]
    public class TestController : BaseApiController
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "Hot", "Sweltering", "Scorching"
        };

        private readonly ILogger<TestController> _logger;

        public TestController(ILogger<TestController> logger)
        {
            _logger = logger;
        }
        /// <summary>
        /// 获取
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<WeatherForecast> Get()
        {
            var rng = new Random();
            return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            {
                Date = DateTime.Now.AddDays(index),
                TemperatureC = rng.Next(-20, 55),
                Summary = Summaries[rng.Next(Summaries.Length)]
            })
            .ToArray();
        }
        /// <summary>
        /// 获取
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public JwtTokenUserInfo GetUser()
        {
            return GetUserInfo(); 
        }
        /// <summary>
        /// Login
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<string> Login()
        {
            var _identityApi = ServiceLocator.Current.GetInstance<IIdentityApi>();
            return await _identityApi.Login(new SDK.Model.UserLoginRequest()
            {
               username= "xiaoming",
                password="$2a$10$CYX9OMv0yO8wR8rE19N2fOaXDJondci5uR68k2eQJm50q8ESsDMlC"
            });
        }
        /// <summary>
        /// 
        /// </summary>
        public class WeatherForecast
        {
            /// <summary>
            /// 时间
            /// </summary>
            public DateTime Date { get; set; }
            /// <summary>
            /// a
            /// </summary>

            public int TemperatureC { get; set; }
            /// <summary>
            /// a
            /// </summary>
            public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
            /// <summary>
            /// a
            /// </summary>
            public string Summary { get; set; }
        }
    }
}
