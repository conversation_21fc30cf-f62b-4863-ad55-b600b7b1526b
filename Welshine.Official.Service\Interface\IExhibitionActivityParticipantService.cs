﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;

namespace Welshine.Official.Service.Interface
{
    public interface IExhibitionActivityParticipantService
    {
        /// <summary>
        /// 展会活动参与人员列表
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="orderBy"></param>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="name">姓名</param>
        /// <param name="mobile">电话</param>
        /// <param name="code">券码</param>
        /// <param name="createdStartTime">参与开始时间</param>
        /// <param name="createdEndTime">参与结束时间</param>
        /// <param name="type">类型:0->经销;1->终端;</param>
        /// <param name="isScene">是否现场客户:0->否;1->是;</param>
        /// <returns></returns>
        Task<PageRows<ExhibitionActivityParticipantResponse>> GetExhibitionActivityParticipantPageList(int pageIndex, int pageSize, string orderBy, long exhibitionActivityId, string name, string mobile, string code, DateTime? createdStartTime, DateTime? createdEndTime, int? type, int? isScene);

        /// <summary>
        /// 展会活动参与人员总数
        /// </summary>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="name">姓名</param>
        /// <param name="mobile">电话</param>
        /// <param name="code">券码</param>
        /// <param name="createdStartTime">参与开始时间</param>
        /// <param name="createdEndTime">参与结束时间</param>
        /// <param name="type">类型:0->经销;1->终端;</param>
        /// <param name="isScene">是否现场客户:0->否;1->是;</param>
        /// <returns></returns>
        Task<int> GetExhibitionActivityParticipantCount(long exhibitionActivityId, string name, string mobile, string code, DateTime? createdStartTime, DateTime? createdEndTime, int? type, int? isScene);

        /// <summary>
        /// 展会活动参与人员登记
        /// </summary>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="name">姓名</param>
        /// <param name="mobile">手机</param>
        /// <param name="type">类型:0->经销;1->终端;</param>
        /// <param name="company">公司名称</param>
        /// <param name="provinceId">省id</param>
        /// <param name="cityId">市id</param>
        /// <param name="longitude">经度</param>
        /// <param name="latitude">纬度</param>
        /// <param name="openId">用户openid</param>
        /// <returns></returns>
        Task<string> ParticipantCheckIn(long exhibitionActivityId, string name, string mobile, int type, string company, long? provinceId, long? cityId, decimal longitude, decimal latitude, string openId);

        /// <summary>
        /// 获取参与人员信息
        /// </summary>
        /// <param name="exhibitionActivityId">展会活动id</param>
        /// <param name="openId">用户openid</param>
        /// <returns></returns>
        Task<ExhibitionActivityParticipant> GetParticipantCheckInInfo(long exhibitionActivityId, string openId);

    }
}
