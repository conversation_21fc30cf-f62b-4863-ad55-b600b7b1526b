﻿using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Domain.Entity;

namespace Welshine.Official.Service
{
    public class OrganizationService
    {
        public void AddDepartment(string departmentName, JwtTokenUserInfo userInfo)
        {
            var count = DbScoped.SugarScope.Queryable<Department>().Count();
            if(count>99)
            {
                throw new BusinessException(ErrorCode.DepartmentCountOverLoad);
            }
            var anyNameRepeat = DbScoped.SugarScope.Queryable<Department>().Any(x=>x.DepartmentName== departmentName);
            if (anyNameRepeat)
            {
                throw new BusinessException(ErrorCode.DepartmentPostNameRepeat);
            }
            anyNameRepeat = DbScoped.SugarScope.Queryable<Post>().Any(x=>x.PostName== departmentName);
            if (anyNameRepeat)
            {
                throw new BusinessException(ErrorCode.DepartmentPostNameRepeat);
            }
            var department = new Department();
            department.DepartmentName = departmentName;
            department.CreatedTime = DateTime.Now;
            department.UpdatedTime = DateTime.Now;
            department.UpdatedBy = userInfo.UserName;
            department.CreatedBy = userInfo.UserName;
            department.ModifierId = userInfo.UserId;
            department.CreatorId = userInfo.UserId;
            DbScoped.SugarScope.Insertable<Department>(department).ExecuteCommand();
        }

        public void DeleteDepartment(long departmentId)
        {
            var anyDepartmentAccount = DbScoped.SugarScope.Queryable<Account>().Any(x=>x.DepartmentId== departmentId);
            if (anyDepartmentAccount)
            {
                throw new BusinessException(ErrorCode.DepartmentDeleteError);
            }
            try
            {
                DbScoped.SugarScope.BeginTran();
                DbScoped.SugarScope.Deleteable<Post>(x => x.DepartmentId == departmentId).ExecuteCommand();
                DbScoped.SugarScope.Deleteable<Department>(x => x.Id == departmentId).ExecuteCommand();
                DbScoped.SugarScope.CommitTran();
            }
            catch (Exception ex)
            {
                DbScoped.SugarScope.RollbackTran();
                throw ex;
            }
        }

        public void EditDepartment(long departmentId,string departmentName, JwtTokenUserInfo userInfo)
        {
            var department = DbScoped.SugarScope.Queryable<Department>().Where(x=>x.Id == departmentId).First();
            if (department != null) 
            {
                var anyRepeatDepartment = DbScoped.SugarScope.Queryable<Department>().Any(x=>x.Id!=departmentId&&x.DepartmentName == departmentName);
                if (anyRepeatDepartment)
                {
                    throw new BusinessException(ErrorCode.DepartmentPostNameRepeat);
                }
                anyRepeatDepartment = DbScoped.SugarScope.Queryable<Post>().Any(x=>x.PostName==departmentName);
                if (anyRepeatDepartment)
                {
                    throw new BusinessException(ErrorCode.DepartmentPostNameRepeat);
                }
                department.UpdatedTime = DateTime.Now;
                department.UpdatedBy = userInfo.UserName;
                department.ModifierId = userInfo.UserId;
                department.DepartmentName = departmentName;
                DbScoped.SugarScope.Updateable(department).ExecuteCommand();
            }
            else
            {
                throw new BusinessException(ErrorCode.DepartmentNotExist);
            }
        }

        public List<Department> GetDepartments()
        {
            var result = DbScoped.SugarScope.Queryable<Department>().ToList();
            return result;
        }

        public List<Post> GetPosts(long departmentId)
        {
            var result = DbScoped.SugarScope.Queryable<Post>().Where(x=>x.DepartmentId == departmentId).ToList();
            return result;
        }

        public void SaveDepartmentPosts(long departmentId,List<string> postNames, JwtTokenUserInfo userInfo)
        {
            List<string> dest = postNames.Distinct().ToList();
            if(dest.Count!=postNames.Count)
            {
                throw new BusinessException(ErrorCode.DepartmentPostNameRepeat);
            }
            var anyRepeat = DbScoped.SugarScope.Queryable<Department>().Where(x => dest.Contains(x.DepartmentName)).Any();
            if(anyRepeat)
            {
                throw new BusinessException(ErrorCode.DepartmentPostNameRepeat);
            }
            anyRepeat = DbScoped.SugarScope.Queryable<Post>().Where(x=>x.DepartmentId!=departmentId).Where(x => dest.Contains(x.PostName)).Any();
            if (anyRepeat)
            {
                throw new BusinessException(ErrorCode.DepartmentPostNameRepeat);
            }
            dest = dest.TakeWhile(x=>!String.IsNullOrEmpty(x)).ToList();
            if(dest.Count>99)
            {
                throw new BusinessException(ErrorCode.PostCountOverLoad);
            }

            var isDepartmentExist = DbScoped.SugarScope.Queryable<Department>().Where(x => x.Id == departmentId).Any();
            if(!isDepartmentExist)
            {
                throw new BusinessException(ErrorCode.DepartmentNotExist);
            }
            var postList = DbScoped.SugarScope.Queryable<Post>().Where(x => x.DepartmentId == departmentId).ToList();
            List<string> src = postList.Select(x=>x.PostName).ToList();

            List<string> insertPostNameList = dest.Except(src).ToList();
            List<string> deletePostNameList = src.Except(dest).ToList();

            List<long> deletePostIds = new List<long>();
            foreach (var item in postList)
            {
                if (deletePostNameList.Contains(item.PostName))
                {
                    deletePostIds.Add(item.Id);
                    var anyAccount = DbScoped.SugarScope.Queryable<Account>().Where(x => x.PostId == item.Id).Any();
                    if (anyAccount)
                    {
                        throw new BusinessException(ErrorCode.PostDeleteError);
                    }
                }
            }
            List<Post> insertPosts = new List<Post>();
            foreach (var item in insertPostNameList)
            {
                Post post= new Post();
                post.PostName = item;
                post.UpdatedTime= DateTime.Now;
                post.CreatedTime = DateTime.Now;
                post.CreatedBy = userInfo.UserName;
                post.UpdatedBy = userInfo.UserName;
                post.CreatorId = userInfo.UserId;
                post.ModifierId = userInfo.UserId;
                post.DepartmentId = departmentId;
                insertPosts.Add(post);
            }


            try
            {
                DbScoped.SugarScope.BeginTran();
                DbScoped.SugarScope.Insertable<Post>(insertPosts).ExecuteCommand();
                DbScoped.SugarScope.Deleteable<Post>().Where(x => x.DepartmentId == departmentId && deletePostIds.Contains(x.Id)).ExecuteCommand();
                DbScoped.SugarScope.CommitTran();
            }
            catch (Exception ex)
            {
                DbScoped.SugarScope.RollbackTran();
                throw ex;
            }

        }
    }
}
