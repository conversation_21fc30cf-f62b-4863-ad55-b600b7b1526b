﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Welshine.Official.Core.Attributes.ModelValid;
using Welshine.Official.Domain.VO.Admin.Request;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 分类产品详情条目
    /// </summary>
    public class GetCategoryProductResponse
    {
        /// <summary>
        /// 分类产品Id
        /// </summary>
        public long CategoryProductId { get; set; }

        /// <summary>
        /// 语言: 0->英文;
        /// </summary>
        public int ProductLanguage { get; set; }

        /// <summary>
        /// 分类Id
        /// </summary>
        public long CategoryId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        ///// <summary>
        ///// 商品材质
        ///// </summary>
        //public string ProductTexture { get; set; }

        ///// <summary>
        ///// 商品颜色
        ///// </summary>
        //public string ProductColor { get; set; }

        ///// <summary>
        ///// 商品规格
        ///// </summary>
        //public string ProductSpecification { get; set; }

        /// <summary>
        /// 商品介绍
        /// </summary>
        public string ProductIntroduce { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public List<FileResponse> ProductPicture { get; set; }

        /// <summary>
        /// 角图图片
        /// </summary>
        public List<FileResponse> ProductCornerMarkPicture { get; set; }

        /// <summary>
        /// 发布状态: 0->未发布; 1->已发布;
        /// </summary>
        public int ReleaseStatus { get; set; }

        /// <summary>
        /// 商品规格
        /// </summary>
        public List<ProductIntroduce> IntroduceList { get; set; }
    }
}
