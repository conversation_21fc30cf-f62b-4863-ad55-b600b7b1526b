﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Dto;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 版本服务接口
    /// </summary>
    public interface IVersionsService
    {
        /// <summary>
        /// 添加版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        Task<bool> AddVersions(Versions versions);

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <param name="versionsId">版本Id</param>
        /// <returns></returns>
        Task<GetVersionsResponse<T>> GetVersions<T>(int versionType, long versionsId);

        /// <summary>
        /// 修改版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        Task<bool> EditVersions(Versions versions);

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <returns></returns>
        Task<bool> ExistsRelease(int versionType);

        /// <summary>
        /// 删除版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        Task<bool> DeleteVersions(Versions versions);

        /// <summary>
        /// 提交版本信息审核
        /// </summary>
        /// <param name="versionsId">版本Id</param>
        /// <returns></returns>
        Task<bool> SubmitVersionsApprove(Versions versions);

        /// <summary>
        /// 提交版本信息审核
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        Task<bool> VersionsApprove(Versions versions);

        /// <summary>
        /// 获取版本列表
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="versionType">版本类型</param>
        /// <param name="title">版本标题</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<VersionsListResponse>> GetVersionsPageList(string userId, int versionType, string title, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 版本发布
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
         Task<bool> VersionsRelease(Versions versions);

        /// <summary>
        /// 获取所有已发布的版本
        /// </summary>
        /// <returns></returns>
        Task<List<VersionsListResponse>> GetAllReleaseVersions();

        /// <summary>
        /// 获取版本列表(直营门店)
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="storeName">门店名称</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<GetContactsPageListResponse>> GetContactsPageList(string userId, string storeName, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取版本列表(直营门店)
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<GetWebContactsPageListResponse> GetWebContactsPageList(int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 首页
        /// </summary>
        /// <returns></returns>
        Task<GetHomePageInfoResponse> GetHomePageInfo();

        /// <summary>
        /// 获取中文产品图
        /// </summary>
        /// <returns></returns>
        Task<ProductCNData> GetProductCN();

        /// <summary>
        /// 底部横幅
        /// </summary>
        /// <returns></returns>
        Task<BottomData> GetBottomData();

        /// <summary>
        /// 关于惠而信
        /// </summary>
        /// <returns></returns>
        Task<WelshineData> GetWelshineData();

        /// <summary>
        /// 侧边横幅
        /// </summary>
        /// <returns></returns>
        Task<SidebarData> GetSidebarData();
    }
}
