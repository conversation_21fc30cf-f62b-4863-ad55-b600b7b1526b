﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Welshine.Official.Service.Interface
{
    public interface IWxService
    {
        /// <summary>
        /// 获取用户手机
        /// </summary>
        /// <param name="code">验证码</param>
        /// <returns></returns>
        Task<string> GetWxPhone(string code);
        /// <summary>
        /// 获取OpenId
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<string> GetOpenId(string code);

        /// <summary>
        /// 获取打印机小程序的OpenId
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<string> GetPrinterOpenId(string code);
    }
}
