﻿using DTHY.Core.Repository;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.Repository.Interface
{
    /// <summary>
    /// 产品仓储接口
    /// </summary>
    public interface IProductRepository : IBaseRepository<Product>
    {
        /// <summary>
        /// 判断产品名称是否存在
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <returns></returns>
        Task<bool> ExistsProductName(string productName);

        /// <summary>
        /// 判断产品名称是否存在
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="productId">产品Id</param>
        /// <returns></returns>
        Task<bool> ExistsProductName(string productName, long productId);

        /// <summary>
        /// 获取数据总数
        /// </summary>
        /// <returns></returns>
        Task<int> GetCount();

        /// <summary>
        /// 添加产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        Task<bool> AddProduct(Product product, string thumbnail, List<string> detailPicture);

        /// <summary>
        /// 获取产品信息
        /// </summary>
        /// <param name="ProductId">产品Id</param>
        /// <returns></returns>
        Task<Product> GetProduct(long ProductId);

        /// <summary>
        /// 修改产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        Task<bool> EditProduct(Product product, string thumbnail, List<string> detailPicture);

        /// <summary>
        /// 删除产品分类
        /// </summary>
        /// <param name="product">产品信息</param>
        /// <returns></returns>
        Task<bool> DeleteProduct(Product product);

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<ProductListReponse>> GetProductPageList(string productName, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取产品列表
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<List<WXProductListReponse>> WX_GetProductPageList();
    }
}
