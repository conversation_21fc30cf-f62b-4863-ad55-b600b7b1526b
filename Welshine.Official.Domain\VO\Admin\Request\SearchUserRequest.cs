﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class SearchUserRequest
    {
        /// <summary>
        /// 用户姓名
        /// </summary>
        [RegularExpression(@"^[\u4e00-\u9fa5A-Za-z]{0,10}$",ErrorMessage = "姓名应为10个以内中文字")]
        public string UserName { get; set; }
    }
}
