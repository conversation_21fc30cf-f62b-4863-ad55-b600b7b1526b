﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class AddAccountRequest
    {
        /// <summary>
        /// 部门id
        /// </summary>
        [Required(ErrorMessage = "部门id为必填")]
        public long? DepartmentId { get; set; }
        /// <summary>
        /// 岗位id
        /// </summary>
        [Required(ErrorMessage = "岗位id为必填")]
        public long? PostId { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        [Required(ErrorMessage ="登陆账号为必填")]
        [RegularExpression(@"[a-zA-Z0-9]{4,20}", ErrorMessage = "用户账号必须为4-20位的数字字母组合")]
        public string LoginName { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        [Required(ErrorMessage = "姓名为必填")]
        [RegularExpression(@"[\u4e00-\u9fa5_a-zA-Z]{2,10}", ErrorMessage = "姓名位2-10位长度的中文或字母组合")]
        public string UserName { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [Required(ErrorMessage = "电话为必填")]
        [RegularExpression(@"[0-9]{8,13}", ErrorMessage = "电话必须为8-13位的数字")]
        public string Tel { get; set; }
    }
}
