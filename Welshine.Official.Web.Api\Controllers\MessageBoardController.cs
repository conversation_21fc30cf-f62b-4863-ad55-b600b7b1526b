﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Service.Interface;
using Welshine.Official.Web.Api.Core;

namespace Welshine.Official.Web.Api.Controllers
{
    /// <summary>
    /// 留言区
    /// </summary>
    public class MessageBoardController : BaseApiController
    {
        private readonly IMessageBoardService _messageBoardService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="messageBoardService"></param>
        public MessageBoardController(IMessageBoardService messageBoardService, IHttpContextAccessor httpContextAccessor)
        {
            _messageBoardService = messageBoardService;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 添加用户留言
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddMessageBoard([FromBody] BaseRequest<AddMessageBoardRequest> request)
        {
            bool result = false;
            try
            {
                //本地执行代码时，这将返回 {::1}。部署到测试或生产服务器后，它将获取您客户端的 IP
                //HttpContextAccessor context = new HttpContextAccessor();
                //var ipAddress = context.HttpContext?.Connection.RemoteIpAddress.ToString();

                //var ip = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress != null ?
                //    _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString() : "127.0.0.1";

                var ip = this.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                if (string.IsNullOrWhiteSpace(ip))
                { 
                    ip = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress != null ?
                    _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString() : "127.0.0.1";
                }

                MessageBoard messageBoard = new MessageBoard()
                {
                    Name = request.Body.Name,
                    Phone = request.Body.Phone,
                    Content = request.Body.Content,
                    Email = request.Body.Email,
                    Area = request.Body.Area,
                    IpAddress = ip,
                };

                result = await _messageBoardService.AddMessageBoard(messageBoard);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddMessageBoard Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

    }
}
