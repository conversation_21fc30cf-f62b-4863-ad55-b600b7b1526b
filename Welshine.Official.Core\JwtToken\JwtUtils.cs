﻿using JWT;
using JWT.Algorithms;
using JWT.Builder;
using JWT.Exceptions;
using JWT.Serializers;
using Microsoft.AspNetCore.DataProtection;
using MySqlX.XDevAPI.Common;
using Org.BouncyCastle.Crypto.Tls;
using Org.BouncyCastle.Ocsp;
using System;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Text;

namespace Welshine.Official.Core.JwtToken
{
    public static class JwtUtils
    {
        private static readonly X509Certificate2 _certificate = new X509Certificate2("./server.pfx", "dthy123...");
        private static IJwtDecoder _jwtDecoder;
        private static readonly IJsonSerializer serializer = new JsonNetSerializer();
        private static readonly IBase64UrlEncoder urlEncoder = new JwtBase64UrlEncoder();
        private static readonly IDateTimeProvider provider = new UtcDateTimeProvider();

        public static string Encode(Dictionary<string,object> payLoad,long exp)
        {
            var build = JwtBuilder.Create()
                     .WithAlgorithm(new RS256Algorithm(_certificate))
                     .AddClaim("exp", exp);
            foreach (var claim in payLoad)
            {
                build.AddClaim(claim.Key, claim.Value);
            }
            var token = build.Encode();
            return token;
        }

        public static IDictionary<string,object> Decode(string token)
        {
            var result = new Dictionary<string,object>();
            try
            {
                if(_jwtDecoder == null)
                {
                    IJwtValidator validator = new JwtValidator(serializer, provider);
                    IJwtAlgorithm algorithm = new RS256Algorithm(_certificate);
                    _jwtDecoder = new JwtDecoder(serializer, validator, urlEncoder, algorithm);
                }
                var json = _jwtDecoder.Decode(token);
                result = serializer.Deserialize<Dictionary<string,object>>(json);
                result.Add("Success", true);
                result.Add("Message", "Success");
            }
            catch (TokenNotYetValidException)
            {
                result.Add("Success", false);
                result.Add("Message", "Token is not valid yet");
            }
            catch (TokenExpiredException)
            {
                result.Add("Success", false);
                result.Add("Message", "Token has expired");
            }
            catch (SignatureVerificationException)
            {
                result.Add("Success", false);
                result.Add("Message", "Token has invalid signature");
            }
            return result;
        }
    }
}
