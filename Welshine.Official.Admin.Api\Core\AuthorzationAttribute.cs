﻿using CommonServiceLocator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using Serilog;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using Welshine.Official.Core;
using Welshine.Official.Core.Config;
using Welshine.Official.Core.JwtToken;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.SDK;
using Welshine.Official.Service;

namespace Welshine.Official.Admin.Api.Core
{
    /// <summary>
    /// 鉴权
    /// </summary>
    public class AuthorzationAttribute : ActionFilterAttribute
    {
        public override async void OnActionExecuting(ActionExecutingContext context)
        {
            IList<FilterDescriptor> filterDescriptors = context.ActionDescriptor.FilterDescriptors;
            if (filterDescriptors.Any((FilterDescriptor m) => m.Filter.GetType() == typeof(AllowAnonymousAttribute)) || filterDescriptors.Any((FilterDescriptor m) => m.Filter.GetType() == typeof(AllowAnonymousFilter)))
            {
                return;
            }
            var isAnonymous = context.ActionDescriptor.EndpointMetadata.OfType<AllowAnonymousAttribute>().Any();
            if (isAnonymous)
            {
                return;
            }
            var httpContextAccessor = ServiceLocator.Current.GetInstance<IHttpContextAccessor>();
            HttpRequest request = httpContextAccessor.HttpContext.Request;
            string url = request.PathBase + request.Path;
            if (request.Headers.ContainsKey("Authorization") && request.Headers.TryGetValue("Authorization", out var value))
            {
                try
                {
                    string token = value.ToString().Split(' ')[1];

                    var payLoad = JwtUtils.Decode(token);
                    if ((bool)payLoad["Success"])
                    {
                        var roles = JsonConvert.DeserializeObject<List<JwtTokenUserRole>>(payLoad["Roles"].ToString());
                        if (!roles.Any(x => x.roleName == "admin"))
                        {
                            var validate = new RBACGRoupService().RBACValidate(roles, url);
                            if (!validate)
                            {
                                context.Result = new ForbidResult();
                                return;
                            }
                        }
                        var loginName = payLoad["LoginName"].ToString();
                        var redisToken = FreeRedisHelper.DefaultInstance.Get("AccountToken:" + loginName);
                        if (redisToken == null)
                        {
                            context.Result = new ForbidResult();
                            return;
                        }
                        else
                        {
                            if (redisToken!=token)
                            {
                                context.Result = new ForbidResult();
                                return;
                            }
                        }
                    }
                    else
                    {
                        context.Result = new UnauthorizedObjectResult(payLoad["Message"].ToString());
                        return;
                    }


                    /*var tokenHandler = new JwtSecurityTokenHandler();
                    var userTokenInfo = tokenHandler.ReadToken(token) as JwtSecurityToken;
                    var user = userTokenInfo.Claims.FirstOrDefault(x => x.Type == "user")?.Value;
                    if (string.IsNullOrWhiteSpace(user))
                    {
                        context.Result = new UnauthorizedObjectResult("没有用户信息");
                        return;
                    }
                    //校验角色
                    var userInfo= JsonConvert.DeserializeObject<JwtTokenUserInfo>(user);
                    var roleList=new List<string>() { "ROLE_CMSADMIN", "ROLE_ADMIN" };
                    if(!(userInfo.Roles!=null&& userInfo.Roles.Any(x=> roleList.Contains(x.roleName))))
                    {
                        context.Result = new ForbidResult();
                        return;
                    }
                    //校验token
                    var _identityApi = ServiceLocator.Current.GetInstance<IIdentityApi>();
                    var checkInfo = await _identityApi.CheckToken(token);
                    if (!checkInfo.Success)
                    {
                        context.Result = new UnauthorizedObjectResult(checkInfo.Message);
                        return;
                    }*/
                }
                catch (System.Exception ex)
                {
                    Log.Warning("AuthorzationAttribute:" + ex.Message+ex.StackTrace);
                    context.Result = new UnauthorizedObjectResult(ex.Message);
                    return;
                }
            }
            else
            {
                context.Result = new UnauthorizedObjectResult(ApiStatus.Unauthorized.GetHashCode());
                return;
            }
          
        }
    }
}
