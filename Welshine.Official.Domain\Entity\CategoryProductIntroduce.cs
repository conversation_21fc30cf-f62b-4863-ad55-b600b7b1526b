﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 分类产品规格
    /// </summary>
    [SqlSugar.SugarTable("cms_category_product_introduce")]
    public class CategoryProductIntroduce : BaseBigEntity
    {
        /// <summary>
        /// 型号
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "category_product_id")]
        public long CategoryProductId { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "introduce_model")]
        public string IntroduceModel { get; set; }

        /// <summary>
        /// 尺寸
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "introduce_size")]
        public string IntroduceSize { get; set; }

        /// <summary>
        /// 容量
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "introduce_capacity")]
        public string IntroduceCapacity { get; set; }

        /// <summary>
        /// 规格图片
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "img_url")]
        public string ImgUrl { get; set; }

    }
}
