<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile>./Welshine.Official.Admin.Api.xml</DocumentationFile>
    <StartupObject>Welshine.Official.Admin.Api.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="appsettings.pro.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Welshine.Official.Repository\Welshine.Official.Repository.csproj" />
    <ProjectReference Include="..\Welshine.Official.Service\Welshine.Official.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile_jenkins">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="server.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Welshine.Official.Admin.Api.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>


</Project>
