﻿
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using Welshine.Official.Core.Filters;

namespace Welshine.Official.Core.RestfulApi.Base
{
    /// <summary>
    /// RequestPageModel
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class RequestPageModel<T> : BaseRequest, IValidatableObject
    {
        /// <summary>
        /// 页码
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "页码最小值为0")]
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小(0表示不分页,获取全部数据)
        /// </summary>
        [DefaultValue(10)]
        [Range(0, 2500, ErrorMessage = "页大小最小值为0,最大值2500")]
        public int PageSize { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        [DefaultValue("id")]
        public string OrderFile { get; set; } = "id";

        /// <summary>
        /// 排序类型(0:正序 1:倒序)
        /// </summary>
        [EnumDataType(typeof(SortType), ErrorMessage = "排序类型参数错误.")]
        public SortType SortType { get; set; }


        /// <summary>
        /// 其他请求参数
        /// </summary>
        [Required(ErrorMessage = "请求数据不能为空")]
        public T RequestParams { get; set; }
        /// <summary>
        /// 排序(后端用)
        /// </summary>
        [IgnoreDataMember]
        [SwaggerExclude]
        public string OrderBy
        {
            get
            {
                return (this.OrderFile ?? "id") + " " + (this.SortType == 0 ? "asc" : "desc");
            }
        }
        /// <summary>
        /// Validate
        /// </summary>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var result = new List<ValidationResult>();
            if (PageIndex < 0) result.Add(new ValidationResult("页码 不可小于零"));
            if (PageSize < 0) result.Add(new ValidationResult("也数量 不可小于零"));
            if (SortType != SortType.Asc && SortType != SortType.Desc)
                result.Add(new ValidationResult("排序类型无效"));
            return result;
        }
    }

    /// <summary>
    /// RequestPageModel
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class RequestPageModel : BaseRequest, IValidatableObject
    {
        /// <summary>
        /// 页码
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "页码最小值为0")]
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小(0表示不分页,获取全部数据)
        /// </summary>
        [DefaultValue(10)]
        [Range(0, 2500, ErrorMessage = "页大小最小值为0,最大值2500")]
        public int PageSize { get; set; }

        /// <summary>
        /// Validate
        /// </summary>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var result = new List<ValidationResult>();
            if (PageIndex < 0) result.Add(new ValidationResult("页码 不可小于零"));
            if (PageSize < 0) result.Add(new ValidationResult("也数量 不可小于零"));
           
            return result;
        }
    }

    /// <summary>
    /// 后端用 过期
    /// </summary>
    [System.Obsolete]
    public class PageHead
    {
        /// <summary>
        /// 页码
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "页码最小值为0")]
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小(0表示不分页,获取全部数据)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "页大小最小值为0")]
        public int PageSize { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        [DefaultValue("id")]
        public string OrderFile { get; set; } = "id";
        /// <summary>
        /// 排序
        /// </summary>
        public string OrderBy
        {
            get
            {
                return (this.OrderFile ?? "id") + " " + (this.SortType == 0 ? "asc" : "desc");
            }
        }
        /// <summary>
        /// 排序类型(0:正序 1:倒序)
        /// </summary>
        [EnumDataType(typeof(SortType), ErrorMessage = "排序类型参数错误.")]
        public SortType SortType { get; set; }
    }

    /// <summary>
    /// 排序类型
    /// </summary>
    public enum SortType
    {
        /// <summary>
        /// 正序
        /// </summary>
        [Description("正序")]
        Asc = 0,

        /// <summary>
        /// 倒序
        /// </summary>
        [Description("倒序")]
        Desc = 1
    }
}
