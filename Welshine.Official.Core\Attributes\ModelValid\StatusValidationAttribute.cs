﻿using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Core.Attributes.ModelValid
{
    /// <summary>
    /// 状态校验 true/false
    /// </summary>
    public class StatusValidationAttribute : ValidationAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="value">第一个参数是验证对象的值</param>
        /// <param name="validationContext"></param>
        /// <returns></returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null)
            {
                var valueAsString = value.ToString().ToLower();
                if (!string.IsNullOrWhiteSpace(valueAsString))
                {
                    if (valueAsString != "true" && valueAsString != "false")
                    {
                        string errorMessage = "状态格式不对.";
                        return new ValidationResult(ErrorMessage ?? errorMessage);
                    }
                }
            }
            return ValidationResult.Success;
        }
    }
}
