﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 展会服务接口
    /// </summary>
    public interface IExhibitionService
    {
        /// <summary>
        /// 添加展会
        /// </summary>
        /// <param name="exhibition">展会信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        Task<bool> AddExhibition(Exhibition exhibition, string thumbnail, List<string> detailPicture);

        /// <summary>
        /// 修改展会
        /// </summary>
        /// <param name="exhibition">展会信息</param>
        /// <param name="thumbnail">略缩图</param>
        /// <param name="detailPicture">详情图</param>
        /// <returns></returns>
        Task<bool> EditExhibition(Exhibition exhibition, string thumbnail, List<string> detailPicture);

        /// <summary>
        /// 获取展会
        /// </summary>
        /// <param name="exhibitionId">展会Id</param>
        /// <returns></returns>
        Task<ExhibitionDetailResponse> GetExhibitionById(long exhibitionId);

        /// <summary>
        /// 删除展会
        /// </summary>
        /// <param name="exhibition">展会信息</param>
        /// <returns></returns>
        Task<bool> DeleteExhibition(Exhibition exhibition);

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <param name="exhibitionName">展会名称</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<ExhibitionListReponse>> GetExhibitionPageList(string exhibitionName, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取展会列表
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        Task<PageRows<WXExhibitionListReponse>> WX_GetExhibitionPageList(int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取展会
        /// </summary>
        /// <param name="exhibitionId">展会Id</param>
        /// <returns></returns>
        Task<WXExhibitionDetailResponse> WX_GetExhibitionById(long exhibitionId);
    }
}
