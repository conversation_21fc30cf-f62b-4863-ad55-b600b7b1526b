﻿using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;

namespace Welshine.Official.Core.Filters
{
    public class SwaggerDefaultREValueFilter : ISchemaFilter
    {
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            var sasa = context.Type;
            var excludedProperties = context.Type.GetProperties();
            foreach (var property in excludedProperties)
            {
                var attributes = property.GetCustomAttributes(true);
                foreach (var attribute in attributes)
                {
                    
                    var a = attribute.GetType();
                    if (a == typeof(RegularExpressionAttribute))
                    {
                        schema.Properties.Remove(property.Name);
                        //schema.Properties.Remove("atlasName");
                        //var sss = schema.Properties["atlasName"];
                        
                        schema.Example = new OpenApiObject
                        {
                            [property.Name] = new OpenApiString("")
                        };
                    }
                }
            };
        }
    }
}
