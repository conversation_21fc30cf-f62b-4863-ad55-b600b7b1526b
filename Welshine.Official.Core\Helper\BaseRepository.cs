﻿using SqlSugar;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;

namespace DTHY.Core.Repository
{
    /// <summary>
    /// 基类实现
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    public class BaseRepository<TEntity> :IBaseRepository<TEntity> where TEntity : class, new()
    {
        public async Task<bool> Add(TEntity model)
        {
            SetCreateTime(model);
            return (await DbScoped.SugarScope.Insertable(model).ExecuteCommandAsync()) > 0;
        }
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> AddIdentity(TEntity model)
        {
            SetCreateTime(model);
            long id = await DbScoped.SugarScope.GetSimpleClient<TEntity>().InsertReturnBigIdentityAsync(model);
            var idProp = model.GetType().GetProperty("Id");
            idProp.SetValue(model, id);
            return id > 0;
        }
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> AddBigIdentity(TEntity model)
        {
            SetCreateTime(model);
            return await DbScoped.SugarScope.Insertable(model).ExecuteCommandIdentityIntoEntityAsync();
        }

        public Task<bool> Delete(TEntity model, bool isForce = false)
        {
            if (!isForce && SetDeleted(model))
            {
                SetUpdateTime(model);
                return DbScoped.SugarScope.GetSimpleClient<TEntity>().UpdateAsync(model);
            }
            else
            {
                return DbScoped.SugarScope.GetSimpleClient<TEntity>().DeleteAsync(model);
            }
        }

        public new Task<bool> DeleteByIds(object[] ids)
        {
            return DbScoped.SugarScope.GetSimpleClient<TEntity>().DeleteByIdAsync(ids);
        }

        public Task<TEntity> QueryByID(object objId)
        {
            return DbScoped.SugarScope.GetSimpleClient<TEntity>().GetByIdAsync(objId);
            //return base.GetByIdAsync(objId);
        }

        public new Task<bool> Update(TEntity model)
        {
            return DbScoped.SugarScope.GetSimpleClient<TEntity>().UpdateAsync(model);
        }
        public async Task<bool> Update(List<TEntity> model)
        {
            return await DbScoped.SugarScope.GetSimpleClient<TEntity>().UpdateRangeAsync(model);
        }
        public Task<bool> AnyEntity(Expression<Func<TEntity, bool>> where)
        {
            return DbScoped.SugarScope.GetSimpleClient<TEntity>().AsQueryable().AnyAsync(where);
        }

        public Task<int> CountEntity(Expression<Func<TEntity, bool>> where)
        {
            return DbScoped.SugarScope.GetSimpleClient<TEntity>().AsQueryable().CountAsync(where);
        }

        public Task<TEntity> GetEntity(Expression<Func<TEntity, bool>> where)
        {
            return DbScoped.SugarScope.Queryable<TEntity>().FirstAsync(where);
        }

        public Task<List<TEntity>> GetEntityList(Expression<Func<TEntity, bool>> where)
        {
            return DbScoped.SugarScope.GetSimpleClient<TEntity>().AsQueryable().Where(where).ToListAsync();
        }

        public async Task<PageRows<TEntity>> GetPageList(int pageIndex, int pageSize, string orderBy, Expression<Func<TEntity, bool>> where)
        {
            RefAsync<int> totalNumber = 0;
            return new PageRows<TEntity>()
            {
                Data = await DbScoped.SugarScope.GetSimpleClient<TEntity>().AsQueryable().Where(where).OrderByIF(!string.IsNullOrWhiteSpace(orderBy), orderBy).ToPageListAsync(pageIndex, pageSize, totalNumber),
                Total = totalNumber
            };

        }


        private static void SetCreateTime(TEntity entity)
        {
            var createTimeProp = entity.GetType().GetProperty("CreatedTime");
            var updateTimeProp = entity.GetType().GetProperty("UpdatedTime");
            if (createTimeProp != null)
            {
                var oldvalue = createTimeProp.GetValue(entity);
                if (oldvalue == null)
                {
                    createTimeProp.SetValue(entity, DateTime.Now);
                }
                else if ((DateTime)oldvalue < DateTime.Now)
                {
                    createTimeProp.SetValue(entity, DateTime.Now);
                }

            }

            if (updateTimeProp != null)
            {
                var oldvalue = updateTimeProp.GetValue(entity);
                if (oldvalue == null)
                {
                    updateTimeProp.SetValue(entity, DateTime.Now);
                }
                else if ((DateTime)oldvalue < DateTime.Now)
                {
                    updateTimeProp.SetValue(entity, DateTime.Now);
                }

            }
        }

        private static void SetUpdateTime(TEntity entity)
        {
            var updateTimeProp = entity.GetType().GetProperty("UpdatedTime");

            if (updateTimeProp != null)
            {
                var oldvalue = updateTimeProp.GetValue(entity);
                if (oldvalue == null)
                {
                    updateTimeProp.SetValue(entity, DateTime.Now);
                }
                else if ((DateTime)oldvalue < DateTime.Now)
                {
                    updateTimeProp.SetValue(entity, DateTime.Now);
                }

            }
        }

        private bool SetDeleted(TEntity entity)
        {
            var deletedProp = entity.GetType().GetProperty("IsDeleted");

            if (deletedProp != null)
            {
                deletedProp.SetValue(entity, true);
                return true;
            }
            return false;
        }

        public async Task<bool> SaveEntity(TEntity model)
        {
            model = await DbScoped.SugarScope.Saveable(model).ExecuteReturnEntityAsync();
            return true;
        }
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> SaveModel(TEntity model)
        {
            return await DbScoped.SugarScope.Saveable(model).ExecuteCommandAsync() > 0;
        }
        public async Task<bool> SaveEntity(List<TEntity> model)
        {
            return await DbScoped.SugarScope.Saveable(model).ExecuteCommandAsync() > 0;
        }
    }
}
