﻿using CsvHelper.Configuration.Attributes;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    /// <summary>
    /// 留言板列表条目
    /// </summary>
    public class MessageBoardPageListResponse
    {
        /// <summary>
        /// Id
        /// </summary>
        [Ignore]
        public long Id { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [Name("姓名")]
        public string Name { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [Name("电话")]
        public string Phone { get; set; }

        /// <summary>
        /// 留言信息
        /// </summary>
        [Name("留言信息")]
        public string Content { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [Name("邮箱")]
        public string Email { get; set; }

        /// <summary>
        /// 地区
        /// </summary>
        [Name("地区")]
        public string Area { get; set; }

        /// <summary>
        /// ip地址
        /// </summary>
        [Name("ip地址")]
        public string IpAddress { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        [Name("提交时间")]
        [Format("yyyy-MM-dd HH:mm:ss")]
        public DateTime CreatedTime { get; set; }
    }
}
