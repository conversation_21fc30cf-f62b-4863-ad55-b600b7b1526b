﻿using DTHY.Core.Repository;
using SqlSugar.IOC;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    public class CorporateNewRepository : BaseRepository<CorporateNew>, ICorporateNewRepository
    {
        public async Task<CorporateNew> InsertAsync(CorporateNew corporateNew)
        {
            var data = await DbScoped.SugarScope.Insertable<CorporateNew>(corporateNew).ExecuteReturnEntityAsync();
            return data;
        }
    }
}
