﻿using Aliyun.OSS;
using Aliyun.OSS.Util;
using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO;
using Welshine.Official.Repository;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class FileService : IFileService
    {
        private readonly IOssService _aliOssService;
        private readonly IFileRepository _fileRepository;
        private readonly IMapper _mapper;
        public FileService(IOssService aliOssService, IFileRepository fileRepository, IMapper mapper)
        {
            _aliOssService = aliOssService;
            _fileRepository = fileRepository;
            _mapper = mapper;
        }
        /// <summary>
        /// 获取文件类型
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private int GetFileType(string fileName)
        {
            string extensionName = System.IO.Path.GetExtension(fileName);

            var imageArray = new[] { ".jpg", ".png", ".jpeg", ".pdf", ".csv", ".image" };
            var txtArray = new[] { ".txt" };

            if (imageArray.Contains(extensionName))
            {
                return 1;
            }
            if (txtArray.Contains(extensionName))
            {
                return 2;
            }

            return 0;
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="formFile">文件</param>
        /// <param name="userId">用户Id</param>
        /// <param name="baseUrl"></param>
        /// <param name="isOverlay">是否覆盖</param>
        /// <returns></returns>
        public async Task<FileDto> UploadFile(IFormFile formFile, string userId, string userName, bool isOverlay)
        {
            var baseUrl = _aliOssService.BaseUrl;
            string fileName = isOverlay ? formFile.FileName : (DateTime.Now.ToString("yyyyMMddHHmmssfff") + formFile.FileName);
            string keyName = $"{userId}/{fileName}";
            int fileType = GetFileType(formFile.FileName);

            string fileMD5 = GetFileMD5(formFile, fileName, userId);
            var fileData = await _fileRepository.ExistsByMD5(fileMD5);
            if (fileData == null)
            {
                _aliOssService.PutObject(keyName, formFile.OpenReadStream());
            }
           
            var file = new Files()
            {
                Id = Guid.NewGuid().ToString("N"),
                Name = formFile.FileName,
                Length = formFile.Length,
                ContentType = formFile.ContentType,
                UserId = userId,
                BaseUrl = baseUrl,
                Url = fileData == null ? keyName : fileData.Url,
                FileType = fileType,
                CreatedBy = userName,
                UpdatedBy = userName,
                CreatorId = userId,
                ModifierId = userId,
                UpdatedTime = DateTime.Now,
                CreatedTime = DateTime.Now,
                MD5 = fileMD5
            };
            await _fileRepository.AddFile(file);
            return _mapper.Map<FileDto>(file);
        }

        /// <summary>
        /// 获取文件md5
        /// </summary>
        /// <param name="formFile"></param>
        /// <param name="fileName"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        private string GetFileMD5(IFormFile formFile, string fileName, string userId)
        {
            var basePath = AppContext.BaseDirectory;
            basePath = basePath + $"uploadFile/{userId}";
            if (!Directory.Exists(basePath))
            {
                Directory.CreateDirectory(basePath);
            }

            string md5 = "";
            string filePath = $"{basePath}/{fileName}";
            using (FileStream fs = new System.IO.FileStream(filePath, FileMode.Create))
            {
                formFile.CopyTo(fs);
                fs.Flush();
            }

            using (var fs = File.Open(filePath, FileMode.Open))
            {
                md5 = OssUtils.ComputeContentMd5(fs, fs.Length);
                fs.Close();
                fs.Dispose();
            }
            File.Delete(filePath);
            return md5;
        }

        /// <summary>
        /// 添加文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<FileDto> AddUploadFiles(Files file)
        {
            await _fileRepository.AddFile(file);
            return _mapper.Map<FileDto>(file);
        }
    }
}
