﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 微信用户
    /// </summary>
    [SqlSugar.SugarTable("cms_open_user")]
    public class OpenUser : BaseEntity<string>
    {
        /// <summary>
        /// 微信OpenId
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "open_id")]
        public string OpenId { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "phone")]
        public string Phone { get; set; }
    }
}
