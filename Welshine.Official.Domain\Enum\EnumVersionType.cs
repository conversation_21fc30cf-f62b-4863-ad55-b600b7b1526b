﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Welshine.Official.Domain.Enum
{
    /// <summary>
    /// 版本类型
    /// </summary>
    public enum EnumVersionType
    {
        /// <summary>
        /// 首页Banner
        /// </summary>
        [Description("首页Banner")]
        Banner = 0,

        /// <summary>
        /// 首页中部广告
        /// </summary>
        [Description("首页中部广告")]
        Mid = 1,

        /// <summary>
        /// 关于惠而信
        /// </summary>
        [Description("关于惠而信")]
        Welshine = 2,

        /// <summary>
        /// 联系方式
        /// </summary>
        [Description("联系方式")]
        Contacts = 3,

        /// <summary>
        /// 产品介绍(中文)
        /// </summary>
        [Description("产品介绍(中文)")]
        ProductCN = 4,

        /// <summary>
        /// 首页底部横幅
        /// </summary>
        [Description("首页底部横幅")]
        Bottom = 5,

        /// <summary>
        /// 侧边浮层
        /// </summary>
        [Description("侧边浮层")]
        Sidebar = 6, 
    }
}
