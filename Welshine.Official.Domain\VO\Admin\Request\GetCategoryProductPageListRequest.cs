﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 商品列表查询条目
    /// </summary>
    public class GetCategoryProductPageListRequest
    {
        /// <summary>
        /// 语言: 0->英文;1->中文;
        /// </summary>
        [Range(0, 1, ErrorMessage = "语言参数错误")]
        public int? ProductLanguage { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [StringLength(100, ErrorMessage = "产品名称长度不符", MinimumLength = 1)]
        //[RegularExpression(@"^[^\u4e00-\u9fa5]*$", ErrorMessage = "产品名称不允许有中文")]
        public string ProductName { get; set; }

        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        [Range(0, 1, ErrorMessage = "发布状态参数错误")]
        public int? ReleaseStatus { get; set; }

        /// <summary>
        /// 发布时间筛选
        /// </summary>
        public TimeHorizon TimeScope { get; set; }
    }
}
