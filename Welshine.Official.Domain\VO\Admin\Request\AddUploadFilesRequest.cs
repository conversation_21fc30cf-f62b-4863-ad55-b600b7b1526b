﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 新增文件条目
    /// </summary>
    public class AddUploadFilesRequest
    {
        /// <summary>
        /// 基础路径
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 路径
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long Length { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 是否加密
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// 文件Md5值
        /// </summary>
        public string Md5 { get; set; }
    }
}
