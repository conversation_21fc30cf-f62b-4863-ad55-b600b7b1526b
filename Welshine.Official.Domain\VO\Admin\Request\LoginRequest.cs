﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class LoginRequest
    {
        /// <summary>
        /// 账号
        /// </summary>
        [Required(ErrorMessage ="账号为必填")]
        [StringLength(20, ErrorMessage = "账号长度必须为4-20位", MinimumLength = 4)]
        [RegularExpression(@"^[0-9a-z]*$", ErrorMessage = "账号必须为小写字母或数字")]
        public string LoginName { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码为必填")]
        [StringLength(15, ErrorMessage = "密码长度必须为8-15位", MinimumLength = 8)]
        [RegularExpression(@"^(?=.*?[a-zA-Z])(?=.*?\d).*$", ErrorMessage = "密码须存在英文字母（大写或小写）和数字")]
        public string Password { get; set; }
    }
}
