﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    /// <summary>
    /// 修改产品分类入参
    /// </summary>
    public class EditProductCategoryRequest
    {
        /// <summary>
        /// 分类Id
        /// </summary>
        [Required(ErrorMessage = "分类Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "分类Id参数错误")]
        public long? CategoryId { get; set; }

        /// <summary>
        /// 分类产品Id
        /// </summary>
        [Required(ErrorMessage = "分类产品Id必填,请完善")]
        [Range(1, long.MaxValue, ErrorMessage = "分类产品Id参数错误")]
        public long? CategoryProductId { get; set; }

    }
}
