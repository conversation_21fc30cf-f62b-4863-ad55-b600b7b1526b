﻿namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 展会活动参与人员表
    /// </summary>
    [SqlSugar.SugarTable("cms_exhibition_activity_participant")]
    public class ExhibitionActivityParticipant : BaseBigEntity
    {
        /// <summary>
        /// 展会活动id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "exhibition_activity_id")]
        public long ExhibitionActivityId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "name")]
        public string Name { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "mobile")]
        public string Mobile { get; set; }

        /// <summary>
        /// 类型:0->经销;1->终端;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "type")]
        public int Type { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "company")]
        public string Company { get; set; }

        /// <summary>
        /// 省id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "province_id")]
        public long ProvinceId { get; set; }

        /// <summary>
        /// 省名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "province_name")]
        public string ProvinceName { get; set; }

        /// <summary>
        /// 市id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "city_id")]
        public long CityId { get; set; }

        /// <summary>
        /// 市名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "city_name")]
        public string CityName { get; set; }

        /// <summary>
        /// 券码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "code")]
        public string Code { get; set; }

        /// <summary>
        /// 微信openId
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "open_id")]
        public string OpenId { get; set; }

        /// <summary>
        /// 是否现场客户:0->否;1->是;
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "is_scene")]
        public int IsScene { get; set; }

    }
}
