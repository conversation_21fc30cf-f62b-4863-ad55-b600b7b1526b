﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Core.SNGeneration;
using Welshine.Official.Domain;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    public class UserService : IUserService
    {
        IOpenUserRepository _openUserRepository;
        IUserCheckInRepository _userCheckInRepository;
        IAtlasRepository _atlasRespository;
        /// <summary>
        /// 映射
        /// </summary>
        IMapper _mapper;
        public UserService(IOpenUserRepository openUserRepository, 
            IUserCheckInRepository userCheckInRepository,
            IMapper mapper,
            IAtlasRepository atlasRespository)
        {
            _openUserRepository = openUserRepository;
            _userCheckInRepository = userCheckInRepository;
            _mapper = mapper;
            _atlasRespository = atlasRespository;
        }
        /// <summary>
        /// 查询或者插入用户信息
        /// </summary>
        /// <param name="openId"></param>
        /// <returns></returns>
        public async Task<GetOpenIdResponse> GetOrAddOpenId(string openId)
        {
            var po = await _openUserRepository.GetEntity(x => x.OpenId == openId && x.IsDeleted == false);
            if (po == null)
            {
                po=new Domain.Entity.OpenUser();
                po.Id = Guid.NewGuid().ToString("N");
                po.OpenId = openId;
               await _openUserRepository.Add(po);
            }
            return new GetOpenIdResponse()
            {
                OpenId = po.OpenId,
                Phone = po.Phone
            };
        }
        /// <summary>
        /// 授权手机
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="phone"></param>
        /// <returns></returns>
        public async Task UpdateUserPhone(string openId, string phone)
        {
            var po = await _openUserRepository.GetEntity(x => x.OpenId == openId && x.IsDeleted == false);
            if (po == null)
            {
                throw new BusinessException($"OpenId 不存在", ErrorCode.UserNotFound.GetHashCode());
            }
            po.Phone = phone;
            po.UpdatedTime = DateTime.Now;
            await _openUserRepository.Update(po);
        }
        /// <summary>
        /// 用户登记
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetUserCheckInCount(CreatedTimeScope createdTimeScope, string phone)
        {
            Expression<Func<UserRegister, bool>> where = x => x.IsDeleted == false;
            if (createdTimeScope?.From!=null)
            {
                where = where.And(x=>x.CreatedTime>= createdTimeScope.From);
            }
            if (createdTimeScope?.To != null)
            {
                where = where.And(x => x.CreatedTime <= createdTimeScope.To);
            }
            if (!string.IsNullOrWhiteSpace(phone))
            {
                where = where.And(x => x.Phone.Contains(phone));
            }
            return await _userCheckInRepository.CountEntity(where);
        }

        /// <summary>
        /// 登记分页列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="orderBy"></param>
        /// <param name="createdTimeScope"></param>
        /// <returns></returns>
        public async Task<PageRows<UserCheckInResponse>> GetUserCheckInPageList(int pageIndex, int pageSize, 
            string orderBy, CreatedTimeScope createdTimeScope, string phone)
        {
            Expression<Func<UserRegister, bool>> where = x => x.IsDeleted == false;
            if (createdTimeScope?.From != null)
            {
                where = where.And(x => x.CreatedTime >= createdTimeScope.From);
            }
            if (createdTimeScope?.To != null)
            {
                where = where.And(x => x.CreatedTime <= createdTimeScope.To);
            }
            if (!string.IsNullOrWhiteSpace(phone))
            {
                where = where.And(x => x.Phone.Contains(phone));
            }
            var pageRows= await _userCheckInRepository.GetPageList(pageIndex, pageSize, orderBy, where);
            var list = _mapper.Map<List<UserCheckInResponse>>(pageRows.Data);
            if (list.Any())
            {
                
            }
            return new PageRows<UserCheckInResponse>()
            {
                Total = pageRows.Total,
                Data = list
            };
        }

       

        public async Task<bool> UserCheckIn(string openId, string atlasCode)
        {
            var userInfo = await _openUserRepository.GetEntity(x => x.OpenId == openId && x.IsDeleted == false);
            if (userInfo == null)
            {
                throw new BusinessException($"OpenId 不存在", ErrorCode.UserNotFound.GetHashCode());
            }
            //if (string.IsNullOrWhiteSpace(userInfo.Phone))
            //{
            //    throw new BusinessException(ErrorCode.AuthPhoneError.ToDescriptionName(), ErrorCode.AuthPhoneError.GetHashCode());
            //}
            var atlasInfo=await _atlasRespository.GetEntity(x=>x.AtlasCode== atlasCode&&x.IsDeleted==false);
            if (atlasInfo == null)
            {
                throw new BusinessException(ErrorCode.GetAtlasError.ToDescriptionName(), ErrorCode.GetAtlasError.GetHashCode());
            }
            var redisKey = "UserCheckIn:"+DateTime.Now.ToString("yyyyMMdd")+":" + userInfo.OpenId;
            if((await FreeRedisHelper.DefaultInstance.GetAsync<long?>(redisKey)).GetValueOrDefault()>=3)
            {
                throw new BusinessException(ErrorCode.UserCheckInLimit.ToDescriptionName(), ErrorCode.UserCheckInLimit.GetHashCode());
            }
            UserRegister userRegister=new UserRegister();
            userRegister.UpdatedTime = DateTime.Now;
            userRegister.CreatedTime = DateTime.Now;
            userRegister.AtlasCode = atlasInfo.AtlasCode;
            userRegister.AtlasName=atlasInfo.AtlasName;
            //userRegister.Phone = userInfo.Phone;
            userRegister.Code = await CodeHelper.GetCode("TCLY");
           var result= await _userCheckInRepository.AddBigIdentity(userRegister);
            if (result)
            {

                await FreeRedisHelper.DefaultInstance.IncrAsync(redisKey);
                await FreeRedisHelper.DefaultInstance.ExpireAsync(redisKey,3600*24);
            }
            return result;
        }
    }
}
