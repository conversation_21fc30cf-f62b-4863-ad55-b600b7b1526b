﻿using AutoMapper;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.WxApp.Api.Core
{
    internal class AutoMapperServiceProfile : Profile
    {
        /// <summary>
        /// 
        /// </summary>
        public AutoMapperServiceProfile()
        {
            CreateMap<Stores, GetStorePageListReponse>()
             .ForMember(des => des.StoreId, opt => opt.MapFrom(src => src.Id));
            CreateMap<Stores, GetStoreDetailReponse>()
             .ForMember(des => des.StoreId, opt => opt.MapFrom(src => src.Id));
            CreateMap<AddStoreRequest, Stores>();
            CreateMap<EditStoreRequest, Stores>()
                .ForMember(des => des.Id, opt => opt.MapFrom(src => src.StoreId));
            CreateMap<UserRegister, UserCheckInResponse>()
                .ForMember(des => des.UserCheckId, opt => opt.MapFrom(src => src.Id))
                 .ForMember(des => des.UserCheckCode, opt => opt.MapFrom(src => src.Code))
                 .ForMember(des => des.UserCheckPhone, opt => opt.MapFrom(src => src.Phone))
                ;
            CreateMap<Files, FileDto>()
               .ForMember(des => des.FileId, opt => opt.MapFrom(src => src.Id))
               .ForMember(des => des.Url, opt => opt.MapFrom(src => src.BaseUrl + src.Url));
            CreateMap<GetStorePageListReponse, GetAppStorePageListReponse>();
            CreateMap<GetStoreDetailReponse, GetAppStoreDetailReponse>();
        }
     }
}