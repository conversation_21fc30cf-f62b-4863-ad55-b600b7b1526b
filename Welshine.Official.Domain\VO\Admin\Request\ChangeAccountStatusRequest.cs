﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Request
{
    public class ChangeAccountStatusRequest
    {
        /// <summary>
        /// 用户id
        /// </summary>
        [Required(ErrorMessage ="用户id为必填")]
        public long? AccountId { get; set; }
        /// <summary>
        /// 启用禁用状态
        /// </summary>
        [Required(ErrorMessage = "启用禁用状态为必填")]
        public bool? Status { get; set; }
    }
}
