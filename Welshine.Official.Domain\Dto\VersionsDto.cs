﻿using Newtonsoft.Json;
using Welshine.Official.Domain.Entity;

namespace Welshine.Official.Domain.Dto
{
    /// <summary>
    /// 版本数据模型
    /// </summary>
    public class VersionsDto<T> : Versions
    {
        public T Data
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(RData))
                { 
                    return JsonConvert.DeserializeObject<T>(RData);
                }
                return Data;
            }
            set
            {
                RData = JsonConvert.SerializeObject(value);
            }
        }
    }
}
