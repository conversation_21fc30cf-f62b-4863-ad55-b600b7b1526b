﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class LoginResponse
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public long UserId { get; set; }
        /// <summary>
        /// 用户编号
        /// </summary>
        public string UserNo { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public string LoginName { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }
        /// <summary>
        /// 是否第一次登录
        /// </summary>
        public bool IsFirstLogin { get; set; }
        /// <summary>
        /// token
        /// </summary>
        public string Token { get; set; }
        /// <summary>
        /// 失效时间戳
        /// </summary>
        public long Exp { get; set; }
        /// <summary>
        /// 权限菜单
        /// </summary>
        public List<string> RBACMenu { get; set; }
    }
}
