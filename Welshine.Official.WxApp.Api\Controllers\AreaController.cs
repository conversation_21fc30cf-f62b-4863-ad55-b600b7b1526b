﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    public class AreaController : BaseApiController
    {
        IAreaService _areaService;

        public AreaController(IAreaService areaService)
        {
            _areaService = areaService;
        }

        /// <summary>
        /// 获取地区
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<Area>>> GetAreaList()
        {
            List <Area> result = new List<Area>();
            try
            {
                result = await _areaService.getAreaList();
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetAreaList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
    }
}
