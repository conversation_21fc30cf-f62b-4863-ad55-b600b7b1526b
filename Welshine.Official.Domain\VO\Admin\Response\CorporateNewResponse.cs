﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.VO.Admin.Response
{
    public class CorporateNewResponse
    {
        /// <summary>
        /// 企业资讯Id
        /// </summary>
        public long NewId { get; set; }
        /// <summary>
        /// 中文文章标题
        /// </summary>
        public string NewTitle { get; set; }
        /// <summary>
        /// 内容状态: 0->未发布; 1->已发布;
        /// </summary>
        public int ReleaseStatus { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 最后更新人
        /// </summary>
        public string UpdatedBy { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }
        /// <summary>
        /// 发布日期
        /// </summary>
        public DateTime? PublishDate { get; set; }

        /// <summary>
        /// 文章栏目: 0->企业快讯;1->新品上市;
        /// </summary>
        public int NewColumn { get; set; }

        /// <summary>
        /// 热点推荐: 0->不推荐;1->推荐;
        /// </summary>
        public int NewRecommend { get; set; }
    }
}
