using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using CsvHelper;
using CsvHelper.Excel;

namespace Welshine.Official.Core.Extensions
{
    /// <summary>
    /// Excel/CSV 导入帮助类（读取第一张表为行集合）
    /// 依赖 CsvHelper.Excel.Core
    /// </summary>
    public static class ExcelImportHelper
    {
        /// <summary>
        /// 从流中读取第一张表/CSV所有行，返回每行的列数组（空值用空字符串占位）
        /// </summary>
        public static List<string[]> ParseFirstSheetRows(Stream input, string fileName)
        {
            if (input == null) throw new ArgumentNullException(nameof(input));
            var ext = Path.GetExtension(fileName ?? string.Empty).ToLowerInvariant();
            var rows = new List<string[]>();
            input.Position = 0;
            if (ext == ".csv")
            {
                using var reader = new StreamReader(input, Encoding.UTF8, true, 1024, leaveOpen: true);
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    var cols = (line ?? string.Empty).Split(',');
                    rows.Add(cols);
                }
            }
            else
            {
                // 某些版本的 ExcelParser 需要文件路径，这里落地临时文件再解析
                var tempPath = Path.Combine(Path.GetTempPath(), $"ws_{Guid.NewGuid():N}{ext}");
                using (var fs = new FileStream(tempPath, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    input.CopyTo(fs);
                }
                using var csv = new CsvReader(new ExcelParser(tempPath));
                while (csv.Read())
                {
                    var record = csv.Parser?.Record ?? Array.Empty<string>();
                    var values = new string[record.Length];
                    for (int i = 0; i < record.Length; i++)
                    {
                        values[i] = csv.GetField(i) ?? string.Empty;
                    }
                    rows.Add(values);
                }
                try { File.Delete(tempPath); } catch { /* ignore */ }
            }
            return rows;
        }
    }
}

