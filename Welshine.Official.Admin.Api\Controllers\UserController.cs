﻿using CsvHelper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 用户登记
    /// </summary>
    public class UserController : BaseApiController
    {
        IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        /// <summary>
        /// 用户登记列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<UserCheckInResponse>>> GetUserCheckInPageList([FromBody] RequestPageModel<UserCheckInRequest> request)
        {
            PageRows<UserCheckInResponse> pageRows = new PageRows<UserCheckInResponse>() { Data = new System.Collections.Generic.List<UserCheckInResponse>() };
            try
            {

                pageRows = await _userService.GetUserCheckInPageList(request.PageIndex, request.PageSize, request.OrderBy,
                    request.RequestParams.CreatedTimeScope, request.RequestParams.Phone);

                return Success("查询成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetUserCheckInPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }
        /// <summary>
        /// 用户登记导出
        /// </summary>
        /// <param name="req"></param>
        /// <response code="1018">没有数据，请调整查询条件后重新导出</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> UserCheckInExport([FromBody] BaseRequest<UserCheckInRequest> req)
        {

            List<UserCheckInResponse> list = new List<UserCheckInResponse>();
            var baseDirectory = AppContext.BaseDirectory;
            var basePath = "";
            try
            {

                basePath = baseDirectory + $"/{System.Guid.NewGuid().ToString("N")}".Replace('/', Path.DirectorySeparatorChar);//用于生产数据
                var basePathCopy = baseDirectory + $"/图册下载_{DateTime.Now.ToString("yyMMdd")}".Replace('/', Path.DirectorySeparatorChar);//用于压缩

                var count = await _userService.GetUserCheckInCount(req.Body.CreatedTimeScope, req.Body.Phone);
                if (count == 0)
                {
                    return Json(Failure(ErrorCode.NoDataWarn, "没有数据，请调整查询条件后重新导出"));
                }

                //查询和导出
                if (!Directory.Exists(basePath))
                {
                    Directory.CreateDirectory(basePath);
                }

                List<Task<string>> tasks = new List<Task<string>>();
                int pageIndex = 0; //页码 0也就是第一条 
                int pageSize = 5000; //每页条数    

                while (pageIndex * pageSize < count)
                {
                    var task = GetAllExportPath(basePath, pageIndex + 1, req);
                    var path = await task;
                    if (string.IsNullOrWhiteSpace(path)) break;

                    pageIndex++;
                }

                string zipName = $"图册下载_{DateTime.Now.ToString("yyMMdd")}.zip";
                string zipPath = baseDirectory + $"/{zipName}".Replace('/', Path.DirectorySeparatorChar);
                if (Directory.Exists(basePathCopy))
                {
                    Directory.Delete(basePathCopy, true);
                }
                Directory.Move(basePath, basePathCopy);
                ZipUtility zip = new ZipUtility();
                zip.ZipFileFromDirectory(basePathCopy, zipPath, 5);
                Directory.Delete(basePathCopy, true);
                if (Directory.Exists(basePath))
                {
                    Directory.Delete(basePath, true);
                }
                //文件流
                var filepath = zipPath;
                var provider = new FileExtensionContentTypeProvider();
                var fileInfo = new FileInfo(filepath);
                var ext = fileInfo.Extension;
                new FileExtensionContentTypeProvider().Mappings.TryGetValue(ext, out var contentment);
                return File(await System.IO.File.ReadAllBytesAsync(filepath), contentment ?? "application/octet-stream", fileInfo.Name);
            }
            catch (BusinessException ex)
            {
                return Json(Failure(ex.Code, ex.Message, list));
            }
            catch (System.Exception ex)
            {

                Log.Error("UserCheckInExport ex:" + ex.Message + ex.StackTrace);
                return Json(Failure(ErrorCode.SystemError, list));
            }
            finally
            {
                if (!string.IsNullOrWhiteSpace(basePath) &&Directory.Exists(basePath))
                {
                    Directory.Delete(basePath,true);
                }
            }
        }

        /// <summary>
        /// 获取导出csv地址
        /// </summary>
        /// <param name="basePath"></param>
        /// <param name="pageIndex"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        private async Task<string> GetAllExportPath(string basePath, int pageIndex, BaseRequest<UserCheckInRequest> req)
        {
            var pageRows = (await _userService.GetUserCheckInPageList(pageIndex, pageSize: 5000, "id desc", req.Body.CreatedTimeScope, req.Body.Phone)).Data;
            if (!pageRows.Any()) return null;

            foreach (var item in pageRows)
            {
                item.CreatedTime = item.CreatedTime.AddHours(8);
                item.UpdatedTime = item.UpdatedTime?.AddHours(8);
            }

            var csvFilePath = basePath + $"/图册下载_{DateTime.Now.ToString("yyMMdd")}_{pageIndex}.csv".Replace('/', Path.DirectorySeparatorChar);

            await using (var writer = new StreamWriter(csvFilePath, false, Encoding.UTF8))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    csv.WriteHeader<UserCheckInResponse>();
                    await csv.NextRecordAsync();
                    await csv.WriteRecordsAsync(pageRows);
                }
            }

            return csvFilePath;
        }
    }
}
