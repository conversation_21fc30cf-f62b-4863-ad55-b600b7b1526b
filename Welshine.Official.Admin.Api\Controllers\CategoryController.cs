﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 分类管理
    /// </summary>
    public class CategoryController : BaseApiController
    {
        private readonly ICategoryService _categoryService;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="categoryService"></param>
        public CategoryController(ICategoryService categoryService)
        {
            _categoryService = categoryService;
        }

        /// <summary>
        /// 添加分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2222">分类名称(英文)已存在</response>
        /// <response code="2223">上级分类不存在</response>
        /// <response code="2224">不允许新增下级分类</response>
        /// <response code="2243">分类名称(中文)已存在</response>
        /// <response code="2244">非一级分类不允许设置logo</response>
        /// <response code="2245">请上传logo图片</response>
        /// <response code="2250">请上传手机版激活图标</response>
        /// <response code="2251">非一级分类不允许设置手机版激活图标</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddCategory([FromBody] BaseRequest<AddCategoryRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Category category = new Category()
                {
                    NameEN = request.Body.categoryNameEN,
                    NameCN = request.Body.categoryNameCN,
                    LogoImgUrl = request.Body.logoImgUrl,
                    LogoImgUrlActivate = request.Body.logoImgUrlActivate,
                    FatherId = request.Body.parentId.Value,
                    MobileImgUrlActivate= request.Body.mobileImgUrlActivate,
                    Level = 1,
                    CreatorId = user.UserId,
                    CreatedBy = user.UserName.ToString() ?? "system",
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                };

                result = await _categoryService.AddCategory(category);
                return Success("新增成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddAtlas Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 编辑分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2225">分类不存在</response>
        /// <response code="2222">分类名称(英文)已存在</response>
        /// <response code="2243">分类名称(中文)已存在</response>
        /// <response code="2244">非一级分类不允许设置logo</response>
        /// <response code="2245">请上传logo图片</response>
        /// <response code="2250">请上传手机版激活图标</response>
        /// <response code="2251">非一级分类不允许设置手机版激活图标</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditCategory([FromBody] BaseRequest<EditCategoryRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Category category = new Category()
                {
                    Id = request.Body.CategoryId.Value,
                    NameEN = request.Body.categoryNameEN,
                    NameCN = request.Body.categoryNameCN,
                    LogoImgUrl = request.Body.logoImgUrl,
                    LogoImgUrlActivate = request.Body.logoImgUrlActivate,
                    MobileImgUrlActivate = request.Body.mobileImgUrlActivate,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _categoryService.EditCategory(category);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditCategory Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 删除分类
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2225">分类不存在</response>
        /// <response code="2233">分类有下级,不允许删除</response>
        /// <response code="2234">分类下有未删除的商品,不允许删除</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteCategory([FromBody] BaseRequest<CategoryIdRequest> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                Category category = new Category()
                {
                    Id = request.Body.CategoryId.Value,
                    IsDeleted = true,
                    ModifierId = user.UserId,
                    UpdatedBy = user.UserName.ToString() ?? "system",
                    UpdatedTime = DateTime.Now
                };

                result = await _categoryService.DeleteCategory(category);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteCategory Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取分类列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<List<GetCategoryResponse>>> GetCategoryList()
        {
            List<GetCategoryResponse> result = null;
            try
            {
                result = await _categoryService.GetCategoryList();
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategoryList Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 修改分类排序
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2225">分类不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditCategorySort([FromBody] BaseRequest<List<EditCategorySortRequest>> request)
        {
            bool result = false;
            try
            {
                var user = GetUserInfo();

                List<Category> list = new List<Category>();
                foreach (var item in request.Body)
                {
                    Category category = new Category()
                    {
                        Id = item.CategoryId.Value,
                        Sort = item.Sort.Value,
                        ModifierId = user.UserId,
                        UpdatedBy = user.UserName.ToString() ?? "system",
                        UpdatedTime = DateTime.Now
                    };
                    list.Add(category);
                }

                result = await _categoryService.EditCategorySort(list);
                return Success("修改成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditCategorySort Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", false);
            }
        }

        /// <summary>
        /// 获取分类
        /// </summary>
        /// <response code="2225">分类不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<Category>> GetCategory([FromBody] BaseRequest<CategoryIdRequest> request)
        {
            Category result = null;
            try
            {
                result = await _categoryService.GetCategory(request.Body.CategoryId.Value);
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetCategory Error {u}", ex.Message);
                return Failure(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

    }
}
