﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Welshine.Official.Domain.Entity
{
    [SqlSugar.SugarTable("cms_account")]
    public class Account : BaseBigEntity
    {
        [SqlSugar.SugarColumn(ColumnName = "login_name")]
        public string LoginName { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "password")]
        public string Password { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "tel")]
        public string Tel { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "login_time")]
        public DateTime? LoginTime { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "user_no")]
        public string UserNo { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "department_id")]
        public long DepartmentId { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "post_id")]
        public long PostId { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "user_name")]
        public string UserName { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "is_first_login")]
        public bool IsFirstLogin { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "status")]
        public bool status { get; set; }
    }
}
