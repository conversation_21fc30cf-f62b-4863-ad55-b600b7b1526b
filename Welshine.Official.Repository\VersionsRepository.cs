﻿using DocumentFormat.OpenXml.Spreadsheet;
using DTHY.Core.Repository;
using Newtonsoft.Json;
using SqlSugar;
using SqlSugar.IOC;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.Enum;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Admin.SaveData;
using Welshine.Official.Repository.Interface;

namespace Welshine.Official.Repository
{
    /// <summary>
    /// 版本仓储实现
    /// </summary>
    public class VersionsRepository : BaseRepository<Versions>, IVersionsRepository
    {
        /// <summary>
        /// 判断版本标题是否存在
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <param name="title">标题</param>
        /// <returns></returns>
        public async Task<bool> ExistsTitle(int versionType, string title)
        {
             return await DbScoped.SugarScope.Queryable<Versions>().AnyAsync(x => x.VersionType == versionType && x.Title == title && !x.IsDeleted);
        }

        /// <summary>
        /// 判断版本标题是否存在
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <param name="title">标题</param>
        /// <param name="versionId">版本Id</param>
        /// <returns></returns>
        public async Task<bool> ExistsTitle(int versionType, string title, long versionId)
        {
            return await DbScoped.SugarScope.Queryable<Versions>().AnyAsync(x => x.VersionType == versionType && x.Title == title && x.Id != versionId && !x.IsDeleted);
        }

        /// <summary>
        /// 判断是否有已发布的版本
        /// </summary>
        /// <param name="versionType">板块类型</param>
        /// <returns></returns>
        public async Task<bool> ExistsRelease(int versionType)
        {
            return await DbScoped.SugarScope.Queryable<Versions>().AnyAsync(x => x.VersionType == versionType && x.ReleaseStatus == 1 && !x.IsDeleted);
        }

        /// <summary>
        /// 添加版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> AddVersions(Versions versions)
        {
            var id = await DbScoped.SugarScope.Insertable(versions).ExecuteReturnBigIdentityAsync();
            if (id > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <param name="versionsId">版本Id</param>
        /// <returns></returns>
        public async Task<Versions> GetVersions(long versionsId)
        {
            return await DbScoped.SugarScope.Queryable<Versions>().FirstAsync(x => x.Id == versionsId && !x.IsDeleted);
        }

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <param name="versionsId">版本Id</param>
        /// <returns></returns>
        public async Task<Versions> GetVersions(int versionType, long versionsId)
        {
            return await DbScoped.SugarScope.Queryable<Versions>().FirstAsync(x => x.Id == versionsId && x.VersionType == versionType && !x.IsDeleted);
        }

        /// <summary>
        /// 修改版本信息
        /// </summary>
        /// <param name="versions"></param>
        /// <returns></returns>
        public async Task<bool> EditVersions(Versions versions)
        {
            var result = await DbScoped.SugarScope.Updateable(versions).ExecuteCommandAsync();
            if (result > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 根据类型设置所有版本发布状态为未发布
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <returns></returns>
        public async Task<int> ResetReleaseStatus(int versionType)
        {
            return  await DbScoped.SugarScope.Updateable<Versions>()
                .SetColumns(it => it.ReleaseStatus == 0)
                //.SetColumns(it => it.ReleaseTime == null)
                .Where(it => it.VersionType == versionType && it.ReleaseStatus == 1 && !it.IsDeleted).ExecuteCommandAsync();
        }

        /// <summary>
        /// 获取版本列表
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="versionType">版本类型</param>
        /// <param name="title">版本标题</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<VersionsListResponse>> GetVersionsPageList(string userId, int versionType, string title, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;
            PageRows<VersionsListResponse> result = new PageRows<VersionsListResponse>();

            result.Data = await DbScoped.SugarScope.Queryable<Versions>()
                .Where(x => !x.IsDeleted && x.VersionType == versionType)
                .Where(x => (x.ApproverStatus == 0 && x.CreatorId == userId) || x.ApproverStatus > 0 )
                .WhereIF(!string.IsNullOrWhiteSpace(title), x => x.Title.Contains(title))
                .WhereIF(releaseStatus != null, x => x.ReleaseStatus == releaseStatus)
                .WhereIF(approverStatus != null, x => x.ApproverStatus == approverStatus)
                //侧边浮层
                .WhereIF(versionType == 6 && times != null && times.From != null, x => x.ReleaseTime >= times.From)
                .WhereIF(versionType == 6 && times != null && times.To != null, x => x.ReleaseTime <= times.To)
                //关于惠而信
                .WhereIF(versionType == 2 && times != null && times.From != null, x => x.ReleaseTime >= times.From)
                .WhereIF(versionType == 2 && times != null && times.To != null, x => x.ReleaseTime <= times.To)
                //底部横幅
                .WhereIF(versionType == 5 && times != null && times.From != null, x => x.ReleaseTime >= times.From)
                .WhereIF(versionType == 5 && times != null && times.To != null, x => x.ReleaseTime <= times.To)
                //首页横幅
                .WhereIF(versionType == 1 && times != null && times.From != null, x => x.ReleaseTime >= times.From)
                .WhereIF(versionType == 1 && times != null && times.To != null, x => x.ReleaseTime <= times.To)
                //首页Banner
                .WhereIF(versionType == 0 && times != null && times.From != null, x => x.ReleaseTime >= times.From)
                .WhereIF(versionType == 0 && times != null && times.To != null, x => x.ReleaseTime <= times.To)
                //中文产品
                .WhereIF(versionType == 4 && times != null && times.From != null, x => x.ReleaseTime >= times.From)
                .WhereIF(versionType == 4 && times != null && times.To != null, x => x.ReleaseTime <= times.To)

                .OrderByIF(orderFile == "VersionsId" || orderFile == "id", x => x.Id, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "CreatedTime", x=>x.CreatedTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "ApprovalTime", x => x.ApprovalTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "ReleaseTime", x => x.ReleaseTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .Select(x => new VersionsListResponse
                {
                    VersionsId = x.Id,
                    VersionType = x.VersionType,
                    Title = x.Title,
                    Content = x.Content,
                    ReleaseStatus = x.ReleaseStatus,
                    CreatedUserName = x.CreatedBy,
                    SubmitUserName = x.SubmitApprover,
                    SubmitTime = x.SubmitApprovalTime,
                    ApproverUserName = x.Approver,
                    ApproverStatus = x.ApproverStatus,
                    ApprovalTime = x.ApprovalTime,
                    ReleaseTime = x.ReleaseTime ,
                    CreatedTime = x.CreatedTime

                })
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            result.Total = totalNumber;

            return result;
        }

        /// <summary>
        /// 获取所有已发布的版本
        /// </summary>
        /// <returns></returns>
        public async Task<List<VersionsListResponse>> GetAllReleaseVersions()
        {
            List<VersionsListResponse> result = new List<VersionsListResponse>();

            result = await DbScoped.SugarScope.Queryable<Versions>()
                .Where(x => !x.IsDeleted && x.ReleaseStatus == 1 && x.VersionType != 4)
                .OrderBy(x => x.VersionType, OrderByType.Asc)
                .Select(x => new VersionsListResponse
                {
                    VersionsId = x.Id,
                    VersionType = x.VersionType,
                    Title = x.Title,
                    Content = x.Content,
                    RData= x.RData,
                    ReleaseStatus = x.ReleaseStatus,
                    CreatedUserName = x.CreatedBy,
                    SubmitUserName = x.SubmitApprover,
                    SubmitTime = x.SubmitApprovalTime,
                    ApproverUserName = x.Approver,
                    ApproverStatus = x.ApproverStatus,
                    ApprovalTime = x.ApprovalTime,
                    ReleaseTime = x.ReleaseTime

                })
                .ToListAsync();

            return result;
        }

        /// <summary>
        /// 获取版本列表(直营门店)
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="storeName">门店名称</param>
        /// <param name="releaseStatus">内容状态</param>
        /// <param name="approverStatus">审核状态</param>
        /// <param name="times">发布时间</param>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<PageRows<GetContactsPageListResponse>> GetContactsPageList(string userId, string storeName, int? releaseStatus, int? approverStatus, TimeHorizon times, string orderFile, SortType sortType, int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;

            PageRows<Versions> versionsResult = new PageRows<Versions>();
            versionsResult.Data = await DbScoped.SugarScope.Queryable<Versions>()
                .Where(x => !x.IsDeleted && x.VersionType == 3)
                .WhereIF(!string.IsNullOrWhiteSpace(userId), x => (x.ApproverStatus == 0 && x.CreatorId == userId) || x.ApproverStatus > 0)
                .WhereIF(!string.IsNullOrWhiteSpace(storeName), x => x.Title.Contains(storeName))
                .WhereIF(releaseStatus != null, x => x.ReleaseStatus == releaseStatus)
                .WhereIF(approverStatus != null, x => x.ApproverStatus == approverStatus)
                .WhereIF(times != null && times.From != null, x => x.ReleaseTime >= times.From)
                .WhereIF(times != null && times.To != null, x => x.ReleaseTime <= times.To)
                .OrderByIF(orderFile == "ContactsId" || orderFile == "id", x => x.Id, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "CreatedTime", x => x.CreatedTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "ApprovalTime", x => x.ApprovalTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(orderFile == "ReleaseTime", x => x.ReleaseTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            PageRows<GetContactsPageListResponse> result = new PageRows<GetContactsPageListResponse>();
            result.Total = totalNumber;

            result.Data = new List<GetContactsPageListResponse>();
            foreach (var item in versionsResult.Data)
            {
                AddContactsVersionsRequest cvi = JsonConvert.DeserializeObject<AddContactsVersionsRequest>(item.RData);

                GetContactsPageListResponse cplr = new GetContactsPageListResponse()
                {
                    ContactsId = item.Id,
                    ReleaseStatus = item.ReleaseStatus,
                    ApproverUserName = item.Approver,
                    ApproverStatus = item.ApproverStatus,
                    ApprovalTime = item.ApprovalTime,
                    ReleaseTime = item.ReleaseTime,
                    CreatedName = item.CreatedBy,
                    CreatedTime = item.CreatedTime,
                    SubmitTime = item.SubmitApprovalTime,

                    StoreName = cvi.CNInfo.StoreName,
                    StoreAddress = cvi.CNInfo.StoreAddress,
                    StoreStartTime = cvi.CNInfo.StoreStartTime,
                    StoreEndTime = cvi.CNInfo.StoreEndTime,
                    Phone = cvi.CNInfo.StorePhoneList[0]
                };
                result.Data.Add(cplr);
            }

            return result;
        }

        /// <summary>
        /// 获取版本列表(直营门店)
        /// </summary>
        /// <param name="pageIndex">页码数</param>
        /// <param name="pageSize">每页显示数</param>
        /// <returns></returns>
        public async Task<GetWebContactsPageListResponse> GetWebContactsPageList(int pageIndex = 1, int pageSize = 10)
        {
            RefAsync<int> totalNumber = 0;

            PageRows<Versions> versionsResult = new PageRows<Versions>();
            versionsResult.Data = await DbScoped.SugarScope.Queryable<Versions>()
                .Where(x => !x.IsDeleted && x.VersionType == 3 && x.ReleaseStatus == 1)
                //.WhereIF(!string.IsNullOrWhiteSpace(storeName), x => x.Title.Contains(storeName))
                //.WhereIF(releaseStatus != null, x => x.ReleaseStatus == releaseStatus)
                //.WhereIF(approverStatus != null, x => x.ApproverStatus == approverStatus)
                //.WhereIF(times != null && times.From != null, x => x.ReleaseTime >= times.From)
                //.WhereIF(times != null && times.To != null, x => x.ReleaseTime <= times.To)
                //.OrderByIF(orderFile == "ContactsId" || orderFile == "id", x => x.Id, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                //.OrderByIF(orderFile == "CreatedTime", x => x.CreatedTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                //.OrderByIF(orderFile == "ApprovalTime", x => x.ApprovalTime, sortType == SortType.Asc ? OrderByType.Asc : OrderByType.Desc)
                .OrderByIF(true, x => x.ReleaseTime, OrderByType.Desc)
                .ToPageListAsync(pageIndex, pageSize, totalNumber);

            GetWebContactsPageListResponse result = new GetWebContactsPageListResponse();
            result.Total = totalNumber;

            List<ContactsVersionsItem> CNList = new List<ContactsVersionsItem>();
            List<ContactsVersionsItem> ENList = new List<ContactsVersionsItem>();
            foreach (var item in versionsResult.Data)
            {
                ContactsData cvi = JsonConvert.DeserializeObject<ContactsData>(item.RData);

                CNList.Add(cvi.CNInfo);
                ENList.Add(cvi.ENInfo);
            }

            result.CNList = CNList;
            result.ENList = ENList;

            return result;
        }

        /// <summary>
        /// 获取已发布中文产品
        /// </summary>
        /// <returns></returns>
        public async Task<Versions> GetProductCN()
        { 
            return await DbScoped.SugarScope.Queryable<Versions>().FirstAsync(x => x.VersionType == EnumVersionType.ProductCN.GetHashCode() && x.ReleaseStatus == 1 && !x.IsDeleted);
        }

        /// <summary>
        /// 获取当前已发布版本数据
        /// </summary>
        /// <param name="versionType">版本类型</param>
        /// <returns></returns>
        public async Task<int> GetReleaseCount(int versionType)
        {
            return await DbScoped.SugarScope.Queryable<Versions>().CountAsync(x => x.VersionType == versionType && x.ReleaseStatus == 1 && !x.IsDeleted);
        }
    }
}
