﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Threading.Tasks;
using Welshine.Official.Admin.Api.Core;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.Admin.Request;
using Welshine.Official.Domain.VO;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;
using Welshine.Official.Core.Extensions;
using System.Collections.Generic;
using Welshine.Official.Domain.VO.Admin.Response;
using Welshine.Official.Domain.VO.Request;
using System;
using SqlSugar;

namespace Welshine.Official.Admin.Api.Controllers
{
    /// <summary>
    /// 展会活动
    /// </summary>
    public class ExhibitionActivityController : BaseApiController
    {
        IExhibitionActivityService _exhibitionActivityService;

        public ExhibitionActivityController(IExhibitionActivityService exhibitionActivityService)
        {
            _exhibitionActivityService = exhibitionActivityService;
        }

        /// <summary>
        /// 添加展会活动
        /// </summary>
        /// <param name="request"></param>
        /// <response code="1016">文件Id不存在</response>
        /// <response code="2202">文件格式错误</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddExhibitionActivity([FromBody] BaseRequest<AddExhibitionActivityRequest> request)
        {
            bool result = false;
            try
            {
                var userInfo = GetUserInfo();
                var userId = userInfo.UserId;
                var userName = userInfo.UserName;
                result = await _exhibitionActivityService.AddExhibitionActivity(request.Body.ExhibitionActivityName, request.Body.ExhibitionActivityStartTime, request.Body.ExhibitionActivityEndTime, request.Body.ExhibitionActivityAddress, request.Body.AddressLongitude, request.Body.AddressLatitude, request.Body.CodePrefix, request.Body.EntryPicture, request.Body.ParticipationPicture, request.Body.CodePicture, userName, userId);
                return Success("添加成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddExhibitionActivity " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 编辑展会活动
        /// </summary>
        /// <param name="request"></param>
        /// <response code="1016">文件Id不存在</response>
        /// <response code="2202">文件格式错误</response>
        /// <response code="2252">展会活动不存在</response>
        /// <response code="2253">展会活动未停用,不允许编辑</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditExhibitionActivity([FromBody] BaseRequest<EditExhibitionActivityRequest> request)
        {
            bool result = false;
            try
            {
                var userInfo = GetUserInfo();
                var userId = userInfo.UserId;
                var userName = userInfo.UserName;
                result = await _exhibitionActivityService.EditExhibitionActivity(request.Body.ExhibitionActivityId, request.Body.ExhibitionActivityName, request.Body.ExhibitionActivityStartTime, request.Body.ExhibitionActivityEndTime, request.Body.ExhibitionActivityAddress, request.Body.AddressLongitude, request.Body.AddressLatitude, request.Body.EntryPicture, request.Body.ParticipationPicture, request.Body.CodePicture, userName, userId);
                return Success("编辑成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditExhibitionActivity " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 展会活动列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<PageRows<ExhibitionActivityResponse>>> GetExhibitionActivityPageList([FromBody] RequestPageModel<GetExhibitionActivityPageListRequest> request)
        {
            PageRows<ExhibitionActivityResponse> pageRows = new PageRows<ExhibitionActivityResponse>() { Data = new System.Collections.Generic.List<ExhibitionActivityResponse>() };
            try
            {
                DateTime? dt1 = request.RequestParams.ActivityTimeScope == null ? null : request.RequestParams.ActivityTimeScope.From;
                DateTime? dt2 = request.RequestParams.ActivityTimeScope == null ? null : request.RequestParams.ActivityTimeScope.To;
                var orderType = OrderByType.Desc;
                if (request.SortType == SortType.Asc)
                {
                    orderType = OrderByType.Asc;
                }
                var pageRowList = await _exhibitionActivityService.GetExhibitionActivityPageList(request.PageIndex, request.PageSize, request.OrderFile, orderType, request.RequestParams.ExhibitionActivityName, dt1, dt2, request.RequestParams.Status);
                pageRows.Total = pageRowList.Total;
                pageRows.Data = _mapper.Map<List<ExhibitionActivityResponse>>(pageRowList.Data);

                return Success("查询成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionActivityPageList " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), pageRows);
            }
        }

        /// <summary>
        /// 编辑展会活动状态
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2252">展会活动不存在</response>
        /// <response code="2254">当前活动非停用状态，无法启用</response>
        /// <response code="2255">当前活动非启用状态，无法停用</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> EditExhibitionActivityStatus([FromBody] BaseRequest<EditExhibitionActivityStatus> request)
        {
            bool result = false;
            try
            {
                var userInfo = GetUserInfo();
                var userId = userInfo.UserId;
                var userName = userInfo.UserName;
                result = await _exhibitionActivityService.EditExhibitionActivityStatus(request.Body.ExhibitionActivityId, request.Body.Status, userName, userId);
                return Success("编辑成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("EditExhibitionActivityStatus " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 删除展会活动
        /// </summary>
        /// <param name="request"></param>
        /// <response code="2252">展会活动不存在</response>
        /// <response code="2256">当前活动非停用状态，无法删除</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteExhibitionActivity([FromBody] BaseRequest<ExhibitionActivityIdRequest> request)
        {
            bool result = false;
            try
            {
                var userInfo = GetUserInfo();
                var userId = userInfo.UserId;
                var userName = userInfo.UserName;
                result = await _exhibitionActivityService.DeleteExhibitionActivity(request.Body.ExhibitionActivityId, userName, userId);
                return Success("删除成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteExhibitionActivity " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

        /// <summary>
        /// 展会活动详情
        /// </summary>
        /// <response code="2252">展会活动不存在</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ExhibitionActivityDetailResponse>> GetExhibitionActivityDetail([FromBody] BaseRequest<ExhibitionActivityIdRequest> request)
        {
            ExhibitionActivityDetailResponse result = null;
            try
            {
                result = await _exhibitionActivityService.GetExhibitionActivityDetail(request.Body.ExhibitionActivityId);
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetExhibitionActivityDetail " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }

    }
}
